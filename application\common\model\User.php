<?php

namespace app\common\model;

use think\Db;
use think\Model;
use app\common\model\service\BmLog;
use traits\model\SoftDelete;

/**
 * 会员模型
 */
class User extends Model
{

    use SoftDelete;
    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';
    // 追加属性
    protected $append = [
        'url',
    ];

    /**
     * 获取个人URL
     * @param string $value
     * @param array  $data
     * @return string
     */
    public function getUrlAttr($value, $data)
    {
        return "/u/" . $data['id'];
    }
    // 生成邀请码
    public static function generateCode()
    {
        // 随机字符串，可以根据需要调整长度和字符
        $code = substr(md5(uniqid(rand())), 0, 6);
        // 检查邀请码是否已存在
        $count = self::where('invit_code', $code)->count();
        if ($count > 0) {
            // 如果存在，则递归生成新的邀请码
            return self::generateCode();
        }
        return $code;
    }

    // 递归查询下级代理
    public static function getSubAgents($parentId = null, $searchName = '',$is_sqdl='')
    {
        $where = [];
        if($is_sqdl==1){
            $where['is_sqdl'] = 1;
        }elseif($is_sqdl==2){
            $where['is_ylgw'] = 1;
        }
        $query = self::where('parent_id', $parentId)->where($where);
        if (!empty($searchName)) {
            $query->where('nickname', 'like', '%' . $searchName . '%');
        }
        $agents = $query->field('id,nickname,avatar,mobile,createtime')->select();
        $subUsers = [];
        foreach ($agents as &$agent) {
            $agent['createtime'] = date("Y-m-d H:i", $agent['createtime']);
            $subUsers[] = $agent;
            $subAgents = self::getSubAgents($agent['id'], $searchName,$is_sqdl);
            if (!empty($subAgents)) {
                // $agent['sub_agents'] = $subAgents;
                $subUsers = array_merge($subUsers, $subAgents);
            }
        }
        return $subUsers;
    }

    /**
     * 获取头像
     * @param string $value
     * @param array  $data
     * @return string
     */
    public function getAvatarAttr($value, $data)
    {
        if (!$value) {
            //如果不需要启用首字母头像，请使用
            $value = '/assets/img/avatar.png';
            // $value = letter_avatar($data['nickname']);
        }
        return $value;
    }
    /**
     * 获取头像
     * @param string $value
     * @param array  $data
     * @return string
     */
    public function getBirthdayAttr($value, $data)
    {
        if (!$value) {
            //如果不需要启用首字母头像，请使用
            $value = '';
            // $value = letter_avatar($data['nickname']);
        }
        return $value;
    }

    /**
     * 获取会员的组别
     */
    public function getGroupAttr($value, $data)
    {
        return UserGroup::get($data['group_id']);
    }

    /**
     * 获取验证字段数组值
     * @param string $value
     * @param array  $data
     * @return  object
     */
    public function getVerificationAttr($value, $data)
    {
        $value = array_filter((array)json_decode($value, true));
        $value = array_merge(['email' => 0, 'mobile' => 0], $value);
        return (object)$value;
    }

    /**
     * 设置验证字段
     * @param mixed $value
     * @return string
     */
    public function setVerificationAttr($value)
    {
        $value = is_object($value) || is_array($value) ? json_encode($value) : $value;
        return $value;
    }
    /**
     * 查询此用户最近的养老院长（不包括自己）
     * <AUTHOR>
     * @date 2024/9/5  下午5:46
     * @notes
     */
    public static function getOneSq($user_id)
    {
        $user = self::where(['id'=>$user_id])->field('id,parent_id,is_sqdl')->find();
        if(empty($user) || $user['parent_id'] == 0){
            return [];
        }

        // 从上级开始查找，不包括自己
        return self::findSqInParents($user['parent_id']);
    }

    /**
     * 递归查找上级中的养老院长
     */
    private static function findSqInParents($user_id)
    {
        if($user_id == 0) {
            return [];
        }

        $user = self::where(['id'=>$user_id])->field('id,parent_id,is_sqdl')->find();
        if(empty($user)){
            return [];
        }

        if($user['is_sqdl'] == 1){
            return $user;
        }else{
            return self::findSqInParents($user['parent_id']);
        }
    }

    /**
     * 商城发放奖励
     * <AUTHOR>
     * @date 2024/10/19  下午5:16
     * @notes
     */
    // public function sendShopMoney($user_id,$order_id)
    // {
    //     //获取所属养老院长
    //     $sq_user = self::getOneSq($user_id);
    //     if($sq_user){
    //
    //     }
    //     $user = self::where(['id'=>$user_id])->field('id,parent_id,is_sqdl,is_qydl')->find();
    //
    // }


    /**
     * 查询此用户最近的城市运营商（不包括自己）
     */
    public static function getOneQy($user_id)
    {
        $user = self::where(['id'=>$user_id])->field('id,parent_id,is_qydl')->find();
        if(empty($user) || $user['parent_id'] == 0){
            return [];
        }

        // 从上级开始查找，不包括自己
        return self::findQyInParents($user['parent_id']);
    }

    /**
     * 递归查找上级中的城市运营商
     */
    private static function findQyInParents($user_id)
    {
        if($user_id == 0) {
            return [];
        }

        $user = self::where(['id'=>$user_id])->field('id,parent_id,is_qydl')->find();
        if(empty($user)){
            return [];
        }

        if($user['is_qydl'] == 1){
            return $user;
        }else{
            return self::findQyInParents($user['parent_id']);
        }
    }

    /**
     * 查询此用户最近的养老顾问（不包括自己）
     * <AUTHOR>
     * @date 2024/12/20  下午5:46
     * @notes
     */
    public static function getOneYlgw($user_id)
    {
        $user = self::where(['id'=>$user_id])->field('id,parent_id,is_ylgw')->find();
        if(empty($user) || $user['parent_id'] == 0){
            return [];
        }

        // 从上级开始查找，不包括自己
        return self::findYlgwInParents($user['parent_id']);
    }

    /**
     * 递归查找上级中的养老顾问
     */
    private static function findYlgwInParents($user_id)
    {
        if($user_id == 0) {
            return [];
        }

        $user = self::where(['id'=>$user_id])->field('id,parent_id,is_ylgw')->find();
        if(empty($user)){
            return [];
        }

        if($user['is_ylgw'] == 1){
            return $user;
        }else{
            return self::findYlgwInParents($user['parent_id']);
        }
    }

    /**
     * 变更会员余额
     * @param int    $money   余额
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function money($money, $user_id, $memo,$service_type=0,$service_ids="",$type="")
    {
        Db::startTrans();
        try {
            $user = self::lock(true)->find($user_id);
            if ($user && $money != 0) {
                $before = $user->money;
                //$after = $user->money + $money;
                $after = function_exists('bcadd') ? bcadd($user->money, $money, 2) : $user->money + $money;
                //更新会员信息
                $user->save(['money' => $after]);
                //写入日志
                MoneyLog::create(['user_id' => $user_id, 'money' => $money, 'before' => $before, 'after' => $after, 'memo' => $memo,'service_type'=>$service_type,'service_ids'=>$service_ids,'type'=>$type]);

                // 如果是养老顾问佣金，更新统计字段
                if ($type == 'fenyong' && $money > 0 && $user->is_ylgw == 1) {
                    $user->ylgw_total_commission = bcadd($user->ylgw_total_commission ?: 0, $money, 2);
                    $user->save(['ylgw_total_commission' => $user->ylgw_total_commission]);
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
        }
    }

    /**
     * 变更会员线下余额
     * @param int    $money   余额
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function xianxiamoney($money, $user_id, $memo,$service_type=0,$service_ids="",$type="")
    {
        Db::startTrans();
        try {
            $user = self::lock(true)->find($user_id);
            if ($user && $money != 0) {
                $before = $user->ylgw_total_commission;
                //$after = $user->money + $money;
                $after = function_exists('bcadd') ? bcadd($user->ylgw_total_commission, $money, 2) : $user->money + $money;
                if ($type == 'fenyong' && $money > 0 && $user->is_ylgw == 1) {
                    $user->ylgw_total_commission = bcadd($user->ylgw_total_commission ?: 0, $money, 2);
                    $user->save(['ylgw_total_commission' => $user->ylgw_total_commission]);
                }
                //写入日志
                MoneyLog::create(['user_id' => $user_id, 'money' => $money, 'before' => $before, 'after' => $after, 'memo' => $memo,'service_type'=>$service_type,'service_ids'=>$service_ids,'type'=>$type]);

                // 如果是养老顾问佣金，更新统计字段
                
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
        }
    }

    /**
     * 变更会员解锁保姆次数
     * @param int    $money   余额
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function bm($bm_num, $user_id, $memo,$bm_id=0)
    {
        Db::startTrans();
        try {
            $user = self::lock(true)->find($user_id);
            if ($user && $bm_num != 0) {
                $before = $user->bm_num;
                //$after = $user->money + $money;
                $after = function_exists('bcadd') ? bcadd($user->bm_num, $bm_num, 2) : $user->bm_num + $bm_num;
                //更新会员信息
                $user->save(['bm_num' => $after]);
                //写入日志
                BmLog::create(['user_id' => $user_id, 'bm_num' => $bm_num, 'before' => $before, 'after' => $after, 'memo' => $memo,'bm_id'=>$bm_id]);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
        }
    }

    // 养老院长递归查询下级用户
    public static function getAllNonAgentDescendants($parentId = 0)
    {
        $result = [];
        // 查询当前parentId下的所有用户（包括代理和非代理）
        $users = self::where('parent_id', $parentId)->field('id, parent_id, is_sqdl')->select();
        foreach ($users as $user) {
            // 检查这个用户是否是代理
            if ($user['is_sqdl'] != 1) {
                // 如果不是代理，则将其添加到结果中
                $result[] = $user['id'];
                // 递归查询这个非代理用户的下级（排除代理及其下级）
                $result = array_merge($result, self::getAllNonAgentDescendants($user['id']));
            }
            // 如果是代理，则忽略它，不添加到结果中，也不递归查询其下级
        }
        return $result;
    }

    // 城市运营商查询下级所有用户
    public static function getAllNonAgentDescendantsQy($parentId = 0)
    {
        $result = [];
        // 查询当前parentId下的所有用户（包括代理和非代理）
        $users = self::where('parent_id', $parentId)->field('id, parent_id, is_qydl')->select();
        foreach ($users as $user) {
            // 检查这个用户是否是代理
            if ($user['is_qydl'] != 1) {
                // 如果不是代理，则将其添加到结果中
                $result[] = $user['id'];
                // 递归查询这个非代理用户的下级（排除代理及其下级）
                $result = array_merge($result, self::getAllNonAgentDescendantsQy($user['id']));
            }
            // 如果是代理，则忽略它，不添加到结果中，也不递归查询其下级
        }
        return $result;
    }

    // 养老顾问查询下级所有用户
    public static function getAllNonAgentDescendantsYlgw($parentId = 0)
    {
        $result = [];
        // 查询当前parentId下的所有用户（包括养老顾问和非养老顾问）
        $users = self::where('parent_id', $parentId)->field('id, parent_id, is_ylgw, is_sqdl, is_qydl')->select();
        foreach ($users as $user) {
            // 检查这个用户是否是更高级别的代理（养老院长或城市运营商）
            if ($user['is_sqdl'] != 1 && $user['is_qydl'] != 1) {
                // 如果不是更高级别代理，则将其添加到结果中
                $result[] = $user['id'];
                // 递归查询这个用户的下级（排除更高级别代理及其下级）
                $result = array_merge($result, self::getAllNonAgentDescendantsYlgw($user['id']));
            }
            // 如果是更高级别代理，则忽略它，不添加到结果中，也不递归查询其下级
        }
        return $result;
    }

    //养老顾问查询下级所有用户



    // 递归查询下级用户
    public static function getUserChilds($parentId = 0,$is_qydl=false)
    {
        $result = [];
        // 查询当前parentId下的所有用户（包括代理和非代理）
        $users = self::where('parent_id', $parentId)->field('id, parent_id, is_sqdl,is_qydl')->select();
        foreach ($users as $user) {
            if($is_qydl == true){
                // 检查这个用户是否是代理
                if ($user['is_qydl'] != 1 && $user['is_sqdl'] == 1) {
                    // 如果不是代理，则将其添加到结果中
                    $result[] = $user['id'];
                    // 递归查询这个非代理用户的下级（排除代理及其下级）
                    $result = array_merge($result, self::getUserChilds($user['id'],$is_qydl));
                }
            }else{
                // 检查这个用户是否是代理
                if ($user['is_sqdl'] != 1 ) {
                    // 如果不是代理，则将其添加到结果中
                    $result[] = $user['id'];
                    // 递归查询这个非代理用户的下级（排除代理及其下级）
                    $result = array_merge($result, self::getUserChilds($user['id'],$is_qydl));
                }
            }

            // 如果是代理，则忽略它，不添加到结果中，也不递归查询其下级
        }
        return $result;
    }

    /**
     * 变更会员积分
     * @param int    $score   积分
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function score($score, $user_id, $memo)
    {
        Db::startTrans();
        try {
            $user = self::lock(true)->find($user_id);
            if ($user && $score != 0) {
                $before = $user->score;
                $after = $user->score + $score;
                $level = self::nextlevel($after);
                //更新会员信息
                $user->save(['score' => $after, 'level' => $level]);
                //写入日志
                ScoreLog::create(['user_id' => $user_id, 'score' => $score, 'before' => $before, 'after' => $after, 'memo' => $memo]);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
        }
    }

    /**
     * 根据积分获取等级
     * @param int $score 积分
     * @return int
     */
    public static function nextlevel($score = 0)
    {
        $lv = array(1 => 0, 2 => 30, 3 => 100, 4 => 500, 5 => 1000, 6 => 2000, 7 => 3000, 8 => 5000, 9 => 8000, 10 => 10000);
        $level = 1;
        foreach ($lv as $key => $value) {
            if ($score >= $value) {
                $level = $key;
            }
        }
        return $level;
    }

    public static function getSimpleUser($id)
    {
        $user = self::where(['id'=>$id])->field('id,avatar,nickname,mobile')->find();
        if(empty($user)){
            return null;
        }
        return $user;
    }
}
