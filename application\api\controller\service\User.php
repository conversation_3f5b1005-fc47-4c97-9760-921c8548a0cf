<?php

namespace app\api\controller\service;

use app\admin\model\wanlshop\CouponReceive;
use app\api\model\service\MoneyLog;
use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use addons\service\library\WxPay;
use app\api\model\service\UserInfo;
use app\api\model\service\Order;
use app\api\model\service\ProjectConfig;
use app\common\model\UserArea;
use fast\Http;
use think\Db;
use fast\Random;
use think\Config;
use think\Cache;
use think\Exception;
use think\Validate;
class User extends Api
{
    protected $noNeedLogin = ['login', 'logout', 'mobilelogin', 'register', 'resetpwd', 'getNearAddress','changeemail', 'changemobile', 'third', 'phone', 'perfect','cs','userLogin','getLocation','getarea','cities'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }

    }

    /**
     * 用户足迹
     * <AUTHOR>
     * @date 2024/9/23  上午10:58
     * @notes
     */
    public function userFooterView()
    {
        $list = model('app\common\model\service\FooterView')
            ->where('user_id',$this->auth->id)
            ->order('view_time','desc')
            ->paginate()->each(function ($row){
                $row->goods;
            });
        $this->success('信息返回成功',$list);
    }


    /**
     * 城市运营商开通养老院长
     * <AUTHOR>
     * @date 2024/9/20  下午5:28
     * @notes
     */
    public function agentOrder()
    {
        $mobile = $this->request->param('mobile');
        $code = $this->request->param('code');
        $paytype = $this->request->param('paytype');
        $addr = $this->request->param('addr');
        (!$mobile || !$code) && $this->error('参数缺失');
        (!Sms::check($mobile, $code, 'agent')) && $this->error('验证码不正确');
        $user = model('app\common\model\User')->where('mobile',$mobile)->where('is_service',0)->find();
        if(empty($user)) $this->error('用户不存在');
        if(isset($user['is_sqdl']) && $user['is_sqdl'] == 1){
            $this->error('此用户已是养老院长，请勿重复开通');
        }
        $uid = $this->auth->id;
        //获取此城市运营商代理区域
        $quyu = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->find();
        $userInfo=$this->auth->getUserInfo();
        if($userInfo['is_qydl']!=1)  $this->error('您还不是城市运营商');
        $addr= $addr ?? $userInfo->addr;
        $money = config('site.agent_money');
        Db::startTrans();
        try{
            $price = $money;
            $price <=0 && $this->error('金额错误');
            $orderId = 'Agentorder'.Random::alnum(5).'-'.$uid.'-'.time();
            $Order = new \app\common\model\AgentOrder(['area_user_id'=>$uid,'kt_mobile'=>$mobile,'price'=>$price,'orderid'=>$orderId,'paytype'=>$paytype,'addr'=>$addr]);
            $Order->allowField(true)->save();
            if($paytype == 4){//余额支付
//                if($money < $price){
//                    throw new Exception("余额不足");
//                }
//                \app\common\model\User::money(-$price,$uid,'开通养老院长');
//                db('service_agent_order')->where('orderid',$orderId)->update(['status'=>'paid','paytime'=>time()]);
//                $re = 1;
//                $user->is_sqdl=1;
//                $user->addr=$addr;
//                $address = explode('/',$addr);
//                $user->province = $address[0];
//                $user->city  = $address[1];
//                $user->district = $address[2]??'';
//                // 修复：升级为代理时不应该将上级设置为自己
//                if($user->parent_id == 0 || $user->parent_id == $this->auth->id) {
//                    $user->parent_id = 0; // 成为顶级代理
//                }
                $user->save();
            }else{
                $re = \addons\service\library\Pay::payOrder(['amount'=>$price,'orderid'=>$orderId,'title'=>'开通养老院长'],$paytype,$uid,0);
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('支付失败',$e->getMessage());
        }
        $this->success('支付信息返回成功',$re);
    }

    /**
     * 城市运营商获取代理列表
     * <AUTHOR>
     * @date 2024/9/18  下午4:31
     * @notes
     */
    public function getAgent()
    {
        $keywords = $this->request->param('keywords');
        //为1时查询is_sqdl 为2时查询ls_ylgw
        $type = $this->request->param('type',1);



        // $where = [];
        // if($keywords){
        //     $where[] = ['nickname','like','%'.$keywords.'%'];
        // }
        // //获取此城市运营商代理区域
        // $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
        // if(!$district){
        //     $this->error('您还不是城市运营商');
        // }
        // $list = model('app\api\model\service\User')->where('is_sqdl',1)->where('district',$district)
        //     ->where($where)
        //     ->field('id,mobile,nickname,avatar,createtime')
        //     ->paginate()->each(function ($item){
        //         $item['createtime'] = date('Y-m-d H:i:s',$item['createtime']);
        //         return $item;
        //     });
        // $this->success('信息返回成功',$list);
        $list = \app\common\model\User::getSubAgents($this->auth->id,$keywords,$type);
        $this->success('信息返回成功',['count'=>count($list),'list'=>$list]);

    }

    /**
     * 城市运营商查看下级用户
     * <AUTHOR>
     * @date 2024/10/31  下午3:21
     * @notes
     */
    public function areaUserList()
    {

    }

    /**
     * 代理详情
     * <AUTHOR>
     * @date 2024/10/30  下午4:28
     * @notes
     */
    public function getAgentDetails()
    {
        $user_id = $this->request->post('user_id');
        (!$user_id) && $this->error('参数缺失');
        $user = model('app\common\model\User')->where('id',$user_id)->field('id,nickname,avatar,mobile,birthday,createtime')->find();
        //获取下级所有用户
        $children = \app\common\model\User::getSubAgents($user_id,'',false);
        $user_ids = array_column($children,'id');
        //查询订单总数
        $goods_count = model('app\api\model\wanlshop\Order')->where('user_id','in',$user_ids)->where('state','not in',[1,7])->count();
        //服务
        $service_count = model('app\api\model\service\Order')->where('user_id','in',$user_ids)->where('status','not in',[0,-1])->count();

        //查询订单总金额
        $goods_price = model('app\api\model\wanlshop\Order')->alias('order')
            ->join('wanlshop_order_goods goods','goods.order_id=order.id','left')
            ->where('order.user_id','in',$user_ids)->where('order.state','not in',[1,7])->sum('price');
        $service_price = model('app\api\model\service\Order')->where('user_id','in',$user_ids)->where('status','not in',[0,-1])->sum('price');

        //代理总佣金
        $total_price = MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->sum('money');
        $user['order_num'] = $goods_count+$service_count;
        $user['order_price'] = $goods_price+$service_price;
        $user['total_price'] = $total_price;
        $user['children'] = $children;
        $user['children_count'] = count($children);
        $this->success('信息返回成功',$user);
    }




    /**
     * 高德地图获取城市地址信息
     * <AUTHOR>
     * @date 2024/8/20  下午5:27
     * @notes
     */
    public function getNearAddress()
    {
        $name = $this->request->param('name');//关键字
        $city = $this->request->param('city');//城市名
        (!$name || !$city) && $this->error('参数缺失');
        $re = \addons\service\library\Common::getAreaList(['city'=>$city,'name'=>$name]);
        $re['status'] == 0 && $this->error('请求失败');
        $this->success('信息返回',$re['pois']);
    }

    /**
     * 获取系统区域信息
     * <AUTHOR>
     * @date 2024/8/22  下午3:20
     * @notes
     */
    public function getSysArea()
    {
        //获取当前id
        $city = input('city');
        $pid = db('area')->where('name',$city)->where('level',2)->value('id');
        $list = db('area')->where(['pid'=>$pid])->field('id,name,first,lng,lat,pinyin')->select();
        $this->success('信息返回成功',$list);
    }


    public function getArea()
    {
        $data['openCity'] = \app\api\model\service\CityConfig::field('id,city')->order('weigh desc')->select();
        $data['areaList'] = db('area')->where(['level'=>2])->field('id,name,first,lng,lat,pinyin')->select();
        $this->success('信息返回成功',$data);
    }

    /**
     * 根据首字母获取城市
     */
    public function cities(){
        $list = \app\common\model\Area::field("first")
            ->with(['children'=>function($query){
                $query->where('level',2)->where('name','neq','直辖县级')->where('first','not null')->field(['id','first','name','lng','lat']);
            }])
            ->where('level',2)
            ->where('first','not null')
            ->where('name','neq','直辖县级')
            ->order('first','asc')
            ->group("first")
            ->select();
        $this->success('',['cities'=>$list]);
    }

    /**
     * 根据经纬度获取位置
     * @return void
     */
    public function getLocation()
    {
        $lng = input('lng','');
        $lat = input('lat','');

        // 如果经纬度为空，尝试通过IP获取位置信息
        if (empty($lng) || empty($lat)) {
            $userIp = $this->request->ip();
            $ipLocation = \addons\service\library\Map::getLocationByIp($userIp);

            if ($ipLocation && isset($ipLocation['rectangle'])) {
                // 从IP定位结果中提取经纬度（取矩形区域的中心点）
                $rectangle = explode(';', $ipLocation['rectangle']);
                if (count($rectangle) == 2) {
                    $leftBottom = explode(',', $rectangle[0]);
                    $rightTop = explode(',', $rectangle[1]);
                    if (count($leftBottom) == 2 && count($rightTop) == 2) {
                        // 计算中心点经纬度
                        $lng = ($leftBottom[0] + $rightTop[0]) / 2;
                        $lat = ($leftBottom[1] + $rightTop[1]) / 2;
                    }
                }
            }

            // 如果IP定位失败，返回错误
            if (empty($lng) || empty($lat)) {
                $this->error('定位异常，无法获取位置信息');
            }
        }

        $re = \addons\service\library\Map::getLocation($lng,$lat);
        if (!$re) {
            $this->error('获取地址信息失败');
        }
        $this->success('地址信息返回成功',$re);
    }


    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @param string $account  账号
     * @param string $password 密码
     */
    public function login()
    {
        $account = $this->request->post('account');
        $password = $this->request->post('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 用户登录
     * @return void
     * @throws \think\Exception
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function userLogin()
    {
        $config = \app\api\model\service\ProjectConfigure::getProjectConfig();
        $type = input('type', '');
        if ($this->request->isPost()) {
            $invit_code = $this->request->post('invit_code');
            $post = $this->request->post();
            if (!isset($post['iv'])) {
                $this->error('参数获取异常');
            }
            if (!is_numeric($type)) {
                $this->error('参数缺失');
            }
            switch ($type) {
                case 0://用户端
                    $appid = $config['userappid'];
                    $secret = $config['usersecret'];
                    break;
                case 1://服务人员端
                    $appid = $config['skillappid'];
                    $secret = $config['skillsecret'];
                    break;
                case 2:
                    $appid = $config['shopappid'];
                    $secret = $config['shopsecret'];
                    break;
                default:
                    $appid = $config['userappid'];
                    $secret = $config['usersecret'];
            }
            $params = [
                'appid' => $appid,
                'secret' => $secret,
                'js_code' => $post['code'],
                'grant_type' => 'authorization_code'
            ];
            $result = Http::sendRequest("https://api.weixin.qq.com/sns/jscode2session", $params, 'GET');
            $json = (array)json_decode($result['msg'], true);
            if (array_key_exists('unionid',$json)) {
                $userInfo = \app\api\model\service\UserInfo::get(['unionid' => $json['unionid']]);
            }elseif(array_key_exists('openid',$json)){
                switch ($type)
                {
                    case 0:
                        $userInfo = \app\api\model\service\UserInfo::get(['user_openid' => $json['openid']]);
                        break;
                    case 1:
                        $userInfo = \app\api\model\service\UserInfo::get(['skill_openid' => $json['openid']]);
                        break;
                    case 2:
                        $userInfo = \app\api\model\service\UserInfo::get(['shop_openid' => $json['openid']]);
                        break;
                }
            }else{
                $this->error('登录异常,请联系管理员');
            }
            
            $user = $userInfo ? \app\common\model\User::get($userInfo->user_id) : null;
            if ($userInfo && $user && $user->status === 'normal') {
                $userInfo->updatetime = time();
                if(!$userInfo->user_openid && $type == 0)
                {
                    $userInfo->user_openid = $json['openid'];
                }elseif (!$userInfo->skill_openid && $type == 1)
                {
                    $userInfo->skill_openid = $json['openid'];
                }elseif (!$userInfo->shop_openid && $type == 2){
                    $userInfo->shop_openid = $json['openid'];
                }
                $userInfo->save();
                $ret = $this->auth->direct($userInfo->user_id);
            } else {
                $errCode = $this->decryptData($appid, $json['session_key'], $this->request->post('encryptedData','', 'trim'), urldecode($this->define_str_replace($post['iv'])), $data);
                if ($errCode == 0) {
                    $data = (array)json_decode($data, true);
                    $mobile = $data['phoneNumber'];
                    $user = \app\common\model\User::getByMobile($mobile);
                  
                    if ($user && $user->is_service == 0) {
                        if ($user->status != 'normal') {
                            $this->error(__('Account is locked'));
                        }
                        $userInfo = \app\api\model\service\UserInfo::get(['user_id' => $user->id]);
                      
                        if(array_key_exists('unionid',$json) && $json['unionid'])
                        {
                            $userInfo->unionid = $json['unionid'];
                        }
                        if($userInfo->user_openid != $json['openid']  && $type == 0)
                        {
                            $userInfo->user_openid = $json['openid'];
                        }elseif (!$userInfo->skill_openid != $json['openid'] && $type == 1)
                        {
                            $userInfo->skill_openid = $json['openid'];
                        }elseif (!$userInfo->shop_openid != $json['openid'] && $type == 2){
                            $userInfo->shop_openid = $json['openid'];
                        }
                        $userInfo->save();
                        $ret = $this->auth->direct($user->id);
                    } else {

                        $ret = $this->auth->register($mobile, $mobile, '', $mobile, ['avatar'=>config('site.head_image'),'invit_code'=>$invit_code]);
                        $uid = $this->auth->id;
                        $newUser = ['user_id'=>$uid,'mobile'=>$mobile];
                        if(array_key_exists('unionid',$json))
                        {
                            $newUser['unionid'] = $json['unionid'];
                        }
                        switch ($type)
                        {
                            case 0:
                                $newUser['user_openid'] = $json['openid'];
                                break;
                            case 1:
                                $newUser['skill_openid'] = $json['openid'];
                                break;
                            case 2:
                                $newUser['shop_openid'] = $json['openid'];
                                break;
                            default:
                                $newUser['user_openid'] = $json['openid'];
                        }
                        $userInfo = new UserInfo($newUser);
                        $userInfo->allowField(true)->save();

                        $this->giveRegCoupon();

                    }
                } else {
                    $this->error('获取信息失败');
                }
                
            }
            if ($ret) {
                    $userInfo = ['user' => $this->auth->getUserinfo(),'userInfo'=>UserInfo::getInfo($this->auth->id)];
                    $this->success(__('Logged in successful'), $userInfo);
            } else {
                    $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    //避免把iv里面的空格转换为 +
    public  function define_str_replace($data)
    {
        return str_replace(' ','+',$data);
    }

    /**
     * 微信信息解密
     * @param $appid
     * @param $sessionKey
     * @param $encryptedData
     * @param $iv
     * @param $data
     * @return int
     */
    public function decryptData($appid,$sessionKey, $encryptedData, $iv, &$data )
    {
        if (strlen($sessionKey) != 24) {
            return -41001;
        }
        $aesKey=base64_decode($sessionKey);


        if (strlen($iv) != 24) {
            return -41002;
        }
        $aesIV=base64_decode($iv);

        $aesCipher=base64_decode($encryptedData);

        $result=openssl_decrypt( $aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);

        $dataObj=json_decode( $result );
        if( $dataObj  == NULL )
        {
            return -41003;
        }
        if( $dataObj->watermark->appid != $appid )
        {
            return -41004;
        }
        $data = $result;
        return 0;
    }


    public  function giveRegCoupon()
    {
        $userInfo2=$this->auth->getUserinfo();
        if(empty($userInfo2['parent_id'])) return false;

        $parent_user= \app\common\model\User::get($userInfo2['parent_id']);
        $is_dy=true;
        if($parent_user['is_sqdl']==1 || $parent_user['is_qydl']==1) $is_dy=false;
        $couponInfo = \app\api\model\service\Coupon::where(['type'=>5])->order('id desc')->find();
        if($couponInfo['scope']==1) {
            \app\api\model\service\UserCoupon::create(['user_id' => $userInfo2['id'], 'coupon_id' => $couponInfo['id'], 'type' => $couponInfo['type'], 'goods_id' => $couponInfo['goods_id'], 'shop_id' => $couponInfo['shop_id'], 'code' => $couponInfo['code'], 'achieve' => $couponInfo['achieve'], 'reduce' => $couponInfo['reduce'], 'exittime' => 86400 * $couponInfo['effective_day'] + time(), 'state' => 0]);
           if($is_dy)
            \app\api\model\service\UserCoupon::create(['user_id' => $userInfo2['parent_id'], 'coupon_id' => $couponInfo['id'], 'type' => $couponInfo['type'], 'goods_id' => $couponInfo['goods_id'], 'shop_id' => $couponInfo['shop_id'], 'code' => $couponInfo['code'], 'achieve' => $couponInfo['achieve'], 'reduce' => $couponInfo['reduce'], 'exittime' => 86400 * $couponInfo['effective_day'] + time(), 'state' => 0]);

            $result = new CouponReceive();
            $coupon=\app\index\model\wanlshop\Coupon::get(12);
            $result->state = 1;
            $result->coupon_id = 12;
            $result->coupon_no = Random::alnum(16);
            $result->user_id = $userInfo2['id'];
            $result->shop_id = $coupon['shop_id'];
            $result->type = 'reduction';
            $result->name = $couponInfo['name'];
            $result->userlevel = $coupon['userlevel'];
            $result->usertype = $coupon['usertype'];
            $result->price = $couponInfo['reduce'];
            $result->discount = $coupon['discount'];
            $result->limit = $couponInfo['achieve'];
            $result->rangetype = $coupon['rangetype'];
            $result->range = $coupon['range'];
            $result->pretype = $coupon['pretype'];
            $result->validity = $couponInfo['effective_day'];
            $result->startdate = date('Y-m-d');
            $result->enddate = date('Y-m-d',86400 * $couponInfo['effective_day'] + time());
            $result->save();


            $result = new CouponReceive();
            $coupon=\app\index\model\wanlshop\Coupon::get(12);
            $result->state = 1;
            $result->coupon_id = 12;
            $result->coupon_no = Random::alnum(16);
            $result->user_id = $userInfo2['parent_id'];
            $result->shop_id = $coupon['shop_id'];
            $result->type = 'reduction';
            $result->name = $couponInfo['name'];
            $result->userlevel = $coupon['userlevel'];
            $result->usertype = $coupon['usertype'];
            $result->price = $couponInfo['reduce'];
            $result->discount = $coupon['discount'];
            $result->limit = $couponInfo['achieve'];
            $result->rangetype = $coupon['rangetype'];
            $result->range = $coupon['range'];
            $result->pretype = $coupon['pretype'];
            $result->validity = $couponInfo['effective_day'];
            $result->startdate = date('Y-m-d');
            $result->enddate = date('Y-m-d',86400 * $couponInfo['effective_day'] + time());
            if($is_dy)
            $result->save();
            //课程
            $coupon = \app\common\model\xiluedu\Coupon::where('id',4)->find();
            $info = [
                'user_id' => $userInfo2['id'],
                'coupon_id'=>$coupon['id'],
                'start_time'=> $coupon['use_start_time'],
                'end_time'=> 86400 * $couponInfo['effective_day'] + time(),
                'createtime'=>time(),
                'updatetime'=>time(),
            ];
            $info2 = [
                'user_id' => $userInfo2['parent_id'],
                'coupon_id'=>$coupon['id'],
                'start_time'=> $coupon['use_start_time'],
                'end_time'=> 86400 * $couponInfo['effective_day'] + time(),
                'createtime'=>time(),
                'updatetime'=>time(),
            ];
            $user_coupon[] = $info;
            if($is_dy)
            $user_coupon[] = $info2;
            $model =  new  \app\admin\model\xiluedu\UserCoupon();
            $model->saveAll($user_coupon);


        }elseif ($couponInfo['scope']==2){

            $result = new CouponReceive();
            $coupon=\app\index\model\wanlshop\Coupon::get(12);
            $result->state = 1;
            $result->coupon_id = 12;
            $result->coupon_no = Random::alnum(16);
            $result->user_id = $userInfo2['id'];
            $result->shop_id = $coupon['shop_id'];
            $result->type = 'reduction';
            $result->name = $couponInfo['name'];
            $result->userlevel = $coupon['userlevel'];
            $result->usertype = $coupon['usertype'];
            $result->price = $couponInfo['reduce'];
            $result->discount = $coupon['discount'];
            $result->limit = $couponInfo['achieve'];
            $result->rangetype = $coupon['rangetype'];
            $result->range = $coupon['range'];
            $result->pretype = $coupon['pretype'];
            $result->validity = $couponInfo['effective_day'];
            $result->startdate = date('Y-m-d');
            $result->enddate = date('Y-m-d',86400 * $couponInfo['effective_day'] + time());
            $result->save();


            $result = new CouponReceive();
            $result->state = 1;
            $result->coupon_id = 12;
            $result->coupon_no = Random::alnum(16);
            $result->user_id = $userInfo2['parent_id'];
            $result->shop_id = $coupon['shop_id'];
            $result->type = 'reduction';
            $result->name = $couponInfo['name'];
            $result->userlevel = $coupon['userlevel'];
            $result->usertype = $coupon['usertype'];
            $result->price = $couponInfo['reduce'];
            $result->discount = $coupon['discount'];
            $result->limit = $couponInfo['achieve'];
            $result->rangetype = $coupon['rangetype'];
            $result->range = $coupon['range'];
            $result->pretype = $coupon['pretype'];
            $result->validity = $couponInfo['effective_day'];
            $result->startdate = date('Y-m-d');
            $result->enddate = date('Y-m-d',86400 * $couponInfo['effective_day'] + time());
            if($is_dy)
            $result->save();

        }elseif ($couponInfo['scope']==3){
            \app\api\model\service\UserCoupon::create(['user_id' => $userInfo2['id'], 'coupon_id' => $couponInfo['id'], 'type' => $couponInfo['type'], 'goods_id' => $couponInfo['goods_id'], 'shop_id' => $couponInfo['shop_id'], 'code' => $couponInfo['code'], 'achieve' => $couponInfo['achieve'], 'reduce' => $couponInfo['reduce'], 'exittime' => 86400 * $couponInfo['effective_day'] + time(), 'state' => 0]);
            if($is_dy)
            \app\api\model\service\UserCoupon::create(['user_id' => $userInfo2['parent_id'], 'coupon_id' => $couponInfo['id'], 'type' => $couponInfo['type'], 'goods_id' => $couponInfo['goods_id'], 'shop_id' => $couponInfo['shop_id'], 'code' => $couponInfo['code'], 'achieve' => $couponInfo['achieve'], 'reduce' => $couponInfo['reduce'], 'exittime' => 86400 * $couponInfo['effective_day'] + time(), 'state' => 0]);

        }elseif ($couponInfo['scope']==4){
            $coupon = \app\common\model\xiluedu\Coupon::where('id',4)->find();
            $info = [
                'user_id' => $userInfo2['id'],
                'coupon_id'=>$coupon['id'],
                'start_time'=> $coupon['use_start_time'],
                'end_time'=> 86400 * $couponInfo['effective_day'] + time(),
                'createtime'=>time(),
                'updatetime'=>time(),
            ];
            $info2 = [
                'user_id' => $userInfo2['parent_id'],
                'coupon_id'=>$coupon['id'],
                'start_time'=> $coupon['use_start_time'],
                'end_time'=>86400 * $couponInfo['effective_day'] + time(),
                'createtime'=>time(),
                'updatetime'=>time(),
            ];
            $user_coupon[] = $info;
            if($is_dy)
            $user_coupon[] = $info2;
            $model =  new  \app\admin\model\xiluedu\UserCoupon();
            $model->saveAll($user_coupon);
        }

        return true;
    }


    public function getAreaInfo()
    {
        $user_id = $this->request->param('user_id');
        $info= UserArea::where('user_id', $user_id)->find();
        $this->success('获取成功', $info);
    }
}
