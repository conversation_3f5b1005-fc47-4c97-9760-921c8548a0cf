<?php

namespace app\common\model\xiluedu;

use app\admin\model\ShareGoodsOrder;
use app\common\library\Auth;
use app\common\model\User;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\DbException;
use think\Hook;
use think\Model;
use function fast\array_get;


class CourseOrder extends Model
{
    // 表名
    protected $name = 'xiluedu_course_order';

    protected $autoWriteTimestamp = 'int';

    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    public function user() {
        return $this->belongsTo(User::class, 'user_id')->setEagerlyType(0);
    }

    public function course() {
        return $this->belongsTo('Course', 'course_id')->setEagerlyType(0);
    }

    public static function lists($params,$user_id){

        $tab = array_get($params,'tab');
        $pagesize = array_get($params,'pagesize',10);
        $is_service = array_get($params,'is_service',0);
        
        $where['user_id'] = $user_id;
        if($tab == 1){
            $where['pay_status'] = 1;
        }else if($tab == 2){
            $where['pay_status'] = 2;
        }
        if($is_service){
            $where['course_order.is_service'] = $is_service;
        }
       $data= self::where($where)
            ->with(['course'=>function($query){
                $query->withField(['id','type','name','category_id','teacher_id','thumb_image','description','collection_count','salesprice']);
            }])
            ->order('updatetime desc')
            ->paginate($pagesize);
        if($data){
           foreach($data as $k=>$v){
               $data[$k]['course_salesprice']=$v['course']['salesprice']??0;
           }
        }    
             return $data;
    }

    public static function detail($order_id){

        return self::where('course_order.id',$order_id)
            ->with(['course'=>function($query){
                $query->withField(['id','type','name','category_id','teacher_id','thumb_image','description','collection_count','salesprice']);
            }])
            ->find();
    }


    //确认下单
    public static function sure($params){
        if($course_id = array_get($params,'course_id')){
            $row = Course::with(['teacher'=>function($query){
                $query->withField(['id','name','job']);
            }])->field('id,name,status,salesprice,teacher_id,thumb_image')->where('id',$course_id)->find();
            if(!$row || $row->status == 0){
                throw new Exception("课程不存在或已下架");
            }
            $total_price = $row->salesprice;
            $pay_price = $row->salesprice;
            $user_coupon_id = array_get($params,'user_coupon_id');
            //优惠券
            $coupons = UserCoupon::getCanCoupon($course_id);
            $coupon_price = 0;
            $can_coupon = [];
            $use_coupon = [];
            foreach ($coupons as $coupon){
                if($coupon['range_status'] == '1'){
                    if($pay_price >= $coupon['at_least']){
                        if(!$user_coupon_id || $user_coupon_id == $coupon['user_coupon_id']){
                            list($coupon_price,$use_coupon) = self::coupon_cal($coupon,$pay_price,$coupon_price,$use_coupon);
                        }
                        $can_coupon[] = $coupon;
                        continue;
                    }
                }
                else{
                    #课程id
                    if($row['salesprice'] >= $coupon['at_least']){
                        if(!$user_coupon_id || $user_coupon_id == $coupon['user_coupon_id']){
                            list($coupon_price,$use_coupon) = self::coupon_cal($coupon,$row['salesprice'],$coupon_price,$use_coupon);
                        }
                        $can_coupon[] = $coupon;
                        break;
                    }
                }
            }
            unset($coupon);
            //修正大于价格的优惠券
            if($pay_price < $coupon_price){
                $coupon_price = 0;
                $use_coupon = [];
            }

            foreach ($can_coupon as $coupon){
                $coupon['checked'] = false;
                if($coupon['id'] == $use_coupon['id']){
                    $coupon['checked'] = true;
                }
            }

            $pay_price = $pay_price - $coupon_price;

            $orderinfo = [
                'pay_price'     => $pay_price,
                'total_price'   => $total_price,
                'course'        => $row,
                'coupon_price'  =>  $coupon_price,
                'use_coupon'   =>  $use_coupon,
                'available_coupon'=>$can_coupon,
            ];
            return $orderinfo;
        }else{
            throw new Exception("非法参数");
        }
    }


    protected static function coupon_cal($coupon,$price,$coupon_price,$use_coupon){

        if($coupon['type'] == '1'){
            if($coupon_price < $coupon['money']){
                $coupon_price = $coupon['money'];
                $use_coupon = [
                    'id'=>$coupon['id'],'name'=>$coupon['name'],
                    'type'=>$coupon['type'],'discount'=>$coupon['discount'],
                    'at_least'=>$coupon['at_least'],'money'=>$coupon['money'],
                    'range_status'=>$coupon['range_status'],'user_coupon_id'=>$coupon['user_coupon_id'],
                ];
            }
        }
        else{
            //$discount_price = sprintf('%.2f',(100-$coupon['discount']) / 100 * $price) ;
            $discount_price = bcmul(bcdiv(bcsub(100,$coupon['discount']),100,2),$price,2);
            if($coupon_price < $discount_price){
                $coupon_price = $discount_price;
                $use_coupon = [
                    'id'=>$coupon['id'],'name'=>$coupon['name'],
                    'type'=>$coupon['type'],'discount'=>$coupon['discount'],
                    'at_least'=>$coupon['at_least'],'money'=>$coupon['money'],
                    'range_status'=>$coupon['range_status'],'user_coupon_id'=>$coupon['user_coupon_id'],
                ];
            }
        }
        return [$coupon_price,$use_coupon];
    }

    public static function createOrder($params) {
        $sure = static::sure($params);
        $auth = Auth::instance();
        // if(UserCourse::isBuy($auth->id,$params['course_id'])){
        //     throw new  Exception("无需重复购买课程");
        // }
        db()->startTrans();
        //如果用户是养老院长或者是城市运营商,则不允许购买课程16的课程
        $userInfo=User::where('id',$auth->id)->find();
        if($params['course_id'] == 16 ){
            if($userInfo->is_ylgw == 1 || $userInfo->is_sqdl == 1 || $userInfo->is_qydl  == 1){
           
                throw new  Exception("您当前的身份无法购买此课程");
            }
        }
        if($params['course_id'] == 10 ){
            if($userInfo->is_sqdl == 1 || $userInfo->is_qydl  == 1){
           
                throw new  Exception("您当前的身份无法购买此课程");
            }
        }


        #订单创建
        $data = [
            'platform'      =>  array_get($params,'platform','wxmin'),
            'user_id'       =>  $auth->id,
            'order_no'      =>  "C".date("YmdHis").mt_rand(10,9999),
            'course_id'     =>  $sure['course']['id'],
            'total_price'   =>  $sure['total_price'],
            'pay_price'     =>  $sure['pay_price'],
            'user_coupon_id'=>  $sure['use_coupon']['user_coupon_id'] ?? 0,
            'favourable_price'=>  $sure['coupon_price'],
            'ip'            => request()->ip(),
            'pay_status'    =>  1,
            'is_service'   => $params['is_service'] ?? 0,
        ];

        if(!empty($params['share_id'])){
            $share_order=ShareGoodsOrder::where(['id'=>$params['share_id'],'goods_type'=>3])->find();
            if(!empty($share_order) && $share_order['reduce']>0) {
                $data['pay_price'] = bcsub($data['pay_price'], $share_order['reduce'], 2);
                $share_order->save(['status'=>3]);
            }
        }

        try {
            $result = self::create($data);
            $result2 = true;
            if($sure['use_coupon']){
                $result2 = UserCoupon::where('id',$sure['use_coupon']['user_coupon_id'])->update(['use_status'=>1,'usetime'=>time()]);
            }
            Hook::listen("xiluedu_order_divide",$result,['type'=>'course']);
        }catch (Exception $e){
            throw $e;
        }
        if($result2 && $result !== false){
            db()->commit();
            return $result;
        }else {
            db()->rollback();
            throw new Exception("订单创建失败");
        }
    }

    public static function pay_notify($order_no,$transation_id=''){
        $order = self::where('order_trade_no',$order_no)->find();
        if(!$order){
            throw new Exception('订单不存在');
        }
        if($order->pay_status == '2'){
            throw new Exception('订单已支付完毕');
        }
        $order->pay_status = '2';
        $order->paytime = time();
        if($order->course_id==10){
            $order->kt_sqdl=1;
            // 获取用户信息
            $user = User::where('id', $order->user_id)->find();
            if ($user && $user->parent_id == 0) { // 只有当用户没有上级时才进行分配
                // 尝试获取用户默认地址
                $user_address = \app\api\model\wanlshop\Address::where(['user_id' => $user->id, 'default' => 1])->find();
                // 检查用户是否有省、市、区信息
                $user_has_full_address = !empty($user_address) && !empty($user_address['province']) && !empty($user_address['city']) && !empty($user_address['district']);

                if (!$user_has_full_address) {
                    // 如果用户没有完整地址信息，则分配给默认城市运营商
                    $default_city_manager = \app\common\model\UserArea::getDefaultCityManager();
                    if ($default_city_manager) {
                        $user->parent_id = $default_city_manager['id']; // UserArea::getDefaultCityManager returns User model array
                        $user->save();
                        \think\Log::info("课程10支付通知：用户ID {$user->id} 无完整地址，分配给默认城市运营商ID {$user->parent_id}");
                    } else {
                        \think\Log::info("课程10支付通知：用户ID {$user->id} 无完整地址，但未找到默认城市运营商");
                    }
                }

            }
        }
        $order->save();
        #分销佣金更新为已支付、待分配
        if($divide = OrderDivide::checkExist($order->id,'course')){
            $divide->save(['status'=>2]);
        }

        Course::where('id',$order->course_id)->setInc('real_num',1);

        // 特殊处理课程16（养老顾问套餐）
        if($order->course_id == 16) {
            // 获取用户信息
            $user = User::where('id', $order->user_id)->find();
            if (!$user) {
                throw new Exception('用户不存在');
            }

            // 根据套餐数量创建用户课程关联
            $package_quantity = $order->package_quantity ?: 1;
            for ($i = 0; $i < $package_quantity; $i++) {
                UserCourse::create([
                    'user_id' => $order->user_id,
                    'course_id' => 16,
                    'from_type' => 1, // 购买获得
                ]);
            }

            // 只有普通用户购买课程16才开通养老顾问身份
            // 城市运营商和养老院长购买套餐是为了获得开通额度，不改变自己的身份
            if ($user->is_ylgw == 0 && $user->is_qydl == 0 && $user->is_sqdl == 0) {
                $user->is_ylgw = 1;
                $user->save();
            }else{
                       // 如果购买的是套餐（数量大于等于1），增加开通额度
                        // 所有用户购买套餐都会获得开通额度
                        if ($package_quantity >= 1) {
                            $user->ylgw_quota = $user->ylgw_quota + $package_quantity;
                            $user->save();
                        }
            }

           

            // 执行课程16的分佣逻辑（与余额支付保持一致）
            try {
                \app\api\controller\xiluedu\Package::processCourse16Commission($user, '养老顾问身份套餐', $order);
            } catch (Exception $e) {
                // 分佣失败不影响主流程，只记录日志
                \think\Log::error('课程16微信支付分佣失败：' . $e->getMessage());
            }
        } else {
            // 其他课程的正常处理
            $user_course = [
                'user_id'   =>  $order->user_id,
                'course_id' =>  $order->course_id,
            ];
            UserCourse::create($user_course);
        }

        Hook::listen("xiluedu_course_buy",$order);
        Hook::listen("xiluedu_divide",$order,['type'=>'course']);

        // 对于课程10和课程16，佣金计算已经在Hook中完成，避免重复计算
        if($order->pay_price > 0 && !in_array($order->course_id, [10, 16])) {
            self::shareProfit($order->id, 1);
        }
        return $order;
    }

    /**
     * 课程分佣
     * @param $order_no
     * @throws Exception
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public static function shareProfit($order_id,$type=1){

        if($type == 1) {
            $order = self::where(['pay_status' => 2, 'id' => $order_id])->find();
            if (!$order)  return;

            // 课程16使用统一的新分佣逻辑，不走这个旧方法
            if($order['course_id'] == 16) {
                \think\Log::info("课程16跳过旧分佣逻辑 - 订单ID: {$order_id}");
                return;
            }

//            if ($order['user_confirm'] == 1)  throw new Exception('请勿重复确认');
            $goods = Course::where('id', $order['course_id'])->field('id,shequ_bili,quyu_bili,service_bili,name')->find();
        }else{
            $order = OfflineOrder::where(['pay_status' => 1, 'id' => $order_id])->find();
            if (!$order)  return;
//            if ($order['user_confirm'] == 1) throw new Exception('请勿重复确认');
            $goods = OfflineCourse::where('id', $order['course_id'])->field('id,shequ_bili,quyu_bili,service_bili,name')->find();
        }
        if(empty($goods)) return false;

        // 引入分佣日志记录类
        $commissionLogger = new \app\common\library\CommissionLogger();
        $commissions = []; // 用于记录所有分佣信息
        // 计算老师分佣
        if($goods['service_bili']!=0){
            $service_money = truncateDecimal($order['total_price'] * ($goods['service_bili'] / 100));
            $teacher_rate = $goods['service_bili'];
            $teacher_config_source = 'goods_level';
        }else{
            $teacher_rate = config('site.course_teacher_rate') ?: 30;
            $service_money = truncateDecimal($order['total_price'] * ($teacher_rate / 100));
            $teacher_config_source = 'global';
        }

        // 记录老师分佣日志
        if($service_money > 0) {
            $teacher_id = $goods['teacher_id'] ?? 1;
            $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                $teacher_id,
                'teacher',
                'teacher',
                $teacher_rate,
                $service_money,
                $order['total_price'],
                \app\common\library\CommissionLogger::formatCalculationRule('teacher', $teacher_rate, $order['total_price'], $teacher_config_source),
                [
                    'config_source' => $teacher_config_source,
                    'config_value' => $teacher_rate . '%'
                ]
            );
        }

        $residue=$order['total_price']-$service_money-$order['favourable_price'];
        // 计算养老院长分佣
        if($goods['shequ_bili'] && $goods['shequ_bili']>0){
            $sq_money = truncateDecimal($residue * ($goods['shequ_bili'] / 100));
            $sq_rate = $goods['shequ_bili'];
            $sq_config_source = 'goods_level';
        }else{
            $sq_rate = config('site.course_shequ_rate') ?: 10;
            $sq_money = truncateDecimal($residue * ($sq_rate / 100));
            $sq_config_source = 'global';
        }

        // 计算城市运营商分佣
        if($goods['quyu_bili'] && $goods['quyu_bili']>0){
            $qy_money = truncateDecimal($residue * ($goods['quyu_bili'] / 100));
            $qy_rate = $goods['quyu_bili'];
            $qy_config_source = 'goods_level';
        }else{
            $qy_rate = config('site.course_quyu_rate') ?: 15;
            $qy_money = truncateDecimal($residue * ($qy_rate / 100));
            $qy_config_source = 'global';
        }
        // 检查是否使用新分佣逻辑
        $new_commission_courses = explode(',', config('site.new_commission_courses') ?: '10,16');
        $use_new_commission = in_array($order['course_id'], $new_commission_courses);

        if($use_new_commission) {
            // 新分佣逻辑：直接基于课程价格计算
            $sq_rate = config('site.course_sq_commission_rate') ?: 50; // 养老院长分佣比例
            $qy_rate = config('site.course_qy_commission_rate') ?: 10; // 城市运营商分佣比例
            $ylgw_rate = config('site.course_ylgw_commission_rate') ?: 50; // 养老顾问推荐分佣比例

            $sq_money = truncateDecimal($order['total_price'] * ($sq_rate / 100));
            $qy_money = truncateDecimal($order['total_price'] * ($qy_rate / 100));
            $ylgw_money = truncateDecimal($sq_money * ($ylgw_rate / 100));
        } else {
            // 原有分佣逻辑
            $ylgw_money = truncateDecimal($sq_money * (config('site.gjia_bili') / 100));
        }
        //发放养老院长佣金
        if($sq_money > 0){
            $user = User::where('id',$order['user_id'])->field('id,parent_id,is_sqdl')->find();

            // 如果购买用户自己就是养老院长（比如购买课程10升级为养老院长）
            if($user['is_sqdl'] == 1) {
                // 购买用户自己是养老院长，不发放分佣（自己买自己的不分佣）
                // 但保留$sq_money用于计算养老顾问分佣
                $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                    $user['id'],
                    'nursing_home_director',
                    'direct',
                    $sq_rate,
                    0, // 不发放，金额为0
                    $use_new_commission ? $order['total_price'] : $residue,
                    '购买用户自己是养老院长，不发放分佣',
                    [
                        'config_source' => $sq_config_source,
                        'config_value' => $sq_rate . '%',
                        'is_distributed' => 0,
                        'distribution_memo' => '自己购买不分佣'
                    ]
                );
            } else {
                // 如果购买用户不是养老院长，查找上级养老院长
                if($user['parent_id']){
                    $sq_user = \app\common\model\User::getOneSq($order['user_id']);
                    if(!empty($sq_user)) {
                        \app\common\model\User::money($sq_money,$sq_user['id'],'养老院长分成',2,$order['order_no'],'fenyong');
                        $residue-=$sq_money;

                        // 记录养老院长分佣日志
                        $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                            $sq_user['id'],
                            'nursing_home_director',
                            'direct',
                            $sq_rate,
                            $sq_money,
                            $use_new_commission ? $order['total_price'] : $residue + $sq_money,
                            \app\common\library\CommissionLogger::formatCalculationRule('direct', $sq_rate, $use_new_commission ? $order['total_price'] : $residue + $sq_money, $sq_config_source),
                            [
                                'config_source' => $sq_config_source,
                                'config_value' => $sq_rate . '%',
                                'is_distributed' => 1,
                                'distribution_status' => 'success'
                            ]
                        );
                    } else {
                        // 没有找到养老院长，记录未分佣日志
                        $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                            0,
                            'nursing_home_director',
                            'direct',
                            $sq_rate,
                            0,
                            $use_new_commission ? $order['total_price'] : $residue,
                            '未找到上级养老院长',
                            [
                                'config_source' => $sq_config_source,
                                'config_value' => $sq_rate . '%',
                                'is_distributed' => 0,
                                'distribution_memo' => '未找到上级养老院长'
                            ]
                        );
                    }
                } else {
                    // 没有上级，记录未分佣日志
                    $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                        0,
                        'nursing_home_director',
                        'direct',
                        $sq_rate,
                        0,
                        $use_new_commission ? $order['total_price'] : $residue,
                        '用户没有上级',
                        [
                            'config_source' => $sq_config_source,
                            'config_value' => $sq_rate . '%',
                            'is_distributed' => 0,
                            'distribution_memo' => '用户没有上级'
                        ]
                    );
                }
            }
        }

        //发放养老顾问佣金
        if($ylgw_money > 0){
            $ylgw_user = \app\common\model\User::getOneYlgw($order['user_id']);
            if(!empty($ylgw_user)) {
                // 检查找到的养老顾问是否就是购买用户自己，且购买用户已经是养老院长
                $purchase_user = User::where('id',$order['user_id'])->field('id,parent_id,is_sqdl')->find();
                if($ylgw_user['id'] == $order['user_id'] && $purchase_user['is_sqdl'] == 1) {
                    // 如果购买用户自己就是养老顾问且已升级为养老院长，跳过养老顾问分佣
                    // 避免重复分佣，优先保证养老院长分佣
                    $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                        $ylgw_user['id'],
                        'elderly_advisor',
                        'direct',
                        $use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili'),
                        0, // 不发放，金额为0
                        $sq_money,
                        '购买用户自己是养老顾问且已升级为养老院长，避免重复分佣',
                        [
                            'config_source' => 'global',
                            'config_value' => ($use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili')) . '%',
                            'is_distributed' => 0,
                            'distribution_memo' => '避免重复分佣，优先保证养老院长分佣'
                        ]
                    );
                } else {
                    // 检查是否是推荐分佣（购买用户的直接上级就是养老顾问）
                    $is_referral_commission = ($purchase_user['parent_id'] == $ylgw_user['id']);

                    if($is_referral_commission) {
                        // 推荐分佣：直接发放，不受上级关系影响
                        \app\common\model\User::money($ylgw_money,$ylgw_user['id'],'养老顾问推荐分成',2,$order['order_no'],'fenyong');
                        $residue-=$ylgw_money;

                        // 记录推荐分佣日志
                        $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                            $ylgw_user['id'],
                            'elderly_advisor',
                            'recommendation',
                            $use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili'),
                            $ylgw_money,
                            $sq_money,
                            '推荐分佣：直接发放，不受上级关系影响',
                            [
                                'config_source' => 'global',
                                'config_value' => ($use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili')) . '%',
                                'is_distributed' => 1,
                                'distribution_status' => 'success'
                            ]
                        );
                    } else {
                        // 普通分佣：按原有逻辑处理
                        $ylgw_parent = \app\common\model\User::where('id', $ylgw_user['id'])->field('parent_id,is_sqdl,is_qydl')->find();
                        $has_superior = false;

                        if($ylgw_parent && $ylgw_parent['parent_id'] > 0) {
                            // 检查上级是否是养老院长或城市运营商
                            $parent_user = \app\common\model\User::where('id', $ylgw_parent['parent_id'])->field('is_sqdl,is_qydl')->find();
                            if($parent_user && ($parent_user['is_sqdl'] == 1 || $parent_user['is_qydl'] == 1)) {
                                $has_superior = true;
                            }
                        }

                        if(!$has_superior) {
                            // 没有上级，直接发放到账户余额
                            \app\common\model\User::money($ylgw_money,$ylgw_user['id'],'养老顾问分成',2,$order['order_no'],'fenyong');
                            $residue-=$ylgw_money;

                            // 记录直接发放日志
                            $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                                $ylgw_user['id'],
                                'elderly_advisor',
                                'indirect',
                                $use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili'),
                                $ylgw_money,
                                $sq_money,
                                '没有上级，直接发放到账户余额',
                                [
                                    'config_source' => 'global',
                                    'config_value' => ($use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili')) . '%',
                                    'is_distributed' => 1,
                                    'distribution_status' => 'success'
                                ]
                            );
                        } else {
                            // 有上级，佣金分给上级，养老顾问记录应得金额
                            $parent_user = \app\common\model\User::where('id', $ylgw_user['parent_id'])->find();
                            if ($parent_user) {
                                // 1. 佣金实际分给上级账户
                                \app\common\model\User::money($ylgw_money, $parent_user['id'], '下级养老顾问分佣-线上课程(来自用户' . $ylgw_user['id'] . ')', 2, $order['order_no'], 'fenyong');

                                // 2. 记录养老顾问应得金额
                                $current_commission = $ylgw_user['ylgw_total_commission'] ?: 0;
                                \app\common\model\User::where('id', $ylgw_user['id'])->setInc('ylgw_total_commission', $ylgw_money);

                                // 3. 记录养老顾问的应得分佣详情
                                \app\common\model\MoneyLog::create([
                                    'user_id' => $ylgw_user['id'],
                                    'money' => $ylgw_money,
                                    'before' => $current_commission,
                                    'after' => $current_commission + $ylgw_money,
                                    'memo' => '应得分佣-线上课程(已分给上级' . $parent_user['id'] . ',待线下分账)',
                                    'type' => 'pending_commission',
                                    'createtime' => time()
                                ]);
                            }

                            // 记录线下分佣日志
                            $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                                $ylgw_user['id'],
                                'elderly_advisor',
                                'indirect',
                                $use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili'),
                                $ylgw_money,
                                $sq_money,
                                '有上级，记录到养老顾问总佣金字段，通过对账系统提现',
                                [
                                    'config_source' => 'global',
                                    'config_value' => ($use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili')) . '%',
                                    'is_distributed' => 0,
                                    'distribution_type' => 'offline',
                                    'distribution_memo' => '有上级的养老顾问，通过线下对账分佣'
                                ]
                            );
                        }
                    }
                }
            } else {
                // 没有找到养老顾问，记录未分佣日志
                $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                    0,
                    'elderly_advisor',
                    'indirect',
                    $use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili'),
                    0,
                    $sq_money,
                    '未找到养老顾问',
                    [
                        'config_source' => 'global',
                        'config_value' => ($use_new_commission ? config('site.course_ylgw_commission_rate') : config('site.gjia_bili')) . '%',
                        'is_distributed' => 0,
                        'distribution_memo' => '未找到养老顾问'
                    ]
                );
            }
        }

        //发放城市运营商佣金
        if($qy_money > 0){
            if($use_new_commission) {
                // 新分佣逻辑：直接查找城市运营商
                $qy_user = \app\common\model\User::getOneQy($order['user_id']);
                if(!empty($qy_user)) {
                    \app\common\model\User::money($qy_money,$qy_user['id'],'城市运营商分成',2,$order['order_no'],'fenyong');
                    $residue-=$qy_money;

                    // 记录城市运营商分佣日志
                    $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                        $qy_user['id'],
                        'city_manager',
                        'direct',
                        $qy_rate,
                        $qy_money,
                        $order['total_price'],
                        \app\common\library\CommissionLogger::formatCalculationRule('direct', $qy_rate, $order['total_price'], $qy_config_source),
                        [
                            'config_source' => $qy_config_source,
                            'config_value' => $qy_rate . '%',
                            'is_distributed' => 1,
                            'distribution_status' => 'success'
                        ]
                    );
                } else {
                    // 没有找到城市运营商，记录未分佣日志
                    $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                        0,
                        'city_manager',
                        'direct',
                        $qy_rate,
                        0,
                        $order['total_price'],
                        '未找到城市运营商',
                        [
                            'config_source' => $qy_config_source,
                            'config_value' => $qy_rate . '%',
                            'is_distributed' => 0,
                            'distribution_memo' => '未找到城市运营商'
                        ]
                    );
                }
            } else {
                // 原有逻辑：通过养老院长的上级
                if(!empty($sq_user) && $sq_user['parent_id'] > 0){
                    \app\common\model\User::money($qy_money,$sq_user['parent_id'],'城市运营商分成',1,$order['order_no'],'fenyong');
                    $residue-=$qy_money;

                    // 记录城市运营商分佣日志
                    $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                        $sq_user['parent_id'],
                        'city_manager',
                        'indirect',
                        $qy_rate,
                        $qy_money,
                        $use_new_commission ? $order['total_price'] : $residue + $qy_money,
                        \app\common\library\CommissionLogger::formatCalculationRule('indirect', $qy_rate, $use_new_commission ? $order['total_price'] : $residue + $qy_money, $qy_config_source),
                        [
                            'config_source' => $qy_config_source,
                            'config_value' => $qy_rate . '%',
                            'is_distributed' => 1,
                            'distribution_status' => 'success'
                        ]
                    );
                } else {
                    // 没有找到城市运营商，记录未分佣日志
                    $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                        0,
                        'city_manager',
                        'indirect',
                        $qy_rate,
                        0,
                        $use_new_commission ? $order['total_price'] : $residue,
                        '养老院长没有上级城市运营商',
                        [
                            'config_source' => $qy_config_source,
                            'config_value' => $qy_rate . '%',
                            'is_distributed' => 0,
                            'distribution_memo' => '养老院长没有上级城市运营商'
                        ]
                    );
                }
            }
        }

        //放发平台收益
        if($residue>0){
            \app\common\model\User::money($residue,1,'平台收益',1,$order['order_no'],'platform_income');

            // 记录平台收益日志
            $commissions[] = \app\common\library\CommissionLogger::createCommissionRecord(
                1,
                'platform',
                'direct',
                0, // 平台收益没有固定比例
                $residue,
                $order['total_price'],
                '平台收益：订单总额减去各项分佣后的剩余金额',
                [
                    'config_source' => 'special_rule',
                    'config_value' => '剩余金额',
                    'is_distributed' => 1,
                    'distribution_status' => 'success'
                ]
            );
        }

        //给服务者发放佣金
        if($service_money > 0){
            $teacher_id = $goods['teacher_id'] ?? 1;
            $money_type = ($teacher_id == 1) ? 'platform_income' : 'fenyong';
            $memo = ($teacher_id == 1) ? '课程老师佣金（平台收取）' : '课程老师佣金';
            \app\common\model\User::money($service_money, $teacher_id, $memo, 1, $order['order_no'], $money_type);
        }

        // 记录完整的分佣日志
        if (!empty($commissions)) {
            $courseType = ($type == 1) ? 'course' : 'offline_course';
            $orderData = $order->toArray();
            $orderData['course_name'] = $goods['name'] ?? '课程';
            \app\common\library\CommissionLogger::logCourseCommission($orderData, $commissions, $courseType);
        }



//        self::where(['id'=>$params['id']])->update(['user_confirm_time'=>time(),'shequ_money'=>$sq_money,'quyu_money'=>$qy_money,'service_money'=>$service_money,'user_confirm'=>1]);
        //发放分佣end

        // $userOpenid = UserInfo::getOpenid($order['user_id'],0);
        // Order::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'note'=>'有问题请联系客服','type'=>0,'templateAttr'=>'user_order_template','openid'=>$userOpenid]);
    }

}
