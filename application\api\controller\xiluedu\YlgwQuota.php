<?php

namespace app\api\controller\xiluedu;

use app\common\controller\Api;
use app\common\library\Sms;
use app\common\model\User;
use app\common\model\xiluedu\CourseOrder;
use app\common\model\xiluedu\UserCourse;
use app\api\model\service\UserInfo;
use think\Db;
use think\Exception;
use think\Validate;

/**
 * 基于套餐额度的养老顾问开通控制器
 */
class YlgwQuota extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * 获取当前用户的开通额度信息
     */
    public function quotaInfo()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $user = $this->auth->getUser();

        // 检查用户权限（必须是城市运营商或养老院长）
        if ($user->is_qydl != 1 && $user->is_sqdl != 1) {
            $this->error('您没有权限开通养老顾问身份');
        }

        // 统计已使用的额度（通过订单记录统计）
        $used_quota = CourseOrder::where('platform', 'quota')
            ->where('course_id', 16)
            ->where('pay_status', 2)
            ->where('user_id', 'in', function($query) use ($user) {
                // 查找由当前用户开通的所有用户
                $query->name('user')->where('parent_id', $user->id)->where('is_ylgw', 1)->field('id');
            })
            ->count();

        $result = [
            'total_quota' => $user->ylgw_quota+$used_quota, // 总额度
            'used_quota' => $used_quota, // 已使用额度
            'remaining_quota' => $user->ylgw_quota, // 剩余额度
            'user_type' => $user->is_qydl == 1 ? '城市运营商' : '养老院长',
        ];

        $this->success('获取额度信息成功', $result);
    }

    /**
     * 基于套餐额度开通养老顾问
     */
    public function openYlgw()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $mobile = $this->request->param('mobile');
        $code = $this->request->param('code');

        if (!$mobile || !$code) {
            $this->error('手机号和验证码不能为空');
        }

        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error('手机号格式错误');
        }

        // 验证短信验证码
        if (!Sms::check($mobile, $code, 'ylgw_quota')) {
            $this->error('验证码不正确');
        }

        $current_user = $this->auth->getUser();

        // 检查当前用户权限（必须是城市运营商或养老院长）
        if ($current_user->is_qydl != 1 && $current_user->is_sqdl != 1) {
            $this->error('您没有权限开通养老顾问身份');
        }

     

        if ($current_user->ylgw_quota <= 0) {
            $this->error('开通额度不足，请先购买套餐');
        }

        // 查找目标用户
        $target_user = User::where('mobile', $mobile)->where('is_service', 0)->find();
        if (!$target_user) {
            // 如果用户不存在，则创建一个虚拟账号
            $salt = \fast\Random::alnum();
            $password = \fast\Random::alnum(6); // 生成一个随机密码
            $params = [
                'mobile'   => $mobile,
                'username' => $mobile,
                'nickname' => '手机用户' . substr($mobile, -4),
                'password' => md5(md5($password) . $salt),
                'salt'     => $salt,
                'avatar'   => '/assets/img/avatar.png', // 默认头像
                'gender'   => 0,
                'status'   => 'normal',
                'is_service' => 0,
                'is_ylgw' => 0,
                'is_sqdl' => 0,
                'is_qydl' => 0,
                'is_shop' => 0,
                'parent_id' => 0, // 初始没有上级
            ];

            $target_user = new User();
            $target_user->save($params);
            
            // 同时在 fa_service_user_info 表中创建一条记录，确保登录流程完整
            UserInfo::create([
                'user_id' => $target_user->id,
                'mobile' => $mobile,
            ]);

            // 重新获取一次，确保数据完整
            $target_user = User::get($target_user->id);
        }

        // 检查目标用户是否已经是养老顾问或更高级别
        if ($target_user->is_ylgw == 1) {
            $this->error('该用户已经是养老顾问');
        }

        if ($target_user->is_sqdl == 1) {
            $this->error('该用户已经是养老院长，无法开通养老顾问');
        }

        if ($target_user->is_qydl == 1) {
            $this->error('该用户已经是城市运营商，无法开通养老顾问');
        }

        // 检查目标用户是否已经有上级
        if ($target_user->parent_id != 0 && $target_user->parent_id != $current_user->id) {
            $this->error('该用户已有其他上级，无法重复绑定');
        }

        Db::startTrans();
        try {
            // 确定最终的上级（如果用户已有上级，需要调整上级关系）
            $final_parent_id = $this->determineFinalParent($target_user, $current_user);

            // 绑定上下级关系
            $target_user->parent_id = $final_parent_id;
            $target_user->is_ylgw = 1;
            $target_user->save();

            // 创建课程订单记录（标记为额度开通）
            $order_data = [
                'platform' => 'quota',
                'user_id' => $target_user->id,
                'order_no' => "Q" . date("YmdHis") . mt_rand(10, 9999),
                'order_trade_no' => "QO" . date("YmdHis") . mt_rand(10, 9999),
                'course_id' => 16,
                'total_price' => 0, // 额度开通不收费
                'pay_price' => 0,
                'pay_status' => 2, // 直接标记为已支付
                'pay_type' => 3, // 额度支付
                'paytime' => time(),
                'ip' => request()->ip(),
                'is_service' => 0,
                'package_type' => 0,
                'package_quantity' => 1,
            ];

            CourseOrder::create($order_data);

            // 创建用户课程关联
            UserCourse::create([
                'user_id' => $target_user->id,
                'course_id' => 16,
                'from_type' => 3, // 额度开通获得
            ]);

               // 检查剩余额度
            $used_quota = User::where('id', $current_user->id)
            ->update(['ylgw_quota'=>$current_user->ylgw_quota - 1]);

            Db::commit();
            $this->success('开通养老顾问身份成功', [
                'target_user' => [
                    'id' => $target_user->id,
                    'mobile' => $target_user->mobile,
                    'nickname' => $target_user->nickname,
                ],
                'remaining_quota' => $current_user->ylgw_quota - 1,
            ]);

        } catch (Exception $e) {
            Db::rollback();
            $this->error('开通失败：' . $e->getMessage());
        }
    }

    /**
     * 获取已开通的养老顾问列表
     */
    public function openedList()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $current_user = $this->auth->getUser();

        // 检查用户权限
        if ($current_user->is_qydl != 1 && $current_user->is_sqdl != 1) {
            $this->error('您没有权限查看此信息');
        }

        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);

        // 查询通过额度开通的养老顾问
        $list = CourseOrder::alias('co')
            ->join('user u', 'co.user_id = u.id')
            ->where('co.platform', 'quota')
            ->where('co.course_id', 16)
            ->where('co.pay_status', 2)
            ->where('u.parent_id', $current_user->id)
            ->field('co.id,co.order_no,co.createtime,u.id as user_id,u.mobile,u.nickname,u.avatar')
            ->order('co.createtime desc')
            ->paginate($limit, false, ['page' => $page]);

        $result = [
            'list' => $list->items(),
            'total' => $list->total(),
            'page' => $page,
            'limit' => $limit,
            // 是否还有更多
            'is_more' => $list->total() > $page * $limit,
        ];

        // 格式化时间
        foreach ($result['list'] as &$item) {
            $item['createtime'] = date('Y-m-d H:i:s', $item['createtime']);
        }

        $this->success('获取列表成功', $result);
    }

    /**
     * 检查手机号是否可以开通
     */
    public function checkMobile()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $mobile = $this->request->param('mobile');

        if (!$mobile) {
            $this->error('手机号不能为空');
        }

        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error('手机号格式错误');
        }

        $current_user = $this->auth->getUser();

        // 检查用户权限
        if ($current_user->is_qydl != 1 && $current_user->is_sqdl != 1) {
            $this->error('您没有权限开通养老顾问身份');
        }

        // 查找目标用户
        $target_user = User::where('mobile', $mobile)->where('is_service', 0)->find();
        if (!$target_user) {
            $this->error('未找到该手机号对应的用户，请确认用户已注册');
        }

        // 检查各种状态
        $checks = [
            'exists' => true,
            'is_ylgw' => $target_user->is_ylgw == 1,
            'is_sqdl' => $target_user->is_sqdl == 1,
            'is_qydl' => $target_user->is_qydl == 1,
            'has_parent' => $target_user->parent_id != 0,
            'is_my_subordinate' => $target_user->parent_id == $current_user->id,
            'can_open' => false,
            'message' => '',
        ];

        if ($checks['is_ylgw']) {
            $checks['message'] = '该用户已经是养老顾问';
        } elseif ($checks['is_sqdl']) {
            $checks['message'] = '该用户已经是养老院长，无法开通养老顾问';
        } elseif ($checks['is_qydl']) {
            $checks['message'] = '该用户已经是城市运营商，无法开通养老顾问';
        } elseif ($checks['has_parent'] && !$checks['is_my_subordinate']) {
            // 如果用户已有上级，但不是当前用户，则不能开通
            $checks['message'] = '该用户已有其他上级，无法重复绑定';
        } else {
            $checks['can_open'] = true;
            if ($checks['is_my_subordinate']) {
                $checks['message'] = '该用户已绑定您为上级，可以开通';
            } else {
                $checks['message'] = '可以开通';
            }
        }

        $this->success('检查完成', [
            'mobile' => $mobile,
            'user_info' => [
                'id' => $target_user->id,
                'nickname' => $target_user->nickname,
                'avatar' => $target_user->avatar,
            ],
            'checks' => $checks,
        ]);
    }

    /**
     * 确定用户的最终上级
     * 如果用户已有上级且上级是养老顾问，则向上查找直到找到养老院长或城市运营商
     *
     * @param User $target_user 目标用户
     * @param User $current_user 当前操作用户
     * @return int 最终上级ID
     */
    private function determineFinalParent($target_user, $current_user)
    {
        // 如果用户没有上级，直接绑定当前操作用户
        if (!$target_user->parent_id || $target_user->parent_id == 0) {
            return $current_user->id;
        }

        // 如果用户已经绑定了当前操作用户，保持不变
        if ($target_user->parent_id == $current_user->id) {
            return $current_user->id;
        }

        // 用户已有其他上级，需要判断上级类型
        $current_parent_id = $target_user->parent_id;
        $final_parent_id = $this->findTopLevelParent($current_parent_id);

        \think\Log::info("开通养老顾问上级调整 - 用户ID: {$target_user->id}, 原上级: {$current_parent_id}, 最终上级: {$final_parent_id}");

        return $final_parent_id;
    }

    /**
     * 向上查找最高级别的上级（养老院长或城市运营商）
     *
     * @param int $user_id 起始用户ID
     * @return int 最终上级ID
     */
    private function findTopLevelParent($user_id)
    {
        $max_depth = 10; // 防止无限循环
        $current_depth = 0;
        $current_user_id = $user_id;

        while ($current_depth < $max_depth) {
            $user = User::where('id', $current_user_id)->field('id,parent_id,is_ylgw,is_sqdl,is_qydl,nickname')->find();

            if (!$user) {
                // 用户不存在，返回平台
                \think\Log::warning("查找上级时用户不存在 - 用户ID: {$current_user_id}");
                return 0;
            }

            // 如果当前用户是养老院长或城市运营商，返回这个用户
            if ($user['is_sqdl'] == 1 || $user['is_qydl'] == 1) {
                $role = $user['is_sqdl'] == 1 ? '养老院长' : '城市运营商';
                \think\Log::info("找到最终上级 - 用户ID: {$user['id']}, 角色: {$role}, 昵称: {$user['nickname']}");
                return $user['id'];
            }

            // 如果当前用户是养老顾问，继续向上查找
            if ($user['is_ylgw'] == 1) {
                if (!$user['parent_id'] || $user['parent_id'] == 0) {
                    // 养老顾问没有上级，返回平台
                    \think\Log::info("养老顾问没有上级，返回平台 - 用户ID: {$user['id']}");
                    return 0;
                }

                \think\Log::info("继续向上查找 - 当前用户ID: {$user['id']} (养老顾问), 上级ID: {$user['parent_id']}");
                $current_user_id = $user['parent_id'];
                $current_depth++;
                continue;
            }

            // 如果是普通用户，返回平台
            \think\Log::info("遇到普通用户，返回平台 - 用户ID: {$user['id']}");
            return 0;
        }

        // 超过最大深度，返回平台
        \think\Log::warning("查找上级超过最大深度，返回平台 - 起始用户ID: {$user_id}");
        return 0;
    }
}
