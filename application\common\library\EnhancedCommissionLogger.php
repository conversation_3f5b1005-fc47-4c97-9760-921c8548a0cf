<?php

namespace app\common\library;

use app\common\model\CommissionLog;
use app\common\model\User;
use app\common\model\MoneyLog;
use think\Db;

/**
 * 增强版分佣日志记录助手类
 * 详细记录每一步分佣计算过程
 */
class EnhancedCommissionLogger
{
    /**
     * 记录完整的分佣过程
     * @param array $orderInfo 订单信息
     * @param array $commissionSteps 分佣步骤
     * @return bool
     */
    public static function recordFullCommissionProcess($orderInfo, $commissionSteps)
    {
        try {
            $batchNo = self::generateBatchNo($orderInfo['order_type'], $orderInfo['order_id']);
            $stepNo = 1;
            
            foreach ($commissionSteps as $step) {
                self::recordSingleCommissionStep($batchNo, $stepNo, $orderInfo, $step);
                $stepNo++;
            }
            
            return true;
        } catch (\Exception $e) {
            \think\Log::error('分佣日志记录失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 记录单个分佣步骤
     */
    private static function recordSingleCommissionStep($batchNo, $stepNo, $orderInfo, $step)
    {
        // 获取用户信息
        $commissionUser = User::where('id', $step['user_id'])->field('id,parent_id,is_sqdl,is_qydl,is_ylgw,money')->find();
        $buyer = User::where('id', $orderInfo['buyer_user_id'])->field('id,is_sqdl,is_qydl,is_ylgw')->find();
        
        // 计算详细信息
        $calculationDetails = [
            'step_description' => $step['description'],
            'base_amount' => $step['base_amount'],
            'rate' => $step['rate'],
            'calculation_formula' => $step['calculation_formula'],
            'result_amount' => $step['amount'],
            'config_source' => $step['config_source'] ?? 'global',
            'config_key' => $step['config_key'] ?? '',
            'config_value' => $step['config_value'] ?? '',
            'special_rules' => $step['special_rules'] ?? []
        ];
        
        // 记录余额变化
        $beforeBalance = $commissionUser ? $commissionUser['money'] : 0;
        $afterBalance = $beforeBalance;
        $moneyLogId = 0;
        
        if ($step['is_distributed'] && $step['distribution_type'] == 'online') {
            $afterBalance = $beforeBalance + $step['amount'];
            // 这里应该关联实际的money_log记录ID
            $moneyLogId = $step['money_log_id'] ?? 0;
        }
        
        // 插入分佣日志
        $logData = [
            'batch_no' => $batchNo,
            'step_no' => $stepNo,
            'order_type' => $orderInfo['order_type'],
            'order_id' => $orderInfo['order_id'],
            'order_no' => $orderInfo['order_no'] ?? '',
            'goods_id' => $orderInfo['goods_id'] ?? 0,
            'goods_name' => $orderInfo['goods_name'] ?? '',
            'order_amount' => $orderInfo['order_amount'],
            'payment_method' => $orderInfo['payment_method'] ?? 'wechat',
            'balance_amount' => $orderInfo['balance_amount'] ?? 0,
            'online_payment_amount' => $orderInfo['online_payment_amount'] ?? $orderInfo['order_amount'],
            'base_amount' => $step['base_amount'],
            'commission_user_id' => $step['user_id'],
            'commission_user_role' => self::getUserRole($commissionUser),
            'buyer_user_id' => $orderInfo['buyer_user_id'],
            'buyer_user_role' => self::getUserRole($buyer),
            'commission_type' => $step['commission_type'],
            'commission_rate' => $step['rate'],
            'commission_amount' => $step['amount'],
            'platform_profit' => $step['platform_profit'] ?? 0,
            'total_distributed' => $step['total_distributed'] ?? 0,
            'calculation_rule' => $step['description'],
            'calculation_details' => json_encode($calculationDetails, JSON_UNESCAPED_UNICODE),
            'config_source' => $step['config_source'] ?? 'global',
            'config_value' => $step['config_value'] ?? '',
            'is_distributed' => $step['is_distributed'] ? 1 : 0,
            'distribution_type' => $step['distribution_type'] ?? 'online',
            'distribution_status' => $step['distribution_status'] ?? 'pending',
            'distribution_memo' => $step['distribution_memo'] ?? '',
            'money_log_id' => $moneyLogId,
            'before_balance' => $beforeBalance,
            'after_balance' => $afterBalance,
            'parent_user_id' => $commissionUser['parent_id'] ?? 0,
            'parent_user_role' => self::getParentRole($commissionUser),
            'has_superior' => self::checkHasSuperior($commissionUser) ? 1 : 0,
            'createtime' => time()
        ];
        
        CommissionLog::create($logData);
    }
    
    /**
     * 记录课程分佣过程
     */
    public static function recordCourseCommission($order, $courseInfo, $paymentInfo = [])
    {
        // 检查是否为套餐订单
        if (isset($order['package_id']) && $order['package_id'] > 0) {
            return self::recordCoursePackageCommission($order, $courseInfo, $paymentInfo);
        }

        $orderInfo = [
            'order_type' => 'course',
            'order_id' => $order['id'],
            'order_no' => $order['order_no'] ?? '',
            'goods_id' => $order['course_id'],
            'goods_name' => $courseInfo['name'] ?? '课程',
            'order_amount' => $order['total_price'],
            'buyer_user_id' => $order['user_id'],
            'payment_method' => $paymentInfo['payment_method'] ?? 'wechat',
            'balance_amount' => $paymentInfo['balance_amount'] ?? 0,
            'online_payment_amount' => $paymentInfo['online_payment_amount'] ?? $order['total_price']
        ];

        // 获取分佣配置
        $teacherRate = $courseInfo['service_bili'] ?? config('site.course_teacher_rate', 20);
        $sqRate = $courseInfo['shequ_bili'] ?? config('site.course_shequ_rate', 50);
        $qyRate = $courseInfo['quyu_bili'] ?? config('site.course_quyu_rate', 10);
        $ylgwRate = config('site.course_ylgw_commission_rate', 50);

        // 计算分佣步骤
        $commissionSteps = self::calculateCourseCommissionSteps($order, $teacherRate, $sqRate, $qyRate, $ylgwRate);

        return self::recordFullCommissionProcess($orderInfo, $commissionSteps);
    }

    /**
     * 记录课程套餐分佣过程（特殊逻辑）
     */
    public static function recordCoursePackageCommission($order, $courseInfo, $paymentInfo = [])
    {
        $orderInfo = [
            'order_type' => 'course_package',
            'order_id' => $order['id'],
            'order_no' => $order['order_no'] ?? '',
            'goods_id' => $order['course_id'],
            'goods_name' => ($courseInfo['name'] ?? '课程') . '套餐',
            'order_amount' => $order['total_price'],
            'buyer_user_id' => $order['user_id'],
            'payment_method' => $paymentInfo['payment_method'] ?? 'wechat',
            'balance_amount' => $paymentInfo['balance_amount'] ?? 0,
            'online_payment_amount' => $paymentInfo['online_payment_amount'] ?? $order['total_price']
        ];

        // 计算套餐分佣步骤
        $commissionSteps = self::calculatePackageCommissionSteps($order);

        return self::recordFullCommissionProcess($orderInfo, $commissionSteps);
    }
    
    /**
     * 计算课程分佣步骤
     */
    private static function calculateCourseCommissionSteps($order, $teacherRate, $sqRate, $qyRate, $ylgwRate)
    {
        $steps = [];
        $orderAmount = $order['total_price'];
        
        // 第一步：老师分佣
        $teacherMoney = self::truncateDecimal($orderAmount * ($teacherRate / 100));
        $residue = $orderAmount - $teacherMoney;
        
        $steps[] = [
            'user_id' => 0, // 老师ID需要从其他地方获取
            'commission_type' => 'teacher',
            'rate' => $teacherRate,
            'base_amount' => $orderAmount,
            'amount' => $teacherMoney,
            'description' => "老师分佣：订单总额 {$orderAmount} × {$teacherRate}% = {$teacherMoney}",
            'calculation_formula' => "{$orderAmount} × {$teacherRate}% = {$teacherMoney}",
            'config_source' => 'global',
            'config_key' => 'course_teacher_rate',
            'config_value' => $teacherRate . '%',
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];
        
        // 第二步：养老院长分佣
        $sqMoney = self::truncateDecimal($residue * ($sqRate / 100));
        
        $steps[] = [
            'user_id' => 0, // 需要查找养老院长
            'commission_type' => 'direct',
            'rate' => $sqRate,
            'base_amount' => $residue,
            'amount' => $sqMoney,
            'description' => "养老院长分佣：剩余金额 {$residue} × {$sqRate}% = {$sqMoney}",
            'calculation_formula' => "{$residue} × {$sqRate}% = {$sqMoney}",
            'config_source' => 'global',
            'config_key' => 'course_shequ_rate',
            'config_value' => $sqRate . '%',
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];
        
        // 第三步：城市运营商分佣
        $qyMoney = self::truncateDecimal($residue * ($qyRate / 100));
        
        $steps[] = [
            'user_id' => 0, // 需要查找城市运营商
            'commission_type' => 'direct',
            'rate' => $qyRate,
            'base_amount' => $residue,
            'amount' => $qyMoney,
            'description' => "城市运营商分佣：剩余金额 {$residue} × {$qyRate}% = {$qyMoney}",
            'calculation_formula' => "{$residue} × {$qyRate}% = {$qyMoney}",
            'config_source' => 'global',
            'config_key' => 'course_qy_rate',
            'config_value' => $qyRate . '%',
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];
        
        // 第四步：养老顾问分佣
        $ylgwMoney = self::truncateDecimal($sqMoney * ($ylgwRate / 100));
        
        $steps[] = [
            'user_id' => 0, // 需要查找养老顾问
            'commission_type' => 'indirect',
            'rate' => $ylgwRate,
            'base_amount' => $sqMoney,
            'amount' => $ylgwMoney,
            'description' => "养老顾问分佣：养老院长分佣 {$sqMoney} × {$ylgwRate}% = {$ylgwMoney}",
            'calculation_formula' => "{$sqMoney} × {$ylgwRate}% = {$ylgwMoney}",
            'config_source' => 'global',
            'config_key' => 'course_ylgw_commission_rate',
            'config_value' => $ylgwRate . '%',
            'special_rules' => ['基于养老院长分佣金额计算'],
            'is_distributed' => false, // 根据是否有上级决定
            'distribution_type' => 'offline',
            'distribution_status' => 'pending'
        ];
        
        // 第五步：平台利润
        $platformProfit = $residue - $sqMoney - $qyMoney;
        $totalDistributed = $teacherMoney + $sqMoney + $qyMoney + $ylgwMoney;
        
        $steps[] = [
            'user_id' => 0, // 平台
            'commission_type' => 'direct',
            'rate' => 0,
            'base_amount' => $residue,
            'amount' => $platformProfit,
            'description' => "平台利润：剩余金额 {$residue} - 养老院长分佣 {$sqMoney} - 城市运营商分佣 {$qyMoney} = {$platformProfit}",
            'calculation_formula' => "{$residue} - {$sqMoney} - {$qyMoney} = {$platformProfit}",
            'config_source' => 'calculated',
            'platform_profit' => $platformProfit,
            'total_distributed' => $totalDistributed,
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];
        
        return $steps;
    }
    
    /**
     * 生成批次号
     */
    private static function generateBatchNo($orderType, $orderId)
    {
        return $orderType . '_' . $orderId . '_' . time();
    }
    
    /**
     * 获取用户角色
     */
    private static function getUserRole($user)
    {
        if (!$user) return 'platform';
        
        if ($user['is_qydl'] == 1) return 'city_manager';
        if ($user['is_sqdl'] == 1) return 'nursing_home_director';
        if ($user['is_ylgw'] == 1) return 'elderly_advisor';
        
        return 'regular_user';
    }
    
    /**
     * 获取上级角色
     */
    private static function getParentRole($user)
    {
        if (!$user || !$user['parent_id']) return 'platform';
        
        $parent = User::where('id', $user['parent_id'])->field('is_sqdl,is_qydl,is_ylgw')->find();
        return self::getUserRole($parent);
    }
    
    /**
     * 检查是否有上级
     */
    private static function checkHasSuperior($user)
    {
        if (!$user || !$user['parent_id'] || $user['parent_id'] <= 0) {
            return false;
        }
        
        $parent = User::where('id', $user['parent_id'])->field('is_sqdl,is_qydl')->find();
        if (!$parent) {
            return false;
        }
        
        return ($parent['is_sqdl'] == 1 || $parent['is_qydl'] == 1);
    }
    
    /**
     * 计算套餐分佣步骤（特殊逻辑）
     */
    private static function calculatePackageCommissionSteps($order)
    {
        $steps = [];
        $orderAmount = $order['total_price'];

        // 获取购买用户信息
        $buyer = User::where('id', $order['user_id'])->field('id,parent_id,is_sqdl,is_qydl,is_ylgw')->find();
        if (!$buyer) {
            return $steps;
        }

        // 获取套餐信息
        $packageInfo = null;
        if (isset($order['package_id']) && $order['package_id'] > 0) {
            $packageInfo = Db::name('xiluedu_course_package')->where('id', $order['package_id'])->find();
        }

        // 套餐分佣逻辑：
        // 1. 如果养老院长购买套餐，且有上级城市运营商，则分佣 = 城市运营商价格 - 养老院长价格
        // 2. 如果养老院长购买套餐，但没有上级，则全部归平台
        // 3. 如果城市运营商购买套餐，则全部归平台

        if ($buyer['is_sqdl'] == 1) {
            // 养老院长购买套餐
            $steps[] = self::calculateNursingHomeDirectorPackageCommission($buyer, $orderAmount, $packageInfo);
        } elseif ($buyer['is_qydl'] == 1) {
            // 城市运营商购买套餐
            $steps[] = self::calculateCityManagerPackageCommission($buyer, $orderAmount, $packageInfo);
        } else {
            // 其他用户购买套餐（按普通逻辑处理）
            $steps[] = [
                'user_id' => 0, // 平台
                'commission_type' => 'direct',
                'rate' => 100,
                'base_amount' => $orderAmount,
                'amount' => $orderAmount,
                'description' => "普通用户购买套餐：全部归平台 {$orderAmount}",
                'calculation_formula' => "{$orderAmount} × 100% = {$orderAmount}",
                'config_source' => 'special_rule',
                'config_key' => 'package_platform_profit',
                'config_value' => '100%',
                'special_rules' => ['普通用户购买套餐，全部归平台'],
                'platform_profit' => $orderAmount,
                'total_distributed' => 0,
                'is_distributed' => true,
                'distribution_type' => 'online',
                'distribution_status' => 'success'
            ];
        }

        return $steps;
    }

    /**
     * 计算养老院长套餐分佣
     */
    private static function calculateNursingHomeDirectorPackageCommission($buyer, $orderAmount, $packageInfo)
    {
        // 检查是否有上级城市运营商
        $hasParentCityManager = false;
        $parentUser = null;

        if ($buyer['parent_id'] > 0) {
            $parentUser = User::where('id', $buyer['parent_id'])->field('id,is_qydl')->find();
            if ($parentUser && $parentUser['is_qydl'] == 1) {
                $hasParentCityManager = true;
            }
        }

        if ($hasParentCityManager && $packageInfo) {
            // 有上级城市运营商，计算差价分佣
            $cityManagerPrice = floatval($packageInfo['qydl_price'] ?? 0);
            $nursingHomePrice = floatval($packageInfo['sqdl_price'] ?? 0);
            $commissionAmount = $cityManagerPrice - $nursingHomePrice;

            if ($commissionAmount > 0) {
                return [
                    'user_id' => $parentUser['id'],
                    'commission_type' => 'direct',
                    'rate' => 0, // 不是按比例，是按差价
                    'base_amount' => $orderAmount,
                    'amount' => $commissionAmount,
                    'description' => "城市运营商套餐分佣：城市运营商价格 {$cityManagerPrice} - 养老院长价格 {$nursingHomePrice} = {$commissionAmount}",
                    'calculation_formula' => "{$cityManagerPrice} - {$nursingHomePrice} = {$commissionAmount}",
                    'config_source' => 'special_rule',
                    'config_key' => 'package_price_difference',
                    'config_value' => "城市运营商:{$cityManagerPrice}, 养老院长:{$nursingHomePrice}",
                    'special_rules' => [
                        '养老院长购买套餐，有上级城市运营商',
                        '分佣金额 = 城市运营商套餐价格 - 养老院长套餐价格'
                    ],
                    'platform_profit' => $orderAmount - $commissionAmount,
                    'total_distributed' => $commissionAmount,
                    'is_distributed' => true,
                    'distribution_type' => 'online',
                    'distribution_status' => 'success'
                ];
            }
        }

        // 没有上级或差价为0，全部归平台
        return [
            'user_id' => 0, // 平台
            'commission_type' => 'direct',
            'rate' => 100,
            'base_amount' => $orderAmount,
            'amount' => $orderAmount,
            'description' => $hasParentCityManager ?
                "养老院长购买套餐：差价为0，全部归平台 {$orderAmount}" :
                "养老院长购买套餐：无上级城市运营商，全部归平台 {$orderAmount}",
            'calculation_formula' => "{$orderAmount} × 100% = {$orderAmount}",
            'config_source' => 'special_rule',
            'config_key' => 'package_platform_profit',
            'config_value' => '100%',
            'special_rules' => [
                $hasParentCityManager ? '有上级但差价为0' : '无上级城市运营商',
                '全部归平台'
            ],
            'platform_profit' => $orderAmount,
            'total_distributed' => 0,
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];
    }

    /**
     * 计算城市运营商套餐分佣
     */
    private static function calculateCityManagerPackageCommission($buyer, $orderAmount, $packageInfo)
    {
        // 城市运营商购买套餐，全部归平台
        return [
            'user_id' => 0, // 平台
            'commission_type' => 'direct',
            'rate' => 100,
            'base_amount' => $orderAmount,
            'amount' => $orderAmount,
            'description' => "城市运营商购买套餐：全部归平台 {$orderAmount}",
            'calculation_formula' => "{$orderAmount} × 100% = {$orderAmount}",
            'config_source' => 'special_rule',
            'config_key' => 'package_platform_profit',
            'config_value' => '100%',
            'special_rules' => ['城市运营商购买套餐，全部归平台'],
            'platform_profit' => $orderAmount,
            'total_distributed' => 0,
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];
    }

    /**
     * 记录商城分佣过程
     */
    public static function recordWanlshopCommission($order, $goodsInfo, $paymentInfo = [])
    {
        $orderInfo = [
            'order_type' => 'wanlshop',
            'order_id' => $order['id'],
            'order_no' => $order['order_no'] ?? '',
            'goods_id' => $goodsInfo['id'] ?? 0,
            'goods_name' => $goodsInfo['name'] ?? '商城商品',
            'order_amount' => $order['total_price'],
            'buyer_user_id' => $order['user_id'],
            'payment_method' => $paymentInfo['payment_method'] ?? 'wechat',
            'balance_amount' => $paymentInfo['balance_amount'] ?? 0,
            'online_payment_amount' => $paymentInfo['online_payment_amount'] ?? $order['total_price']
        ];

        // 计算商城分佣步骤（基于利润）
        $profit = $order['total_price'] - ($goodsInfo['cost_price'] ?? 0);
        $commissionSteps = self::calculateWanlshopCommissionSteps($order, $goodsInfo, $profit);

        return self::recordFullCommissionProcess($orderInfo, $commissionSteps);
    }

    /**
     * 计算商城分佣步骤
     */
    private static function calculateWanlshopCommissionSteps($order, $goodsInfo, $profit)
    {
        $steps = [];

        // 获取分佣配置
        $sqRate = $goodsInfo['shequ_bili'] ?? config('site.wanlshop_shequ_rate', 10);
        $qyRate = $goodsInfo['quyu_bili'] ?? config('site.wanlshop_quyu_rate', 10);

        // 养老院长分佣
        $sqMoney = self::truncateDecimal($profit * ($sqRate / 100));
        $steps[] = [
            'user_id' => 0, // 需要查找养老院长
            'commission_type' => 'direct',
            'rate' => $sqRate,
            'base_amount' => $profit,
            'amount' => $sqMoney,
            'description' => "养老院长分佣：商品利润 {$profit} × {$sqRate}% = {$sqMoney}",
            'calculation_formula' => "{$profit} × {$sqRate}% = {$sqMoney}",
            'config_source' => isset($goodsInfo['shequ_bili']) ? 'goods_level' : 'global',
            'config_key' => 'wanlshop_shequ_rate',
            'config_value' => $sqRate . '%',
            'special_rules' => ['基于商品利润计算', '利润 = 养老顾问支付价格 - 养老院长优惠价格'],
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];

        // 城市运营商分佣
        $qyMoney = self::truncateDecimal($profit * ($qyRate / 100));
        $steps[] = [
            'user_id' => 0, // 需要查找城市运营商
            'commission_type' => 'direct',
            'rate' => $qyRate,
            'base_amount' => $profit,
            'amount' => $qyMoney,
            'description' => "城市运营商分佣：商品利润 {$profit} × {$qyRate}% = {$qyMoney}",
            'calculation_formula' => "{$profit} × {$qyRate}% = {$qyMoney}",
            'config_source' => isset($goodsInfo['quyu_bili']) ? 'goods_level' : 'global',
            'config_key' => 'wanlshop_quyu_rate',
            'config_value' => $qyRate . '%',
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];

        // 养老顾问分佣（基于利润的50%）
        $ylgwMoney = self::truncateDecimal($profit * 0.5);
        $steps[] = [
            'user_id' => 0, // 需要查找养老顾问
            'commission_type' => 'indirect',
            'rate' => 50,
            'base_amount' => $profit,
            'amount' => $ylgwMoney,
            'description' => "养老顾问分佣：商品利润 {$profit} × 50% = {$ylgwMoney}",
            'calculation_formula' => "{$profit} × 50% = {$ylgwMoney}",
            'config_source' => 'special_rule',
            'config_key' => 'wanlshop_ylgw_rate',
            'config_value' => '50%',
            'special_rules' => ['固定基于商品利润的50%计算'],
            'is_distributed' => false, // 根据是否有上级决定
            'distribution_type' => 'offline',
            'distribution_status' => 'pending'
        ];

        return $steps;
    }

    /**
     * 记录服务分佣过程
     */
    public static function recordServiceCommission($order, $serviceInfo, $paymentInfo = [])
    {
        $orderInfo = [
            'order_type' => 'service',
            'order_id' => $order['id'],
            'order_no' => $order['orderId'] ?? '',
            'goods_id' => $order['goods_id'],
            'goods_name' => $serviceInfo['name'] ?? '服务',
            'order_amount' => $order['sumprice'],
            'buyer_user_id' => $order['user_id'],
            'payment_method' => $paymentInfo['payment_method'] ?? 'wechat',
            'balance_amount' => $paymentInfo['balance_amount'] ?? 0,
            'online_payment_amount' => $paymentInfo['online_payment_amount'] ?? $order['sumprice']
        ];

        // 计算服务分佣步骤
        $commissionSteps = self::calculateServiceCommissionSteps($order, $serviceInfo);

        return self::recordFullCommissionProcess($orderInfo, $commissionSteps);
    }

    /**
     * 计算服务分佣步骤
     */
    private static function calculateServiceCommissionSteps($order, $serviceInfo)
    {
        $steps = [];
        $orderAmount = $order['sumprice'];

        // 获取分佣配置
        $serviceRate = $serviceInfo['service_bili'] ?? config('site.fwz_bili', 20);
        $sqRate = $serviceInfo['shequ_bili'] ?? config('site.service_shequ_rate', 50);
        $qyRate = $serviceInfo['quyu_bili'] ?? config('site.service_quyu_rate', 10);

        // 第一步：服务者分佣
        $serviceMoney = self::truncateDecimal($orderAmount * ($serviceRate / 100));
        $residue = $orderAmount - $serviceMoney;

        $steps[] = [
            'user_id' => 0, // 服务者ID需要从其他地方获取
            'commission_type' => 'service',
            'rate' => $serviceRate,
            'base_amount' => $orderAmount,
            'amount' => $serviceMoney,
            'description' => "服务者分佣：订单总额 {$orderAmount} × {$serviceRate}% = {$serviceMoney}",
            'calculation_formula' => "{$orderAmount} × {$serviceRate}% = {$serviceMoney}",
            'config_source' => isset($serviceInfo['service_bili']) ? 'goods_level' : 'global',
            'config_key' => 'fwz_bili',
            'config_value' => $serviceRate . '%',
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];

        // 第二步：养老院长分佣
        $sqMoney = self::truncateDecimal($residue * ($sqRate / 100));

        $steps[] = [
            'user_id' => 0, // 需要查找养老院长
            'commission_type' => 'direct',
            'rate' => $sqRate,
            'base_amount' => $residue,
            'amount' => $sqMoney,
            'description' => "养老院长分佣：剩余金额 {$residue} × {$sqRate}% = {$sqMoney}",
            'calculation_formula' => "{$residue} × {$sqRate}% = {$sqMoney}",
            'config_source' => isset($serviceInfo['shequ_bili']) ? 'goods_level' : 'global',
            'config_key' => 'service_shequ_rate',
            'config_value' => $sqRate . '%',
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];

        // 第三步：城市运营商分佣
        $qyMoney = self::truncateDecimal($residue * ($qyRate / 100));

        $steps[] = [
            'user_id' => 0, // 需要查找城市运营商
            'commission_type' => 'direct',
            'rate' => $qyRate,
            'base_amount' => $residue,
            'amount' => $qyMoney,
            'description' => "城市运营商分佣：剩余金额 {$residue} × {$qyRate}% = {$qyMoney}",
            'calculation_formula' => "{$residue} × {$qyRate}% = {$qyMoney}",
            'config_source' => isset($serviceInfo['quyu_bili']) ? 'goods_level' : 'global',
            'config_key' => 'service_quyu_rate',
            'config_value' => $qyRate . '%',
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];

        // 第四步：养老顾问分佣（基于养老院长分佣的50%）
        $ylgwMoney = self::truncateDecimal($sqMoney * 0.5);

        $steps[] = [
            'user_id' => 0, // 需要查找养老顾问
            'commission_type' => 'indirect',
            'rate' => 50,
            'base_amount' => $sqMoney,
            'amount' => $ylgwMoney,
            'description' => "养老顾问分佣：养老院长分佣 {$sqMoney} × 50% = {$ylgwMoney}",
            'calculation_formula' => "{$sqMoney} × 50% = {$ylgwMoney}",
            'config_source' => 'special_rule',
            'config_key' => 'service_ylgw_rate',
            'config_value' => '50%',
            'special_rules' => ['基于养老院长分佣金额的50%计算'],
            'is_distributed' => false, // 根据是否有上级决定
            'distribution_type' => 'offline',
            'distribution_status' => 'pending'
        ];

        // 第五步：平台利润
        $platformProfit = $residue - $sqMoney - $qyMoney;
        $totalDistributed = $serviceMoney + $sqMoney + $qyMoney + $ylgwMoney;

        $steps[] = [
            'user_id' => 0, // 平台
            'commission_type' => 'direct',
            'rate' => 0,
            'base_amount' => $residue,
            'amount' => $platformProfit,
            'description' => "平台利润：剩余金额 {$residue} - 养老院长分佣 {$sqMoney} - 城市运营商分佣 {$qyMoney} = {$platformProfit}",
            'calculation_formula' => "{$residue} - {$sqMoney} - {$qyMoney} = {$platformProfit}",
            'config_source' => 'calculated',
            'platform_profit' => $platformProfit,
            'total_distributed' => $totalDistributed,
            'is_distributed' => true,
            'distribution_type' => 'online',
            'distribution_status' => 'success'
        ];

        return $steps;
    }

    /**
     * 记录余额支付详情
     */
    public static function recordBalancePayment($orderType, $orderId, $orderNo, $userId, $balanceUsed, $onlinePayment, $totalAmount, $paymentMethod = 'wechat')
    {
        try {
            $user = User::where('id', $userId)->field('money')->find();
            $balanceBefore = $user ? $user['money'] + $balanceUsed : $balanceUsed; // 支付前余额
            $balanceAfter = $user ? $user['money'] : 0; // 支付后余额

            $logData = [
                'order_type' => $orderType,
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'user_id' => $userId,
                'balance_before' => $balanceBefore,
                'balance_used' => $balanceUsed,
                'balance_after' => $balanceAfter,
                'online_payment' => $onlinePayment,
                'total_amount' => $totalAmount,
                'payment_method' => $paymentMethod,
                'memo' => "订单支付：余额 {$balanceUsed} + {$paymentMethod} {$onlinePayment} = 总计 {$totalAmount}",
                'createtime' => time()
            ];

            Db::name('balance_payment_log')->insert($logData);
            return true;
        } catch (\Exception $e) {
            \think\Log::error('余额支付日志记录失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 截取小数
     */
    private static function truncateDecimal($number, $decimals = 2)
    {
        $factor = pow(10, $decimals);
        return floor($number * $factor) / $factor;
    }
}
