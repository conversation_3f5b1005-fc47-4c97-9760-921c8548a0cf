<?php
/**
 * 测试课程16分佣修复效果
 * 验证旧的分佣逻辑不再运行
 */

echo "=== 课程16分佣修复验证 ===\n\n";

echo "## 修复内容总结\n\n";

echo "### 1. 问题根源\n";
echo "- CourseOrder::shareProfit()方法仍然在处理课程16分佣\n";
echo "- 第524行给养老顾问增加ylgw_total_commission\n";
echo "- 导致无上级的养老顾问也会获得ylgw_total_commission\n\n";

echo "### 2. 修复方案\n";
echo "- 在CourseOrder::shareProfit()方法开头添加课程16检查\n";
echo "- 如果是课程16，直接返回，不执行旧分佣逻辑\n";
echo "- 确保课程16完全使用新的统一分佣逻辑\n\n";

echo "### 3. 修复代码\n";
echo "```php\n";
echo "// 课程16使用统一的新分佣逻辑，不走这个旧方法\n";
echo "if(\$order['course_id'] == 16) {\n";
echo "    \\think\\Log::info(\"课程16跳过旧分佣逻辑 - 订单ID: {\$order_id}\");\n";
echo "    return;\n";
echo "}\n";
echo "```\n\n";

echo "### 4. 分佣流程对比\n\n";

echo "#### 修复前（错误）：\n";
echo "1. 钩子中执行新分佣逻辑 ✅\n";
echo "2. CourseOrder::shareProfit()再次执行旧分佣逻辑 ❌\n";
echo "3. 结果：无上级养老顾问获得ylgw_total_commission ❌\n\n";

echo "#### 修复后（正确）：\n";
echo "1. 钩子中执行新分佣逻辑 ✅\n";
echo "2. CourseOrder::shareProfit()检测到课程16，直接返回 ✅\n";
echo "3. 结果：无上级养老顾问ylgw_total_commission为0 ✅\n\n";

echo "### 5. 7种情况验证\n\n";

$scenarios = [
    1 => "城市运营商A -> 养老顾问B -> 用户C成为养老顾问",
    2 => "城市运营商A -> 养老院长B -> 养老顾问C -> 用户D成为养老顾问", 
    3 => "养老院长A -> 养老顾问B -> 用户C成为养老顾问",
    4 => "养老院长A -> 用户B成为养老顾问",
    5 => "养老顾问A(上级是0) -> 用户B成为养老顾问",
    6 => "普通用户A -> 用户B成为养老顾问",
    7 => "城市运营商A -> 养老院长B -> 用户C成为养老顾问"
];

foreach($scenarios as $num => $desc) {
    echo "**情况{$num}**: {$desc}\n";
    
    switch($num) {
        case 1:
            echo "- B获得ylgw_total_commission: 91.25元 ✅\n";
            echo "- A获得money: 219元 ✅\n";
            break;
        case 2:
            echo "- C获得ylgw_total_commission: 91.25元 ✅\n";
            echo "- B获得money: 182.5元 ✅\n";
            echo "- A获得money: 36.5元 ✅\n";
            break;
        case 3:
            echo "- B获得ylgw_total_commission: 91.25元 ✅\n";
            echo "- A获得money: 182.5元 ✅\n";
            break;
        case 4:
            echo "- A获得money: 182.5元 ✅\n";
            break;
        case 5:
            echo "- A获得money: 91.25元 ✅\n";
            echo "- A的ylgw_total_commission: 0元 ✅ **修复重点**\n";
            break;
        case 6:
            echo "- 无分佣 ✅\n";
            break;
        case 7:
            echo "- B获得money: 182.5元 ✅\n";
            echo "- A获得money: 36.5元 ✅\n";
            break;
    }
    echo "\n";
}

echo "### 6. 测试建议\n\n";
echo "1. **清理测试数据**：\n";
echo "   - 重置测试用户的ylgw_total_commission为0\n";
echo "   - 清理相关的money_log记录\n\n";

echo "2. **测试情况5**：\n";
echo "   - 创建无上级的养老顾问A\n";
echo "   - 让普通用户B购买课程16\n";
echo "   - 验证A获得91.25元money，ylgw_total_commission保持为0\n\n";

echo "3. **检查日志**：\n";
echo "   ```bash\n";
echo "   tail -f runtime/log/*/**.log | grep \"课程16跳过旧分佣逻辑\"\n";
echo "   ```\n\n";

echo "4. **验证数据库**：\n";
echo "   ```sql\n";
echo "   -- 检查养老顾问的ylgw_total_commission\n";
echo "   SELECT id, nickname, ylgw_total_commission, money \n";
echo "   FROM fa_user \n";
echo "   WHERE is_ylgw = 1 AND parent_id = 0;\n";
echo "   ```\n\n";

echo "### 7. 预期效果\n\n";
echo "✅ **情况5修复**：无上级养老顾问的ylgw_total_commission保持为0\n";
echo "✅ **避免双重分佣**：课程16不再走旧的分佣逻辑\n";
echo "✅ **统一分佣逻辑**：所有课程16购买都使用新的分佣方法\n";
echo "✅ **日志清晰**：可以通过日志确认旧逻辑被跳过\n\n";

echo "### 8. 注意事项\n\n";
echo "- 此修复只影响课程16，其他课程的分佣逻辑不变\n";
echo "- 新的统一分佣逻辑已经在Course16控制器和钩子中正确实现\n";
echo "- 建议在生产环境部署前进行充分测试\n";
