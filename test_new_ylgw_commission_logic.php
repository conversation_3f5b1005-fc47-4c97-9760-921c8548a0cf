<?php
/**
 * 测试新的养老顾问分佣逻辑
 * 根据用户提供的6种情况进行测试
 */

echo "=== 新的养老顾问分佣逻辑测试 ===\n\n";

// 基础分佣金额
$base_amount = 365; // 课程16价格
$ylgw_base = 91.25;  // 养老顾问基础分佣 (25%)
$sqdl_base = 182.5;  // 养老院长基础分佣 (50%)
$qydl_base = 36.5;   // 城市运营商基础分佣 (10%)

echo "基础分佣金额设置：\n";
echo "- 课程价格: {$base_amount}元\n";
echo "- 养老顾问基础分佣: {$ylgw_base}元 (25%)\n";
echo "- 养老院长基础分佣: {$sqdl_base}元 (50%)\n";
echo "- 城市运营商基础分佣: {$qydl_base}元 (10%)\n\n";

// 情况1：城市运营商A -> 养老顾问B -> 用户C（C购买课程16成为养老顾问）
echo "=== 情况1：城市运营商 -> 养老顾问 -> 用户（用户成为养老顾问） ===\n";
echo "用户关系：城市运营商A -> 养老顾问B -> 用户C\n";
echo "分佣结果：\n";
echo "- B获得ylgw_total_commission: {$ylgw_base}元\n";
echo "- A获得money: 219元 (60%)\n";
echo "- 总分佣: " . ($ylgw_base + 219) . "元\n";
echo "- 平台剩余: " . ($base_amount - $ylgw_base - 219) . "元\n\n";

// 情况2：城市运营商A -> 养老院长B -> 养老顾问C -> 用户D（D购买课程16成为养老顾问）
echo "=== 情况2：城市运营商 -> 养老院长 -> 养老顾问 -> 用户（用户成为养老顾问） ===\n";
echo "用户关系：城市运营商A -> 养老院长B -> 养老顾问C -> 用户D\n";
echo "分佣结果：\n";
echo "- C获得ylgw_total_commission: {$ylgw_base}元\n";
echo "- B获得money: {$sqdl_base}元 (50%)\n";
echo "- A获得money: {$qydl_base}元 (10%)\n";
echo "- 总分佣: " . ($ylgw_base + $sqdl_base + $qydl_base) . "元\n";
echo "- 平台剩余: " . ($base_amount - $ylgw_base - $sqdl_base - $qydl_base) . "元\n\n";

// 情况3：养老院长A -> 养老顾问B -> 用户C（C购买课程16成为养老顾问）
echo "=== 情况3：养老院长 -> 养老顾问 -> 用户（用户成为养老顾问） ===\n";
echo "用户关系：养老院长A -> 养老顾问B -> 用户C\n";
echo "分佣结果：\n";
echo "- B获得ylgw_total_commission: {$ylgw_base}元\n";
echo "- A获得money: {$sqdl_base}元 (50%)\n";
echo "- 总分佣: " . ($ylgw_base + $sqdl_base) . "元\n";
echo "- 平台剩余: " . ($base_amount - $ylgw_base - $sqdl_base) . "元\n\n";

// 情况4：养老院长A -> 用户B（B购买课程16成为养老顾问）
echo "=== 情况4：养老院长 -> 用户（用户成为养老顾问） ===\n";
echo "用户关系：养老院长A -> 用户B\n";
echo "分佣结果：\n";
echo "- A获得money: {$sqdl_base}元 (50%)\n";
echo "- 总分佣: {$sqdl_base}元\n";
echo "- 平台剩余: " . ($base_amount - $sqdl_base) . "元\n\n";

// 情况5：养老顾问A（上级是0） -> 用户B（B购买课程16成为养老顾问）
echo "=== 情况5：养老顾问（无上级） -> 用户（用户成为养老顾问） ===\n";
echo "用户关系：养老顾问A（parent_id=0） -> 用户B\n";
echo "分佣结果：\n";
echo "- A获得money: {$ylgw_base}元 (25%)\n";
echo "- 总分佣: {$ylgw_base}元\n";
echo "- 平台剩余: " . ($base_amount - $ylgw_base) . "元\n\n";

// 情况6：普通用户A -> 用户B（B购买课程16成为养老顾问）
echo "=== 情况6：普通用户 -> 用户（用户成为养老顾问） ===\n";
echo "用户关系：普通用户A -> 用户B\n";
echo "分佣结果：\n";
echo "- 无分佣\n";
echo "- 总分佣: 0元\n";
echo "- 平台剩余: {$base_amount}元\n\n";

echo "=== 分佣逻辑总结 ===\n";
echo "1. 养老顾问如果有上级且非平台，获得的是ylgw_total_commission（记录但不实际到账）\n";
echo "2. 管理层（养老院长、城市运营商）获得的是实际到账的money\n";
echo "3. 分佣比例根据层级关系动态调整\n";
echo "4. 普通用户的下级成为养老顾问时不分佣\n";
echo "5. 养老顾问无上级时，直接获得money到账\n\n";

echo "=== 实现要点 ===\n";
echo "1. 基础分佣金额固定：365元课程价格\n";
echo "2. 分佣对象：购买用户的直接上级\n";
echo "3. 分佣方式根据上级身份和层级关系决定\n";
echo "4. 需要递归检查上级关系，确保分佣到正确的管理层\n";
