<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Enroll_end_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-enroll_end_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[enroll_end_time]" type="text" value="{:$row.enroll_end_time?datetime($row.enroll_end_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('上课时间')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-start_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm" data-use-current="true" name="row[start_time]" placeholder="开始时间" type="text" value="{:$row.start_time?datetime($row.start_time):''}">
        </div>
        <div class="col-xs-12 col-sm-4">
            <input id="c-end_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm" data-use-current="true" name="row[end_time]" placeholder="结束时间" type="text" value="{:$row.end_time?datetime($row.end_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_count')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_count" data-rule="required" class="form-control" name="row[total_count]" type="number" value="{$row.total_count|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address" data-rule="required" class="form-control" name="row[address]" type="text" value="{$row.address|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" data-rule="required" class="form-control" name="row[mobile]" type="tel" value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Thumb_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-thumb_image" data-rule="required" class="form-control" size="50" name="row[thumb_image]" type="text" value="{$row.thumb_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-thumb_image" class="btn btn-danger faupload" data-input-id="c-thumb_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-thumb_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-thumb_image" class="btn btn-primary fachoose" data-input-id="c-thumb_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-thumb_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-thumb_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Salesprice')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-salesprice" min="0.01" data-msg-range="价格最低为0.01" data-rule="required,range(0.01~)" class="form-control" step="0.01" name="row[salesprice]" type="number" value="{$row.salesprice|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Teacher_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-teacher_id" data-rule="required" min="0" data-source="xiluedu/teacher/index" class="form-control selectpage" name="row[teacher_id]" type="text" value="{$row.teacher_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Introduce')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-introduce" data-rule="required" class="form-control editor" name="row[introduce]">{$row.introduce|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" data-rule="required" class="form-control editor" rows="5" name="row[content]" cols="50">{$row.content|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('养老院长分成比例（%）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-shequ_bili" data-rule="" class="form-control" name="row[shequ_bili]" type="number" value="{$row.shequ_bili}"  placeholder="请输入养老院长分成比例">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('城市运营商分成比例（%）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-quyu_bili" data-rule="" class="form-control" name="row[quyu_bili]" type="number" value="{$row.quyu_bili}"  placeholder="请输入城市运营商分成比例">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('老师分成比例（%）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_bili" data-rule="" class="form-control" name="row[service_bili]" type="number" value="{$row.service_bili}"  placeholder="请输入服务者分成比例">
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('一级返佣比例')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input aria-describedby="c-distribution_one_rate" data-rule="integer,range(0~100)" class="form-control" name="row[distribution_one_rate]" type="text" value="{$row.distribution_one_rate}">-->
<!--                <span class="input-group-addon" id="c-distribution_one_rate">%</span>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('二级返佣比例')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input aria-describedby="c-distribution_two_rate"  data-rule="integer,range(0~100)" class="form-control" name="row[distribution_two_rate]" type="text" value="{$row.distribution_two_rate}">-->
<!--                <span class="input-group-addon" id="c-distribution_two_rate">%</span>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('海报图')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-poster_image" data-rule="" class="form-control" size="50" name="row[poster_image]" type="text" value="{$row.poster_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-poster_image" class="btn btn-danger faupload" data-input-id="c-poster_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-poster_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-poster_image" class="btn btn-primary fachoose" data-input-id="c-poster_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-poster_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-poster_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('海报视频')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-poster_video" data-rule="" class="form-control" size="50" name="row[poster_video]" type="text" value="{$row.poster_video|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-poster_video" class="btn btn-danger faupload" data-input-id="c-poster_video" data-mimetype="mp4" data-multiple="false" data-preview-id="p-poster_video"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-poster_video" class="btn btn-primary fachoose" data-input-id="c-poster_video" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-poster_video"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-poster_video"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('养老院长')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
                {foreach name="ylgwList" item="vo"}
                <label for="row[is_ylgw]-{$key}"><input id="row[is_ylgw]-{$key}" name="row[is_ylgw]" type="radio" value="{$key}" {in name="key" value="$row.is_ylgw"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" data-rule="required" class="form-control" name="row[weigh]" type="number" value="{$row.weigh|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
