define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 获取URL参数
            var urlParams = new URLSearchParams(window.location.search);
            var courseId = urlParams.get('course_id');
            
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xiluedu/course_package/index?course_id=' + courseId,
                    add_url: 'xiluedu/course_package/add?course_id=' + courseId,
                    edit_url: 'xiluedu/course_package/edit',
                    del_url: 'xiluedu/course_package/del',
                    multi_url: 'xiluedu/course_package/multi',
                    table: 'xiluedu_course_package',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                sortOrder: 'asc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: 'ID', width: 60},
                        {field: 'name', title: '套餐名称', operate: 'LIKE'},
                        {field: 'description', title: '套餐描述', operate: 'LIKE'},
                        {field: 'quantity', title: '数量', width: 80},
                        {field: 'original_price', title: '原价', width: 100, formatter: function(value) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'qydl_price', title: '城市运营商价格', width: 120, formatter: function(value) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'sqdl_price', title: '养老院长价格', width: 120, formatter: function(value) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'ylgw_price', title: '养老顾问价格', width: 120, formatter: function(value) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'user_types_text', title: '适用人员', width: 150, operate: false},
                        {field: 'sort', title: '排序', width: 80},
                        {field: 'status', title: '状态', width: 80, searchList: {"0":"禁用","1":"启用"}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: '创建时间', width: 160, operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
                
                // 价格输入框格式化
                $('input[type="number"][step="0.01"]').on('blur', function() {
                    var value = parseFloat($(this).val());
                    if (!isNaN(value)) {
                        $(this).val(value.toFixed(2));
                    }
                });
                
                // 计算总价显示
                function calculateTotal() {
                    var quantity = parseInt($('#c-quantity').val()) || 0;
                    var originalPrice = parseFloat($('#c-original_price').val()) || 0;
                    var qydlPrice = parseFloat($('#c-qydl_price').val()) || 0;
                    var sqdlPrice = parseFloat($('#c-sqdl_price').val()) || 0;
                    var ylgwPrice = parseFloat($('#c-ylgw_price').val()) || 0;
                    
                    if (quantity > 0) {
                        var totalText = '';
                        if (originalPrice > 0) {
                            totalText += '普通用户总价: ¥' + (originalPrice ).toFixed(2) + ' ';
                        }
                        if (qydlPrice > 0) {
                            totalText += '城市运营商总价: ¥' + (qydlPrice ).toFixed(2) + ' ';
                        }
                        if (sqdlPrice > 0) {
                            totalText += '养老院长总价: ¥' + (sqdlPrice).toFixed(2) + ' ';
                        }
                        if (ylgwPrice > 0) {
                            totalText += '养老顾问总价: ¥' + (ylgwPrice).toFixed(2);
                        }
                        
                        // 显示总价信息
                        if (!$('#total-price-info').length) {
                            $('#c-quantity').parent().append('<div id="total-price-info" class="help-block" style="color: #5cb85c; font-weight: bold;"></div>');
                        }
                        $('#total-price-info').text(totalText);
                    }
                }
                
                // 绑定计算事件
                $('#c-quantity, #c-original_price, #c-qydl_price, #c-sqdl_price, #c-ylgw_price').on('input blur', calculateTotal);
                
                // 页面加载时计算一次
                setTimeout(calculateTotal, 100);
            }
        }
    };
    return Controller;
});
