<?php
namespace app\api\controller\service;

use app\admin\model\service\order\UseLog;
use app\api\model\service\OrderAddress;
use app\api\model\service\OrderDetail;
use app\api\model\service\Skill;
use app\api\model\wanlshop\OrderGoods;
use app\common\controller\Api;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\DbException;
use think\Log;
class Crontab extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * 更新服务者时间
     * @return void
     */
    public function skillTimeUpdate()
    {
        $starttime = strtotime(date("Y-m-d", time()));
        $where['changetime'] = ['<', $starttime];

        $skilltime = new \app\api\model\service\SkillTime();
        $skilltime->where($where)
            ->where('timetype', 'in', [1, 2])
            ->dec('timetype', 1)
            ->update(['changetime' => time()]);
        $skilltime->where($where)
            ->where('timetype', 0)
            ->inc('starttime', 259200)
            ->inc('endtime', 259200)
            ->update(['timetype' => 2, 'state' => 0, 'changetime' => time()]);
        $this->success('时间已更新');
    }

    /**
     * 结算订单
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function updateOrderSettle()
    {
        $list = \app\api\model\service\Order::where(['is_settle'=>1,'settletime'=>['<',time()]])->field('id,user_id,traveltype,skill_id,shop_id,payprice,act_travel_price,settle_price,skill_percent,shop_percent')->select();
        foreach ($list as $value)
        {
            \app\api\model\service\Rebate::orderRebate($value);
            \app\api\model\service\Order::where(['id'=>$value['id']])->update(['is_settle'=>2,'updatetime'=>time()]);
        }
        $this->success('订单已结算');
    }


    /**
     * 默认好评
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \think\Exception
     */
    public function commentOrder()
    {
        $commentConfig = \app\api\model\service\ProjectConfig::getProjectConfig('comment_day');
        $list = \app\api\model\service\Order::where(['status' => 6, 'finishtime' => ['<', time() - 86400 * $commentConfig]])->field('id,user_id')->select();
        foreach ($list as $value)
        {
            \app\api\model\service\Comment::commentAdd(['order_id'=>$value['id'],'user_id'=>$value['user_id'],'score'=>5,'content'=>'此用户未填写评价内容','images'=>'']);
        }
        $this->success('订单已处理');
    }

    /**
     * 更新优惠券状态
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function updateCoupon()
    {
        $userCoupon = \app\api\model\service\UserCoupon::where(['state'=>0,'exittime'=>['<',time()]])->field('id')->select();

        foreach ($userCoupon as $value)
        {
            \app\api\model\service\UserCoupon::where('id',$value['id'])->update(['state'=>-1]);
        }
        $this->success('已处理');
    }

    /**
     * 更新会员状态
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function updateData()
    {
        $list = \app\api\model\service\UserInfo::where(['is_plus'=>1,'plustime'=>['<',time()]])->field('id')->select();
        foreach ($list as $value)
        {
            \app\api\model\service\UserInfo::where('id',$value['id'])->update(['is_plus'=>0,'plustime'=>time(),'discount'=>100]);
        }
        $cancelConfig = \app\api\model\service\ProjectConfig::getProjectConfig('cancel_minute');
        $list = \app\api\model\service\Order::where(['status' => 0, 'createtime' => ['<', time() - 60 * $cancelConfig]])->field('id,user_id')->select();
        foreach ($list as $value)
        {
            \app\api\model\service\Order::where(['id'=>$value['id']])->update(['status'=>-1,'updatetime'=>time(),'is_pool'=>0]);
        }
        $this->success('信息已更新');
    }

    /**
     * 统计每日数据
     * @return void
     */
    public function updateTotalData()
    {
        \app\api\model\service\TotalData::totalData(1);
        $this->success('信息已统计');
    }


    private function parseServiceTime($json_time)
    {
        if($json_time){
            $config=json_decode($json_time,true);
            if($config['type']==1){
                $week=date('w',strtotime(date('Y-m-d')));
                foreach ($config['data'] as $datum){
                    if($datum['day']==$week){
                        return date('Y-m-d').' '.$datum['time'];
                    }
                }
            }elseif ($config['type']==2){
                $day = date('Y-m-d');
                foreach ($config['data'] as $datum){
                    if($datum['day']==$day){
                        return date('Y-m-d').' '.$datum['time'];
                    }
                }
            }
        }
        return false;
    }
    /**
     * 定死服务包下单
     * <AUTHOR>
     * @date 2024/8/26  下午5:54
     * @notes
     */
    public function dsOrderAdd()
    {

        $orders = \app\api\model\service\Order::where('goods_type', 1)->where('total_cost_sy', '>', '0')->where('createtime','>=',time()-30*86400)->select();
        $today_time = strtotime(date('Y-m-d', time()));
        $str='';
        foreach ($orders as $order) {
            $o = \app\api\model\service\Order::where(['p_order_id' => $order['id'], 'createtime' => ['>=', $today_time]])->find();
            if ($o) continue;
            $detail = OrderDetail::where(['order_id' => $order->id])->find();
            $starttime = $this->parseServiceTime($detail['service_time_json']);
            if (!$starttime) continue;
            Db::startTrans();
            try {
                $order_arr = $order->toArray();
                unset($order_arr['id']);
                $order_arr['status'] = 2;
                $order_arr['createtime'] = time();
                $order_arr['paytime'] = time();
                $order_arr['goods_total_price'] = $order_arr['payprice'] = $order_arr['price'] = $order_arr['sumprice'] = truncateDecimal($order_arr['sumprice'] / $order_arr['total_cost_seconds']);
                $order_arr['coupon_price'] = truncateDecimal($order_arr['coupon_price'] / $order_arr['total_cost_seconds']);
                $order_arr['starttime'] = strtotime($starttime);
                $order_arr['is_fwb'] = 0;
                $order_arr['total_cost_sy'] = 0;
                $order_arr['p_order_id'] = $order['id'];
                $order_arr['total_cost_seconds'] = $order['total_cost_seconds'];
                $order_arr['total_cost_use_num'] = $order['total_cost_use_num'] + 1;

                $ordernew = \app\api\model\service\Order::create($order_arr);

                $orderDetail = OrderDetail::where(['order_id' => $order->id])->find();
                $data_detail = $detail->toArray();
                unset($data_detail['id']);
                $data_detail['order_id'] = $ordernew->id;
                $data_detail['skill_id'] = $ordernew->skill_id;
                $data_detail['sumprice'] = $ordernew['sumprice'];
                $res = $orderDetail->allowField(true)->insert($data_detail);

                $update = [
//                    'total_cost_seconds' => $order['total_cost_seconds'] - 1,
                    'total_cost_use_num' => $order['total_cost_use_num'] + 1,
                    'total_cost_sy' => $order['total_cost_sy'] - 1,
                ];
                \app\api\model\service\Order::where('id', $order->id)->update($update);

                $address = OrderAddress::where(['order_id' => $order->id])->find();

                $address_arr= $address->toArray();
                unset($address_arr['id']);
                $address_arr['order_id'] = $ordernew->id;
                $str.=$ordernew->id.', ';
                OrderAddress::create($address_arr);
                Db::commit();
            } catch (\Exception $e) {
                dump($e->getMessage());
                Db::rollback();
            }

        }
        $this->success('执行完毕 新订单号: '.$str);
    }

    public function orderStatus()
    {
        $list=\app\api\model\wanlshop\Order::where('createtime','<',time()-30*60)->where('state',1)->select();
        $order_ids='';
        foreach ($list as $value){
            $value->state=7;
            $value->save();
            $order_ids.=$value->id.',';
        }

        $orders = \app\api\model\service\Order::where('is_fwb', 0)->where('status', 2)->where('starttime','>=',time())->select();
        foreach ($orders as $order) {
            $user_id=Skill::where('id',$order['skill_id'])->value('user_id');
            Skill::service_score($user_id,-1,'订单超时（未出发）',$order['id'],1);
        }

        $this->success('取消订单任务',$order_ids);
    }



    public function update()
    {
        $orders = \app\api\model\service\Order::where('is_fwb', 0)->where('status', 2)->where('starttime','>=',time())->select();
        foreach ($orders as $order) {
            $user_id=Skill::where('id',$order['skill_id'])->value('user_id');
            Skill::service_score($user_id,-1,'订单超时-未出发',$order['id'],1);
        }

    }

    public function orderConfirm()
    {
        $list=\app\api\model\wanlshop\Order::where('paymenttime','<',time()-86400*10)->where('state',3)->select();
        $order_ids='';
        foreach ($list as $order) {
            $id = $order->id;
            Db::startTrans();
            try {
                // 获取支付 1.1.2升级
//			    $pay = model('app\api\model\wanlshop\Pay')->get(['order_id' => $id, 'type' => 'goods']);
                // 平台转款给商家
                // controller('addons\wanlshop\library\WanlPay\WanlPay')->money(+$pay['price'], $order['shop']['user_id'], '买家确认收货', 'pay', $order['order_no']);
                // 查询是否有退款
                $refund = model('app\api\model\wanlshop\Refund')
                    ->where(['order_id' => $order->id, 'state' => 4, 'order_type' => 'goods'])
                    ->select();
                // 退款存在
                if ($refund) {
                    foreach ($refund as $value) {
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->money(-$value['price'], $order['shop']['user_id'], '该订单存在的退款', 'pay', $order['order_no']);
                    }
                }
                $order_list = OrderGoods::where('order_id', $order->id)->field('id,goods_id,title,goods_sku_id,order_id,shop_id,cost_price,freight_price,number,actual_payment,shequ_d_bili,quyu_d_bili')->select();
                foreach ($order_list as $v) {
                    $goods = model('app\api\model\wanlshop\Goods')
                        ->where(['id' => $v['goods_id'], 'status' => 'normal'])
                        ->find();
                    //如果不是城市运营商自己买则发放奖励
                    if ($this->auth->is_qydl != 1) {
                        //获取此用户所属养老院长
                        $sq_user = \app\common\model\User::getOneSq($this->auth->id);
                        //获取此用户所属城市运营商
                        $qy_user = \app\common\model\User::getOneQy($this->auth->id);

                        $sq_money = $v['shequ_d_bili'];
                        $qy_money = $v['quyu_d_bili'];
                        //发放养老院长奖励
                        if ($sq_user && $sq_money > 0) {
                            OrderGoods::where('id', $v['id'])->update(['shequ_bili' => $sq_money, 'shequ_d_bili' => 0]);
                            \app\common\model\User::money($sq_money, $sq_user['id'], '下级购买商品《' . $v['title'] . '》', 0, $id, 'fenyong');
                        }
                        //发放城市运营商奖励
                        if ($qy_user && $qy_money > 0) {
                            OrderGoods::where('id', $v['id'])->update(['quyu_bili' => $qy_money, 'quyu_d_bili' => 0]);
                            \app\common\model\User::money($qy_money, $qy_user['id'], '下级购买商品《' . $v['title'] . '》', 0, $id, 'fenyong');
                        }
                    }
                    $cost_price = $v['cost_price'] * $v['number'] + $v['freight_price'];
                    //给服务者发放佣金
                    if ($cost_price > 0) {
                        $shop_user_id = Db::name('wanlshop_shop')->where('id', $v['shop_id'])->value('user_id');
                        // \app\common\model\User::money($v['cost_price'],$shop_user_id,'供货商卖出商品获得佣金',2,$v['order_id'],'fenyong');
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->money(+$cost_price, $shop_user_id, '供货商卖出商品获得佣金', 'pay', $order['order_no']);
                    }
                }
                // 更新退款
                $order->save(['state' => 4, 'taketime' => time()], ['id' => $id]);
                $order_ids.=$id.',';
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getTrace());
            }
        }
        $this->success('执行完毕 订单集合: '.$order_ids);
    }
}
