<?php

namespace app\api\model\wanlshop;

use think\Model;
use traits\model\SoftDelete;

class GoodsSku extends Model
{

    use SoftDelete;

    // 表名
    protected $name = 'wanlshop_goods_sku';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'price',
    ];
    public function getPriceAttr($value, $data)
    {
        if(isset($data['price']) && isset($data['cost_price'])){
            $lirun = $data['price']-$data['cost_price'];
            $price = $data['price'];
            //查询此用户当前身份
            $auth = \app\common\library\Auth::instance();
            if($auth->isLogin()){
                //设为养老院长价
                if($auth->is_sqdl == 1){
                    if(isset($data['shequ_bili']) && $data['shequ_bili'] > 0){
                        $prices =  $price - $lirun * ($data['shequ_bili']/100);
                    }else{
                        $prices =  $price - $lirun * (config('site.goods_shequ_bili')/100);
                    }
                }
                if($auth->is_qydl == 1){
                    if(isset($data['quyu_bili']) && $data['quyu_bili'] > 0){
                        $prices =  $price - $lirun * ($data['quyu_bili']/100);
                    }else{
                        $prices =  $price - $lirun * (config('site.goods_quyu_bili')/100);
                    }
                }
                if($auth->is_ylgw == 1){
                    if(isset($data['shequ_bili']) && $data['shequ_bili'] > 0){
                        $prices =  $price - $lirun * (($data['shequ_bili']/2)/100);
                    }else{
                        $prices =  $price - $lirun * ((config('site.goods_shequ_bili')/2)/100);
                    }
                }
                $price = isset($prices) ? $prices : $price;
            }else{
                $price = $data['price'];
            }
            return bcdiv($price,1,2);
        }else{
            return isset($data['price']) ? $data['price'] : '';
        }
    }
	// getDifferenceAttr
	public function getDifferenceAttr($value)
	{	
		return $value ? explode(',', $value) : [];
	}
}
