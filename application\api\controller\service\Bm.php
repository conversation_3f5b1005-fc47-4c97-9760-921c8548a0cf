<?php
namespace app\api\controller\service;

use app\common\controller\Api;
use app\common\model\User;
use fast\Random;
use think\Db;
use app\common\model\service\Bm as BmModel;
use app\common\model\service\BmTc;
use app\common\model\service\BmOrder;
use app\common\model\service\BmXq;
use think\Exception;

/**
 * 首页接口
 */
class Bm extends Api
{
    protected $noNeedLogin = [''];
    protected $noNeedRight = ['*'];

    /**
     * 城市运营商护理员审核列表
     * <AUTHOR>
     * @date 2024/9/18  上午10:22
     * @notes
     */
    public function areaBmList()
    {
        $status = $this->request->post('status');
        $sk = $this->request->post('sk');
        //获取此城市运营商代理区域
        $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
        if(!$district){
            $this->error('您还不是城市运营商');
        }
        $where = [];
        if(isset($status)){
            $where['b.status'] = $status;
        }
        if($sk){
            $where['b.name'] =  ["like","%{$sk}%"];;
        }
        $where['s.district'] = $district;
        $list = \app\common\model\service\Bm::alias('b')
            ->join('service_skill s','b.user_id = s.user_id')
            ->field('b.*')
            ->where($where)
            ->paginate();
        $this->success('信息返回成功',$list);
    }

    public function areaBmDetail()
    {
        $id = $this->request->post('id');
        //获取此城市运营商代理区域
        $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
        if(!$district){
            $this->error('您还不是城市运营商');
        }
        $where = [];
        if($id){
            $where['b.id'] =  $id;;
        }
        $where['s.district'] = $district;
        $list = \app\common\model\service\Bm::alias('b')
            ->join('service_skill s','b.user_id = s.user_id')
            ->field('b.*')
            ->where($where)
            ->find();
        $this->success('信息返回成功',$list);
    }
    /**
     * 城市运营商修改护理员信息
     * <AUTHOR>
     * @date 2024/9/18  上午10:57
     * @notes
     */
    public function areaBmUpdate()
    {
        $params = $this->request->post();
        $params['updatetime'] = time();
        $update = array_filter($params);
        try{
            if(!empty($update['work']))
            $update['work']=json_encode($update['work']);
            $res = BmModel::where('id',$params['id'])->update($update);
            if(!$res){
                throw new Exception('信息更新失败');
            }
        }catch (\Exception $e){
            $this->error($e->getMessage());
        }
        $this->success('信息更新成功',$params);
    }

    /**
     * 城市运营商获取保姆信息详情
     * <AUTHOR>
     * @date 2024/9/18  下午2:40
     * @notes
     */
    public function areaGetBm()
    {
        $id = $this->request->post('id');
        $info = \app\common\model\service\Bm::where('id',$id)->find();
        $this->success('信息返回成功',$info);
    }

    /**
     * 获取保姆套餐列表
     * <AUTHOR>
     * @date 2024/8/29  下午3:02
     * @notes
     */
    public function getBmTc()
    {
        $list = BmTc::select();
        $this->success('信息返回成功',$list);
    }

    /**
     * 提交保姆解锁
     * <AUTHOR>
     * @date 2024/8/29  下午3:06
     * @notes
     */
    public function BmOrder()
    {
          $id = $this->request->post('id');
        $paytype = $this->request->post('paytype',0);
          $bmtc = BmTc::get($id);
          if(!$bmtc){
              $this->error('套餐不存在');
          }
        $uid = $this->auth->id;
        $money = model('app\api\model\service\User')->where('id',$uid)->value('money');
        Db::startTrans();
        try{
            $price = $bmtc->price;
            $price <=0 && $this->error('金额错误');
            $orderId = 'Bmorder'.Random::alnum(5).'-'.$uid.'-'.time();

            $BmOrder = new \app\common\model\service\BmOrder(['user_id'=>$uid,'tc_id'=>$id,'num'=>$bmtc->num,'price'=>$price,'orderid'=>$orderId,'paytype'=>$paytype]);
            $BmOrder->allowField(true)->save();
            if($paytype == 4){//余额支付
                if($money < $price){
                    throw new Exception("余额不足");
                }
                User::money(-$price,$uid,'购买保姆套餐');
                db('service_bm_order')->where('orderid',$orderId)->update(['status'=>'paid','paytime'=>time()]);
                //增加保姆次数
                User::bm($bmtc->num,$uid,'购买保姆套餐');
                $re = 1;
            }else{
                $re = \addons\service\library\Pay::payOrder(['amount'=>$price,'orderid'=>$orderId,'title'=>'解锁阿姨'],$paytype,$uid,0);
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('支付失败',$e->getMessage());
        }
        $this->success('支付信息返回成功',$re);
    }

    /**
     * 用户保姆需求
     * <AUTHOR>
     * @date 2024/8/29  下午4:46
     * @notes
     */
    public function userBmXq()
    {
        $id = $this->request->post('id');
        $params = $this->request->post();
        $params['user_id'] = $this->auth->id;
        $type = $this->request->post('type');
        unset($params['type']);
        $bmxq = \app\common\model\service\BmXq::where('user_id',$this->auth->id)->find();
        if($type == 'set'){
            Db::startTrans();
            try{
                if($bmxq){
                    $res = \app\common\model\service\BmXq::where('id',$bmxq->id)->update($params);
                    if(!$res){
                        throw new Exception('信息更新失败');
                    }
                }else{
                    $params['createtime'] = time();
                    $res = \app\common\model\service\BmXq::create($params);
                    if(!$res){
                        throw new Exception('信息更新失败');
                    }
                }

                Db::commit();
            }catch (\Exception $e){
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('提交成功');
        }else{
            $this->success('查询成功',$bmxq);
        }
    }


    /**
     * 获取保姆列表
     * <AUTHOR>
     * @date 2024/8/29  下午5:11
     * @notes
     */
    public function getBmList()
    {
        $bmxq = \app\common\model\service\BmXq::where('user_id',$this->auth->id)->find();
        $where = ['status'=>3];
        if($bmxq){
            if($bmxq->zjqk){
                // $where['b.zjqk'] = $bmxq->zjqk;
            }
            if($bmxq->city){
                // $where['s.city'] = $bmxq->city;
            }
            if($bmxq->fw_type){
                // $where[] =  ['exp', Db::raw("FIND_IN_SET('$bmxq->fw_type',b.gzqk)")];
            }
        }
        $list = \app\common\model\service\Bm::alias('b')
            ->join('service_skill s','b.user_id = s.user_id')
            ->field('b.*')
            ->where($where)
            ->paginate();
        $this->success('信息返回成功',$list);
    }

    /**
     * 保姆详情
     * <AUTHOR>
     * @date 2024/9/6  下午5:33
     * @notes
     */
    public function getBmInfo()
    {
        $id = $this->request->post('id');
        $bm = \app\common\model\service\Bm::where('id',$id)->find();
        if(!$bm){ $this->error('保姆信息错误');}
        $bm['skill_info'] = \app\api\model\service\Skill::getSimpleSkillForUserId($bm['user_id']);
        if(!$bm){
            $this->error('信息不存在');
        }
        $bm['is_js'] = \app\common\model\service\BmLog::where('user_id',$this->auth->id)->where('bm_id',$id)->count();
        $this->success('信息返回成功',$bm);
    }


    /**
     * 保姆详情
     * <AUTHOR>
     * @date 2024/9/6  下午5:33
     * @notes
     */
    public function getBmInfoForUid()
    {
        $id = $this->request->post('user_id');
        $bm = \app\common\model\service\Bm::where('user_id',$id)->find();
        if(!$bm){ $this->success('保姆信息错误');}
        $bm['skill_info'] = \app\api\model\service\Skill::getSimpleSkillForUserId($bm['user_id']);
        if(!$bm){
            $this->success('信息不存在');
        }
        $bm['is_js'] = \app\common\model\service\BmLog::where('user_id',$this->auth->id)->where('bm_id',$bm['id'])->count();
        $this->success('信息返回成功',$bm);
    }


    /**
     * 解锁保姆
     * <AUTHOR>
     * @date 2024/8/30  下午6:24
     * @notes
     */
    public function jsBm()
    {
        $bm_id = $this->request->post('bm_id');
        if(!$bm_id){
            $this->error('参数错误');
        }
        $bm = \app\common\model\service\Bm::where('id',$bm_id)->find();
        if(!$bm){
            $this->error('保姆不存在');
        }
        //查询是否已解锁过
        $is_exist = \app\common\model\service\BmLog::where('user_id',$this->auth->id)->where('bm_id',$bm_id)->find();
        if($is_exist){
            $this->error('请勿重复解锁');
        }
        if($this->auth->bm_num <= 0){
            $this->error('解锁次数不足');
        }
        User::bm(-1,$this->auth->id,'解锁保姆',$bm_id);
        $this->success('解锁成功');
    }

}
