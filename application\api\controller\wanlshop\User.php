<?php

namespace app\api\controller\wanlshop;

use app\api\model\service\Goods;
use app\api\model\service\MoneyLog;
use app\api\model\service\UserInfo;
use app\common\controller\Api;
use addons\wanlshop\library\EasyWeChat\Easywechat;
use addons\wanlshop\library\WanlChat\WanlChat;

use app\common\library\Sms;
use fast\Random;
use fast\Http;
use app\common\model\User as UserModel;
use think\Validate;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use OSS\OssClient;

/**
 * WanlShop会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'logout', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third', 'phone', 'perfect'];
    protected $noNeedRight = ['*'];
    
    public function _initialize()
    {
        parent::_initialize();
        //WanlChat 即时通讯调用
		$this->wanlchat = new WanlChat();
		// Auth 写入
		$this->auth->setAllowFields(['id','username','nickname','mobile','avatar','level','gender','birthday','bio','money','score','successions','maxsuccessions','prevtime','logintime','loginip','jointime','parent_id','is_sqdl','is_qydl','invit_code','card_no','realname','ylgw_number_no']);
		// Auth 读取
		$this->auth->getAllowFields(['id','username','nickname','mobile','avatar','level','gender','birthday','bio','money','score','successions','maxsuccessions','prevtime','logintime','loginip','jointime']);
    }
	
    /**
     * 会员登录
     * @ApiMethod   (POST)
     * @param string $account  账号
     * @param string $password 密码
     */
    public function login()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$account = $this->request->post('account');
			$password = $this->request->post('password');
			$is_service = $this->request->post('is_service',1);
			// $client_id = $this->request->post('client_id');
			if (!$account || !$password) {
				$this->error(__('Invalid parameters'));
			}
			$ret = $this->auth->login($account, $password,$is_service);
			if ($ret) {
			    // if($client_id){
			    //     $this->wanlchat->bind($client_id, $this->auth->id);
			    // }
				$this->success(__('Logged in successful'), self::userInfo());
			} else {
				$this->error($this->auth->getError());
			}
		}
		$this->error(__('非法请求'));
    }

    /**
     * 手机验证码登录
     * @ApiMethod   (POST)
     * @param string $mobile  手机号
     * @param string $captcha 验证码
     */
    public function mobilelogin()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$mobile = $this->request->post('mobile');
			$captcha = $this->request->post('captcha');
			$client_id = $this->request->post('client_id');
			$invit_code = $this->request->post('invit_code');
			$is_service = $this->request->post('is_service',1);
			if (!$mobile || !$captcha) {
				$this->error(__('Invalid parameters'));
			}
			if (!Validate::regex($mobile, "^1\d{10}$")) {
				$this->error(__('Mobile is incorrect'));
			}
			if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
				$this->error(__('Captcha is incorrect'));
			}
			$user = \app\common\model\User::where(['mobile' => $mobile,'is_service'=>$is_service])->find();
			if ($user) {
				if ($user->status != 'normal') {
					$this->error(__('Account is locked'));
				}
				//如果已经有账号则直接登录
				$ret = $this->auth->direct($user->id);
			} else {
				$ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, ['invit_code'=>$invit_code,'is_service'=>$is_service]);
                $newUser = ['user_id'=>$this->auth->id,'mobile'=>$mobile];
                $userInfo = new UserInfo($newUser);
                $userInfo->allowField(true)->save();
                //注册优惠券
                (new \app\api\controller\service\User())->giveRegCoupon();
			}
			if ($ret) {
				Sms::flush($mobile, 'mobilelogin');
				if($client_id){
			        $this->wanlchat->bind($client_id, $this->auth->id);
			    }
				$this->success(__('Logged in successful'), self::userInfo());
			} else {
				$this->error($this->auth->getError());
			}
		}
		$this->error(__('非法请求'));
    }
    
    /**
     * 小程序手机号登录
     * @ApiMethod   (POST)
     * @param string $encryptedData  
     * @param string $iv  
     */
    public function phone()
    {
		
        //设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$post = $this->request->post();
		    if (!isset($post['iv'])) {
		        $this->error(__('获取手机号异常'));
		    }
			// 1.1.9升级 改为Easywechat
			try{
				$auth = Easywechat::app()
					->auth
					->session($post['code']);
			} catch (\Exception $e) {
				$this->error($e->getMessage());
			}
			if(isset($auth['errcode'])){
				$this->error($auth['errmsg']);
			}
		    // 判断third是否存在ID,存在快速登录
			if(isset($auth['unionid'])){
				$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'mp_weixin', 'unionid' => $auth['unionid']]);
			}else{
				$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'mp_weixin', 'openid' => $auth['openid']]);
			}
			
			//如果已经有账号则直接登录
		    if ($third && $third['user_id'] != 0) {
    			$ret = $this->auth->direct($third['user_id']);
		    } else {
    		    // 手机号解码
				try{
					$encryptor = Easywechat::app()
						->encryptor
						->decryptData($auth['session_key'], $post['iv'], $post['encryptedData']);
				} catch (\Exception $e) {
					$this->error($e->getMessage());
				}
                // 开始登录
    		    $mobile = $encryptor['phoneNumber'];
    			$user = \app\common\model\User::getByMobile($mobile);
    			if ($user) {
    				if ($user->status != 'normal') {
    					$this->error(__('Account is locked'));
    				}
    				//如果已经有账号则直接登录
    				$ret = $this->auth->direct($user->id);
    			} else {
    				$ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
    			}
		    }
		    if ($ret) {
		        if (isset($post['client_id']) && $post['client_id'] != null) {
    		        $this->wanlchat->bind($post['client_id'], $this->auth->id);
    		    }
    			$this->success(__('Logged in successful'), self::userInfo());
    		} else {
    			$this->error($this->auth->getError());
    		}
		}
		$this->error(__('非法请求'));
    }
    
    
    /**
     * 注册会员
     * @ApiMethod   (POST)
     * @param string $mobile   手机号
     * @param string $code   验证码
     */
    public function register()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$mobile = $this->request->post('mobile');
			$code = $this->request->post('captcha');
			$client_id = $this->request->post('client_id');
			if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
				$this->error(__('Mobile is incorrect'));
			}
			$ret = Sms::check($mobile, $code, 'register');
			if (!$ret) {
				$this->error(__('Captcha is incorrect'));
			}
			$ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
			if ($ret) {
			    if($client_id){
			        $this->wanlchat->bind($client_id, $this->auth->id);
			    }
				$this->success(__('Sign up successful'), self::userInfo());
			} else {
				$this->error($this->auth->getError());
			}
		}
		$this->error(__('非法请求'));
    }

    /**
     * 注销登录
     */
    public function logout($client_id = null)
    {
        // 踢出即时通讯 1.2.0升级
		foreach ($this->wanlchat->getUidToClientId($this->auth->id) as $client_id) {
			$this->wanlchat->destoryClient($client_id);
		}
        // 退出登录
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     * @ApiMethod   (POST)
	 *
     * @param string $avatar   头像地址
     * @param string $username 用户名
     * @param string $nickname 昵称
     * @param string $bio      个人简介
     */
    public function profile()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$user = $this->auth->getUser();
            $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
            if($avatar)$user->avatar = $avatar;
            $username = $this->request->post('username');
            $nickname = $this->request->post('nickname');
            $bio = $this->request->post('bio');
            $job = $this->request->post('job');
            $city = $this->request->post('city');
            $wechat = $this->request->post('wechat');
            $card_no = $this->request->post('card_no');
            $realname = $this->request->post('realname');
            // 1.1.9升级 生日和性别并不会提交到后台保存
            $gender = $this->request->post('gender');
            $birthday = $this->request->post('birthday');
            $qy_mobile = $this->request->post('qy_mobile');
            $ylgw_xy= $this->request->post('ylgw_xy');
            $ylgw_zs = $this->request->post('ylgw_zs');
            // 1.1.9升级 优化为Easywechat
            if($nickname)$user->nickname = $nickname;
            if($bio)$user->bio = $bio;
            if($job)$user->job = $job;
            if($city)$user->city = $city;
            if($wechat)$user->wechat = $wechat;
            // 1.1.9升级 生日和性别并不会提交到后台保存
            if($gender)$user->gender = $gender;
            if($birthday)$user->birthday = $birthday;
            if($qy_mobile)$user->qy_mobile = $qy_mobile;
            if($card_no){
                $user->card_no = $card_no;
                $user->realname = $realname;
                if(!$user->ylgw_number_no){
                    $user->ylgw_number_no = $this->getCartNo();
                }
            }
            if($ylgw_xy)$user->ylgw_xy = $ylgw_xy;
            if($ylgw_zs)$user->ylgw_zs = $ylgw_zs;
			$user->save();
			$this->success('修改成功',$user);
		}
		$this->error(__('非法请求'));
    }

    public function getCartNo()
    {
        $code = 'JQF'.mt_rand(1000000,9999999);
        $is_user = UserModel::where('ylgw_number_no',$code)->find();
        if($is_user){
            return $this->getCartNo();
        }else{
            return $code;
        }
    }

    /**
     * 修改手机号
     * @ApiMethod   (POST)
     * @param string $email   手机号
     * @param string $captcha 验证码
     */
    public function changemobile()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$user = $this->auth->getUser();
			$mobile = $this->request->post('mobile');
			$captcha = $this->request->post('captcha');
			$is_service = $this->request->post('is_service',0);
			if (!$mobile || !$captcha) {
			    $this->error(__('Invalid parameters'));
			}
			if (!Validate::regex($mobile, "^1\d{10}$")) {
			    $this->error(__('Mobile is incorrect'));
			}
			if (\app\common\model\User::where('mobile', $mobile)->where('is_service',$is_service)->where('id', '<>', $user->id)->find()) {
			    $this->error(__('Mobile already exists'));
			}
			$result = Sms::check($mobile, $captcha, 'changemobile');
			if (!$result) {
			    $this->error(__('Captcha is incorrect'));
			}
			$verification = $user->verification;
			$verification->mobile = 1;
			$user->verification = $verification;
			$user->mobile = $mobile;
			$user->save();
			
			Sms::flush($mobile, 'changemobile');
			$this->success();
		}
		$this->error(__('非法请求'));
    }
    
    /**
     * 重置密码
     * @ApiMethod   (POST)
     * @param string $mobile      手机号
     * @param string $newpassword 新密码
     * @param string $captcha     验证码
     */
    public function resetpwd()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$mobile = $this->request->post("mobile");
			$newpassword = $this->request->post("newpassword");
			$is_service = $this->request->post("is_service",0);
			$captcha = $this->request->post("captcha");
			if (!$newpassword || !$captcha || !$mobile) {
				$this->error(__('Invalid parameters'));
			}
			if (!Validate::regex($mobile, "^1\d{10}$")) {
				$this->error(__('Mobile is incorrect'));
			}
			$user = \app\common\model\User::where(['mobile' => $mobile,'is_service'=>$is_service] )->find();
			if (!$user) {
				$this->error(__('User not found'));
			}
			$ret = Sms::check($mobile, $captcha, 'resetpwd');
			if (!$ret) {
				$this->error(__('Captcha is incorrect'));
			}
			Sms::flush($mobile, 'resetpwd');
			//模拟一次登录
			$this->auth->direct($user->id);
			$ret = $this->auth->changepwd($newpassword, '', true);
			if ($ret) {
				$this->success(__('Reset password successful'));
			} else {
				$this->error($this->auth->getError());
			}
		}
		$this->error(__('非法请求'));
    }
    
    /**
     * 第三方登录-web登录
     * @ApiMethod   (POST)
     * @param string $platform 平台名称
     */
    public function third_web()
    {
        $this->error(__('暂未开放'));
    }
    
    
    /**
     * 第三方登录
     * @ApiMethod   (POST)
     * @param string $platform 平台名称
     * @param string $code     Code码
     */
    public function third()
    {
        //设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			// 获取登录配置
			$config = get_addon_config('wanlshop');
			// 获取前端参数
			$post = $this->request->post();
			// 登录项目
			$time = time();
			$platform = $post['platform'];
			// 开始登录
			switch ($platform)
			{
				// 微信小程序登录
				case 'mp_weixin':
					// 1.1.9升级 改为Easywechat
					try{
						$auth = Easywechat::app()
							->auth
							->session($post['loginData']['code']);
					} catch (\Exception $e) {
						$this->error($e->getMessage());
					}
					if(isset($auth['errcode'])){
						$this->error($auth['errmsg']);
					}
					if(isset($auth['unionid'])){
						$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'unionid' => $auth['unionid']]);
					}else{
						$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'openid' => $auth['openid']]);
					}
					// 成功登录
					if ($third) {
					    $user = model('app\common\model\User')->get($third['user_id']);
					    if (!$user) {
					        $this->success('尚未绑定用户', [
					            'binding' => 0,
					            'token' => $third['token']
					        ]);
					    }
					    $third->save([
					        'access_token' => $auth['session_key'],
					        'expires_in' => 7776000,
					        'logintime' => $time,
					        'expiretime' => $time + 7776000
					    ]);
					    $ret = $this->auth->direct($user->id);
					    if ($ret) {
						    if (isset($post['client_id']) && $post['client_id'] != null) {
						        $this->wanlchat->bind($post['client_id'], $this->auth->id);
						    }
							$this->success(__('Sign up successful'), self::userInfo());
						} else {
							$this->error($this->auth->getError());
						}
					} else {
					    // 新增$third
					    $third = model('app\api\model\wanlshop\Third');
					    $third->platform  = 'weixin_open';
						if(isset($auth['unionid'])){
							$third->unionid  = $auth['unionid'];
						}else{
							$third->openid  = $auth['openid'];
						}
					    $third->access_token  = $auth['session_key'];
					    $third->expires_in  = 7776000;
					    $third->logintime  = $time;
					    $third->expiretime  = $time + 7776000;
					    // 判断当前是否登录
					    if($this->auth->isLogin()){
							if (isset($post['client_id']) && $post['client_id'] != null) {
							    $this->wanlchat->bind($post['client_id'], $this->auth->id);
							}
					        $third->user_id  = $this->auth->id;
					        $third->save();
					        // 直接绑定自动完成
					        $this->success('绑定成功', [
					            'binding' => 1
					        ]);
					    } else {
							$third->token  = Random::uuid();
					        $third->save();
					        // 通知客户端绑定
					        $this->success('尚未绑定用户', [
					            'binding' => 0,
					            'token' => $third->token
					        ]);
					    }
					}
					break;
					
				// 微信App登录
				case 'app_weixin':
					$params = [
						'access_token' => $post['loginData']['authResult']['access_token'],
						'openid' => $post['loginData']['authResult']['openid']
					];
					$result = Http::sendRequest("https://api.weixin.qq.com/sns/userinfo", $params, 'GET');
					if ($result['ret']) {
					    $json = (array)json_decode($result['msg'], true);
						if(isset($json['unionid'])){
							$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'unionid' => $json['unionid']]);
						}else{
							$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'openid' => $json['openid']]);
						}
					    // 成功登录
                        if ($third) {
                            $third->save([
                                'access_token' => $post['loginData']['authResult']['access_token'],
                                'refresh_token' => $post['loginData']['authResult']['refresh_token'],
                                'expires_in' => $post['loginData']['authResult']['expires_in'],
                                'logintime' => $time,
                                'expiretime' => $time + $post['loginData']['authResult']['expires_in']
                            ]);
                            $ret = $this->auth->direct($third['user_id']);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                    		        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                    		    }
                				$this->success(__('Sign up successful'), self::userInfo());
                			} else {
                				$this->error($this->auth->getError());
                			}
                        } else {
                            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform  = 'weixin_open';
							if(isset($json['unionid'])){
								$third->unionid  = $json['unionid'];
							}else{
								$third->openid  = $json['openid'];
							}
                            $third->access_token  = $post['loginData']['authResult']['access_token'];
                            $third->refresh_token  = $post['loginData']['authResult']['refresh_token'];
                            $third->expires_in  = $post['loginData']['authResult']['expires_in'];
                            $third->logintime  = $time;
                            $third->expiretime  = $time + $post['loginData']['authResult']['expires_in'];
                            // 判断当前是否登录,否则注册
                            if($this->auth->isLogin()){
								if (isset($post['client_id']) && $post['client_id'] != null) {
								    $this->wanlchat->bind($post['client_id'], $this->auth->id);
								}
                                $third->user_id  = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
                                $username = $json['nickname'];
								$auth = [];
                                $mobile = '';
                                $gender = $json['sex'] == 1 ? 1 : 0;
                                $avatar = $json['headimgurl'];
								// 1.1.3升级
								if(isset($json['unionid'])){
									// 1.1.3升级 查询其他unionid的user_id进行登录
									$unionid = model('app\api\model\wanlshop\Third')
									    ->where('user_id','<>', 0)
									    ->where('unionid','=', $json['unionid'])
									    ->find();
									if($unionid){
										$auth = $this->auth->direct($unionid['user_id']);
									}else{
										// 注册账户
										$auth = $this->auth->register('u_'.Random::alnum(6), Random::alnum(), '', $mobile, [
										    'gender' => $gender, 
										    'nickname' => $username, 
										    'avatar' => $avatar
										]);
									}
								}else{
									// 注册账户
									$auth = $this->auth->register('u_'.Random::alnum(6), Random::alnum(), '', $mobile, [
									    'gender' => $gender, 
									    'nickname' => $username, 
									    'avatar' => $avatar
									]);
								}
                    			if ($auth) {
                    			    if (isset($post['client_id']) && $post['client_id'] != null) {
                        		        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                        		    }
                    				// 更新第三方登录
                    			    $third->user_id  = $this->auth->id;
                    			    $third->openname  = $username;
                    			    $third->save();
                    				$this->success(__('Sign up successful'), self::userInfo());
                    			} else {
                    				$this->error($this->auth->getError());
                    			}
                            }
                        }
					}else{
					    $this->error('API异常，App登录失败'); 
					}
					break;
				// 微信公众号登录
				case 'h5_weixin':
					$params = [
					    'appid'      => $config['sdk_qq']['gz_appid'],
					    'secret'     => $config['sdk_qq']['gz_secret'],
					    'code'       => $post['code'],
					    'grant_type' => 'authorization_code'
					];
					$result = Http::sendRequest('https://api.weixin.qq.com/sns/oauth2/access_token', $params, 'GET');
					if ($result['ret']) {
						$access = (array)json_decode($result['msg'], true);
						//获取用户信息
						$queryarr = [
							"access_token" => $access['access_token'],
							"openid"       => $access['openid']
						];
						$ret = Http::sendRequest("https://api.weixin.qq.com/sns/userinfo", $queryarr, 'GET');
						if ($ret['ret']) {
							$json = (array)json_decode($ret['msg'], true);
							if(isset($json['unionid'])){
								$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_h5', 'unionid' => $json['unionid']]);
							}else{
								$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_h5', 'openid' => $json['openid']]);
							}
							// 成功登录
							if ($third) {
							    $third->save([
									'openid' => $json['openid'], // 1.1.2升级
							        'access_token' => $access['access_token'],
							        'refresh_token' => $access['refresh_token'],
							        'expires_in' => $access['expires_in'],
							        'logintime' => $time,
							        'expiretime' => $time + $access['expires_in']
							    ]);
								// 登录客户端
							    $ret = $this->auth->direct($third['user_id']);
							    if ($ret) {
							        if (isset($post['client_id']) && $post['client_id'] != null) {
								        $this->wanlchat->bind($post['client_id'], $this->auth->id);
								    }
									$this->success(__('Sign up successful'), self::userInfo());
								} else {
									$this->error($this->auth->getError());
								}
							} else {
							    // 新增$third
							    $third = model('app\api\model\wanlshop\Third');
							    $third->platform  = 'weixin_h5';
								// 1.1.2升级
								if(isset($json['unionid'])){
									$third->unionid  = $json['unionid'];
									$third->openid  = $json['openid'];
								}else{
									$third->openid  = $json['openid'];
								}
							    $third->access_token  = $access['access_token'];
							    $third->refresh_token  = $access['refresh_token'];
							    $third->expires_in  = $access['expires_in'];
							    $third->logintime  = $time;
							    $third->expiretime  = $time + $access['expires_in'];
							    // 获取到的用户信息
							    $username = $json['nickname'];
								$auth = [];
							    $mobile = '';
							    $gender = $json['sex'] == 1 ? 1 : 0;
							    $avatar = $json['headimgurl'];
								
								// 1.1.3升级
								if(isset($json['unionid'])){
									// 1.1.3升级 查询其他unionid的user_id进行登录
									$unionid = model('app\api\model\wanlshop\Third')
									    ->where('user_id','<>', 0)
									    ->where('unionid','=', $json['unionid'])
									    ->find();
										
									if($unionid){
										$auth = $this->auth->direct($unionid['user_id']);
									}else{
										// 注册账户
										$auth = $this->auth->register('u_'.Random::alnum(6), Random::alnum(), '', $mobile, [
										    'gender' => $gender, 
										    'nickname' => $username, 
										    'avatar' => $avatar
										]);
									}
								}else{
									// 注册账户
									$auth = $this->auth->register('u_'.Random::alnum(6), Random::alnum(), '', $mobile, [
									    'gender' => $gender, 
									    'nickname' => $username, 
									    'avatar' => $avatar
									]);
								}
								
							    if ($auth) {
							        if (isset($post['client_id']) && $post['client_id'] != null) {
							            $this->wanlchat->bind($post['client_id'], $this->auth->id);
							        }
							    	// 更新第三方登录
							        $third->user_id  = $this->auth->id;
							        $third->openname  = $username;
							        $third->save();
							    	$this->success(__('Sign up successful'), self::userInfo());
							    } else {
							    	$this->error($this->auth->getError());
							    }
							}
						}else{
							$this->error('获取用户信息失败！'); 
						}
					}else{
						$this->error('获取openid失败！'); 
					}
					break;
				// QQ小程序登录
				case 'mp_qq':
					$params = [
						'appid'      => $config[$platform]['appid'],
						'secret'     => $config[$platform]['appsecret'],
						'js_code'    => $post['loginData']['code'],
						'grant_type' => 'authorization_code'
					];
					$result = Http::sendRequest("https://api.q.qq.com/sns/jscode2session", $params, 'GET');
					if ($result['ret']) {
					    $json = (array)json_decode($result['msg'], true);
						if(isset($json['unionid'])){
							$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'qq_open', 'unionid' => $json['unionid']]);
						}else{
							$third = model('app\api\model\wanlshop\Third')->get(['platform' => 'qq_open', 'openid' => $json['openid']]);
						}
                        // 成功登录
                        if ($third) {
                            $user = model('app\common\model\User')->get($third['user_id']);
                            if (!$user) {
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'token' => $third['token']
                                ]);
                            }
                            $third->save([
                                'access_token' => $json['session_key'],
                                'expires_in' => 7776000,
                                'logintime' => $time,
                                'expiretime' => $time + 7776000
                            ]);
                            $ret = $this->auth->direct($user->id);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                    		        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                    		    }
                				$this->success(__('Sign up successful'), self::userInfo());
                			} else {
                				$this->error($this->auth->getError());
                			}
                        } else {
                            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform  = 'qq_open';
							if(isset($json['unionid'])){
								$third->unionid  = $json['unionid'];
							}else{
								$third->openid  = $json['openid'];
							}
                            $third->access_token  = $json['session_key'];
                            $third->expires_in  = 7776000;
                            $third->logintime  = $time;
                            $third->expiretime  = $time + 7776000;
                            // 判断当前是否登录
                            if($this->auth->isLogin()){
								// 1.1.4升级
								if (isset($post['client_id']) && $post['client_id'] != null) {
								    $this->wanlchat->bind($post['client_id'], $this->auth->id);
								}
                                $third->user_id  = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
								$third->token  = Random::uuid();
                                $third->save();
                                // 通知客户端绑定
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'token' => $third->token
                                ]);
                            }
                        }
					}else{
						$this->error('API异常，微信小程序登录失败'); 
					}
					break; 
					
				// QQ App登录
				case 'app_qq':
					$params = [
						'access_token' => $post['loginData']['authResult']['access_token']
					];
					$options = [
                        CURLOPT_HTTPHEADER  => [
                            'Content-Type: application/x-www-form-urlencoded'
                        ]
                    ];
					$result = Http::sendRequest("https://graph.qq.com/oauth2.0/me", $params, 'GET' ,$options);
					if ($result['ret']) {
					    $json = (array)json_decode(str_replace(" );","",str_replace("callback( ","",$result['msg'])), true);
					    if ($json['openid'] == $post['loginData']['authResult']['openid']) {
				            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'qq_open', 'openid' => $json['openid']]);
    				        if ($third) {
    				            $user = model('app\common\model\User')->get($third['user_id']);
                                if (!$user) {
                                    $this->success('尚未绑定用户', [
                                        'binding' => 0,
                                        'token' => $third['token']
                                    ]);
                                }
    				            $third->save([
                                    'access_token' => $post['loginData']['authResult']['access_token'],
                                    'expires_in' => $post['loginData']['authResult']['expires_in'],
                                    'logintime' => $time,
                                    'expiretime' => $time + $post['loginData']['authResult']['expires_in']
                                ]);
                                $ret = $this->auth->direct($third['user_id']);
                                if ($ret) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                        		        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                        		    }
                    				$this->success(__('Sign up successful'), self::userInfo());
                    			} else {
                    				$this->error($this->auth->getError());
                    			}
    				        } else {
    				            // 新增$third
                                $third = model('app\api\model\wanlshop\Third');
                                $third->platform  = 'qq_open';
                                $third->openid  = $json['openid'];
                                $third->access_token  = $post['loginData']['authResult']['access_token'];
                                $third->expires_in  = $post['loginData']['authResult']['expires_in'];
                                $third->logintime  = $time;
                                $third->expiretime  = $time + $post['loginData']['authResult']['expires_in'];
                                // 判断当前是否登录
                                if($this->auth->isLogin()){
									if (isset($post['client_id']) && $post['client_id'] != null) {
									    $this->wanlchat->bind($post['client_id'], $this->auth->id);
									}
                                    $third->user_id  = $this->auth->id;
                                    $third->save();
                                    // 直接绑定自动完成
                                    $this->success('绑定成功', [
                                        'binding' => 1
                                    ]);
                                } else {
									$third->token  = Random::uuid();
                                    $third->save();
                                    // 通知客户端绑定
                                    $this->success('尚未绑定用户', [
                                        'binding' => 0,
                                        'token' => $third->token
                                    ]);
                                }
    				        }
					    } else {
					        $this->error(__('非法请求，机器信息已提交'));
					    }
					}else{
					    $this->error('API异常，App登录失败'); 
					}
					break;
				// QQ 网页登录
				case 'h5_qq':
					// 后续版本上线
					break; 
				// 微博App登录
				case 'app_weibo':
					$params = [
						'access_token' => $post['loginData']['authResult']['access_token']
					];
					$options = [
                        CURLOPT_HTTPHEADER  => [
                            'Content-Type: application/x-www-form-urlencoded'
                        ],
                        CURLOPT_POSTFIELDS => http_build_query($params),
                        CURLOPT_POST => 1
                    ];
					$result = Http::post("https://api.weibo.com/oauth2/get_token_info", $params, $options);
					$json = (array)json_decode($result, true);
				    if($json['uid'] == $post['loginData']['authResult']['uid']){
				        $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weibo_open', 'openid' => $json['uid']]);
				        if ($third) {
				            $user = model('app\common\model\User')->get($third['user_id']);
                            if (!$user) {
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'token' => $third['token']
                                ]);
                            }
				            $third->save([
                                'access_token' => $post['loginData']['authResult']['access_token'],
                                'expires_in' => $json['expire_in'],
                                'logintime' => $json['create_at'],
                                'expiretime' => $json['create_at'] + $json['expire_in']
                            ]);
                            $ret = $this->auth->direct($third['user_id']);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                    		        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                    		    }
                				$this->success(__('Sign up successful'), self::userInfo());
                			} else {
                				$this->error($this->auth->getError());
                			}
				        } else {
				            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform  = 'weibo_open';
                            $third->openid  = $json['uid'];
                            $third->access_token  = $post['loginData']['authResult']['access_token'];
                            $third->expires_in  = $json['expire_in'];
                            $third->logintime  = $json['create_at'];
                            $third->expiretime  = $json['create_at'] + $json['expire_in'];
                            // 判断当前是否登录
                            if($this->auth->isLogin()){
								// 1.1.4升级
								if (isset($post['client_id']) && $post['client_id'] != null) {
								    $this->wanlchat->bind($post['client_id'], $this->auth->id);
								}
                                $third->user_id  = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
								$third->token  = Random::uuid();
                                $third->save();
                                // 通知客户端绑定
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'token' => $third->token
                                ]);
                            }
				        }
				    }else{
				        $this->error(__('非法请求，机器信息已提交'));
				    }
					break; 
					
				// 小米App登录
				case 'app_xiaomi':
					
					break;
					
				// 苹果登录
				case 'apple':
					// 后续版本上线
					break; 
				default:
					$this->error('暂并不支持此方法登录');
			}
		}
		$this->error(__('10086非正常请求'));
    }

    /**
	 * 进一步完善资料
	 * @ApiMethod   (POST)
	 */
	public function perfect()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
		    $post = $this->request->post();
			
		    // 判断token没有绑定 1.1.4升级
		    $third = model('app\api\model\wanlshop\Third')
				->where('token', '=', $post['token'])
				->find();
				
		    // 当user_id 不为空可以绑定
    		if($third['user_id'] == 0 && $third){
    		    $username = $post['nickName'];
				$auth = [];
    		    $mobile = '';
    		    $gender = $post['gender'];
        	    $avatar = $post['avatarUrl'];
				// 1.1.9升级
				if ($username) {
					$usernameCheck = true;
					try{
					    $security = Easywechat::app()
							->content_security
							->checkText($username);
						if($security['errcode'] == 87014){
							$usernameCheck = false;
						}
					} catch (\Exception $e) {
						$this->error('内容审核失败：可能后台小程序的appid、appsecret配置错误，具体：'. $e->getMessage());
					}
					if(!$usernameCheck){
						$this->error(__('风控审核：用户名包含敏感词汇'));
					}
				}
				// 1.1.4升级
				if($third['unionid']){
					// 1.1.3升级 查询其他unionid的user_id进行登录
					$unionid = model('app\api\model\wanlshop\Third')
					    ->where('id','<>', $third['id'])
					    ->where('unionid','=', $third['unionid'])
					    ->find();
					if($unionid){
						$auth = $this->auth->direct($unionid['user_id']);
					}else{
						$auth = $this->auth->register('u_'.Random::alnum(6), Random::alnum(), '', $mobile, [
						    'gender' => $gender, 
						    'nickname' => $username, 
						    'avatar' => $avatar
						]);
					}
				}else{
					$auth = $this->auth->register('u_'.Random::alnum(6), Random::alnum(), '', $mobile, [
					    'gender' => $gender, 
					    'nickname' => $username, 
					    'avatar' => $avatar
					]);
				}
        		if ($auth) {
					// 1.1.4升级
					if (isset($post['client_id']) && $post['client_id'] != null) {
					    $this->wanlchat->bind($post['client_id'], $this->auth->id);
					}
        			// 更新第三方登录
        			$third->save([
        		        'user_id' => $this->auth->id,
        		        'openname' => $username
        		    ]);
        			$this->success(__('Sign up successful'), self::userInfo());
        		} else {
        			$this->error($this->auth->getError());
        		}
    		}else{
    		    $this->error(__('非法请求，机器信息已提交'));
    		}
		}
		$this->error(__('非法请求'));
	}
	
	/**
	 * 刷新用户中心
	 * @ApiMethod   (POST)
	 */
	public function refresh()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$this->success(__('刷新成功'), self::userInfo());
		}
		$this->error(__('非法请求'));
	}
	
	/**
	 * 数据统计 - 内部使用，开发者不要调用
	 */
	private function userInfo()
	{
		$user_id = $this->auth->id;
		// 查询订单
		$order = model('app\api\model\wanlshop\Order')
			->where('user_id', $user_id)
			->select();
		$orderCount = array_count_values(array_column($order,'state'));
		
		// 物流列表
		$logistics = [];
		foreach ($order as $value)
		{
			if($value['state'] >=3 && $value['state'] <=6){
				//需要查询的订单
			}
		}
		// 统计数量
		$collection = [];
		$concern = [];
		// 1.1.0升级
		$footgoodsprint = [];
		$footgroupsprint = [];
		foreach (model('app\api\model\wanlshop\GoodsFollow')->where('user_id', $user_id)->select() as $row) {
			if($row['goods_type'] === 'goods'){
				if(model('app\api\model\wanlshop\Goods')->get($row['goods_id'])){
					$collection[] = $row['id'];
				}
			}else if($row['goods_type'] === 'groups'){
				if(model('app\api\model\wanlshop\groups\Goods')->get($row['goods_id'])){
					$collection[] = $row['id'];
				}
			}
		}
		// 1.0.8升级  通过uuid查询足迹
		$uuid = $this->request->server('HTTP_UUID');
		if(!isset($uuid)){
			$charid = strtoupper(md5($this->request->header('user-agent').$this->request->ip()));
			$uuid = substr($charid, 0, 8).chr(45).substr($charid, 8, 4).chr(45).substr($charid,12, 4).chr(45).substr($charid,16, 4).chr(45).substr($charid,20,12);
		}
		foreach (model('app\api\model\wanlshop\Record')->where('uuid', $uuid)->select() as $row) {
			if($row['goods_type'] === 'goods'){
				if(model('app\api\model\wanlshop\Goods')->get($row['goods_id'])){
					$footgoodsprint[] = $row['goods_id'];
				}
			}else if($row['goods_type'] === 'groups'){
				if(model('app\api\model\wanlshop\groups\Goods')->get($row['goods_id'])){
					$footgroupsprint[] = $row['goods_id'];
				}
			}
		}
		
		// 查询动态 、收藏夹、关注店铺、足迹、红包卡券
		$finish =  isset($orderCount[6]) ? $orderCount[6] : 0;
		$pay = isset($orderCount[1]) ? $orderCount[1] : 0;
		$delive = isset($orderCount[2]) ? $orderCount[2] : 0;
		$receiving = isset($orderCount[3]) ? $orderCount[3] : 0;
		$evaluate = isset($orderCount[4]) ? $orderCount[4] : 0;
		// 订单状态:1=待支付,2=待成团,3=待发货,4=待收货,5=待评论,6=已完成,7=已取消
		$groups = model('app\api\model\wanlshop\groups\Order')
			->where('user_id', 'eq', $user_id)
			->where('state', 'neq', 7)
			->count();
	    return [
			'userinfo' => $this->auth->getUserinfo(),
			'statistics' => [
				'dynamic' => [
					'collection' => count($collection),
					'concern' => model('app\api\model\wanlshop\find\Follow')->where('user_id', $user_id)->count(),
					'footprint' => count(array_flip($footgoodsprint)) + count(array_flip($footgroupsprint)),
					'coupon' => model('app\api\model\wanlshop\CouponReceive')->where(['user_id' => $user_id, 'state' => '1'])->count(),
					'accountbank' => model('app\api\model\wanlshop\PayAccount')->where('user_id', $user_id)->count()
				],
				'order' => [
					'whole' => $finish + $pay + $delive + $receiving + $evaluate,
					'groups' => $groups,
					'pay' => $pay,
					'delive' => $delive,
					'receiving' => $receiving,
					'evaluate' => $evaluate,
					// 1.1.6升级 退款状态:0=申请退款,1=卖家同意,2=卖家拒绝,3=申请平台介入,4=成功退款,5=退款已关闭,6=已提交物流,7=第三方退款中,8=退款失败
					'customer' => model('app\api\model\wanlshop\Refund')->where(['state' => ['in','0,1,2,3,6,7,8'], 'user_id' => $this->auth->id])->count()
				],
				'logistics' => $logistics
			]
		];
	}

    /**
     * 获取个人用户信息
     */
    public function getUserInfo()
    {
        $user_id = $this->auth->id;
        // 查询订单
        $order = model('app\api\model\wanlshop\groups\Order')
            ->where('status','normal')
            ->where('user_id', $user_id)
            ->select();
        $orderCount = array_count_values(array_column($order,'state'));
        // 统计数量
        $collection = [];
        $concern = [];
        // 1.1.0升级
        $footgoodsprint = [];
        $footgroupsprint = [];
        foreach (model('app\api\model\wanlshop\GoodsFollow')->where('user_id', $user_id)->select() as $row) {
            if($row['goods_type'] === 'goods'){
                if(model('app\api\model\wanlshop\Goods')->get($row['goods_id'])){
                    $collection[] = $row['id'];
                }
            }else if($row['goods_type'] === 'groups'){
                if(model('app\api\model\wanlshop\groups\Goods')->get($row['goods_id'])){
                    $collection[] = $row['id'];
                }
            }
        }
        // 1.0.8升级  通过uuid查询足迹
        $uuid = $this->request->server('HTTP_UUID');
        if(!isset($uuid)){
            $charid = strtoupper(md5($this->request->header('user-agent').$this->request->ip()));
            $uuid = substr($charid, 0, 8).chr(45).substr($charid, 8, 4).chr(45).substr($charid,12, 4).chr(45).substr($charid,16, 4).chr(45).substr($charid,20,12);
        }
        foreach (model('app\api\model\wanlshop\Record')->where('uuid', $uuid)->select() as $row) {
            if($row['goods_type'] === 'goods'){
                if(model('app\api\model\wanlshop\Goods')->get($row['goods_id'])){
                    $footgoodsprint[] = $row['goods_id'];
                }
            }else if($row['goods_type'] === 'groups'){
                if(model('app\api\model\wanlshop\groups\Goods')->get($row['goods_id'])){
                    $footgroupsprint[] = $row['goods_id'];
                }
            }
        }


        // 查询动态 、收藏夹、关注店铺、足迹、红包卡券
        $finish =  isset($orderCount[6]) ? $orderCount[6] : 0;
        $pay = isset($orderCount[1]) ? $orderCount[1] : 0;//待支付
        $delive = isset($orderCount[3]) ? $orderCount[3] : 0;//待发货
        $receiving = isset($orderCount[4]) ? $orderCount[4] : 0;//待收货
        $evaluate = isset($orderCount[5]) ? $orderCount[5] : 0;//待评论
        $complete = isset($orderCount[6]) ? $orderCount[6] : 0;//完成

        // 订单状态:1=待支付,2=待成团,3=待发货,4=待收货,5=待评论,6=已完成,7=已取消
        // $groups = model('app\api\model\wanlshop\groups\Order')
        //     ->where('user_id', 'eq', $user_id)
        //     ->where('state', 'neq', 7)
        //     ->count();
        $userInfo = $this->auth->getUserinfo();
        //获取此用户待结算金额
        $djs_money = 0;
        $yjs_money = 0;
        if($userInfo['is_sqdl'] == 1){
            $child_user = UserModel::getAllNonAgentDescendants($user_id);

            $djs_order = model('app\api\model\service\Order')->where('user_id','in',$child_user)->where('status','in','2,3,4,5')->field('goods_id,payprice')->select();
            foreach ($djs_order as $row){
                $goods = Goods::where('id',$row['goods_id'])->field('id,shequ_bili,quyu_bili')->find();
                if($goods['shequ_bili']){
                    $sq_money = truncateDecimal($row['payprice'] * ($goods['shequ_bili'] / 100));
                }else{
                    $sq_money = truncateDecimal($row['payprice'] * (config('site.shequ_bili') / 100));
                }
                $djs_money += $sq_money;
            }
        }
        $qydl_djs_money  = 0;
        //城市运营商
        if($userInfo['is_qydl'] == 1){
            //获取此城市运营商代理区域
            $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
            $userInfo['district'] = $district;
            if($district){
                $qydl_djs_order = model('app\api\model\service\Order')->alias('o')
                    ->join('service_order_address d','d.order_id=o.id','left')
                    ->where('o.user_id',$user_id)
                    ->where('o.status','in','2,3,4,5')
                    ->where('d.district',$district)
                    ->field('o.goods_id,o.payprice')->select();
                foreach ($qydl_djs_order as $row){
                    $goods = Goods::where('id',$row['goods_id'])->field('id,shequ_bili,quyu_bili')->find();
                    if($goods['quyu_bili']){
                        $qy_money = truncateDecimal($row['payprice'] * ($goods['quyu_bili'] / 100));
                    }else{
                        $qy_money = truncateDecimal($row['payprice'] * (config('site.quyu_bili') / 100));
                    }
                    $qydl_djs_money += $qy_money;
                }
            }
        }
        $yjs_money = MoneyLog::where('user_id',$user_id)->where('type','fenyong')->sum('money');
        $userInfo['qydl_djs_money'] = $qydl_djs_money;//城市运营商待结算金额
        $userInfo['djs_money'] = $djs_money;//养老院长待结算
        $userInfo['yjs_money'] = $yjs_money;//已结算
        $userInfo['bzj_money'] = 0;//保证金

        // $djs_order = model('app\api\model\service\Order')->

        $list = [
            'userinfo' => $userInfo,
            'statistics' => [
                'dynamic' => [
                    'collection' => count($collection),
                    'concern' => model('app\api\model\wanlshop\find\Follow')->where('user_id', $user_id)->count(),
                    'footprint' => count(array_flip($footgoodsprint)) + count(array_flip($footgroupsprint)),
                    'coupon' => model('app\api\model\wanlshop\CouponReceive')->where(['user_id' => $user_id, 'state' => '1'])->count(),
                    'accountbank' => model('app\api\model\wanlshop\PayAccount')->where('user_id', $user_id)->count()
                ],
                'order' => [
                    'whole' => $finish + $pay + $delive + $receiving + $evaluate,
                    // 'groups' => $groups,
                    'pay' => $pay,
                    'delive' => $delive,
                    'receiving' => $receiving,
                    'evaluate' => $evaluate,
                    'complete' => $complete,
                    // 1.1.6升级 退款状态:0=申请退款,1=卖家同意,2=卖家拒绝,3=申请平台介入,4=成功退款,5=退款已关闭,6=已提交物流,7=第三方退款中,8=退款失败
                    'customer' => model('app\api\model\wanlshop\Refund')->where(['state' => ['in','0,1,2,3,6,7,8'], 'user_id' => $this->auth->id])->count()
                ],
                // 'logistics' => $logistics
            ]
        ];
        $this->success('查询成功',$list);
    }
	
	/**
	 * 获取评论列表
	 *
	 * @ApiSummary  (WanlShop 获取我的所有评论)
	 * @ApiMethod   (GET)
	 * 
	 * @param string $list_rows  每页数量
	 * @param string $page  当前页
	 */
	public function comment()
	{
		$list = model('app\api\model\wanlshop\GoodsComment')
			->where('user_id', $this->auth->id)
			->field('id,images,score,goods_id,order_goods_id,state,content,createtime')
			->order('createtime desc')
			->paginate()
			->each(function($data, $key){
				$data['order_goods'] = $data->order_goods ? $data->order_goods->visible(['id','title','image','price']):'';
				return $data;
			});
		$this->success('返回成功', $list);
	}
	
	/**
	 * 获取积分明细
	 */
	public function scoreLog()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$list = model('app\common\model\ScoreLog')
				->where('user_id', $this->auth->id)
				->order('createtime desc')
				->paginate();
			$this->success('ok',$list);
		}
		$this->error(__('非法请求'));
	}

	/**
	 * 生成证书
	 * @ApiMethod   (POST)
	 * @param string $name 姓名
	 * @param string $idcard 身份证号
	 * @param string $identity 身份类型 1=养老院长 2=社区代理 3=区域代理
	 */
	public function generateCertificate()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$name = $this->request->post('name', '', 'trim');
			$idcard = $this->request->post('idcard', '', 'trim');
			$identity = $this->request->post('identity', 1, 'intval');

			if (!$name) {
				$this->error('请输入姓名');
			}

			if (!$idcard) {
				$this->error('请输入身份证号');
			}

			// 验证身份证号格式
			if (!$this->validateIdCard($idcard)) {
				$this->error('身份证号格式不正确');
			}

			// 验证身份类型
			if (!in_array($identity, [1, 2, 3])) {
				$this->error('身份类型不正确');
			}

			// 生成证书
			$certificateResult = $this->createCertificate($name, $idcard, $identity);

			if ($certificateResult && is_array($certificateResult)) {
				// 更新用户证书字段
				$user = $this->auth->getUser();
				$user->ylgw_zs = $certificateResult['url'];
				$user->ylgw_number_no = $certificateResult['certificate_no']; // 保存证书编号
				$user->realname = $name;
				$user->card_no = $idcard;
				$user->save();

				$this->success('证书生成成功', [
					'certificate_url' => $certificateResult['url'],
					'certificate_no' => $certificateResult['certificate_no'],
					'name' => $name,
					'idcard' => $idcard,
					'identity' => $identity
				]);
			} 
		}
		$this->error(__('非法请求'));
	}

	/**
	 * 验证身份证号
	 * @param string $idcard
	 * @return bool
	 */
	private function validateIdCard($idcard)
	{
		// 简单的身份证号验证
		if (strlen($idcard) != 18) {
			return false;
		}

		// 验证前17位是否为数字
		if (!is_numeric(substr($idcard, 0, 17))) {
			return false;
		}

		// 验证最后一位
		$lastChar = substr($idcard, 17, 1);
		if (!is_numeric($lastChar) && strtoupper($lastChar) != 'X') {
			return false;
		}

		return true;
	}

	/**
	 * 创建证书图片
	 * @param string $name 姓名
	 * @param string $idcard 身份证号
	 * @param int $identity 身份类型
	 * @return array 返回证书信息数组包含URL和编号
	 */
	private function createCertificate($name, $idcard, $identity)
	{
		// 生成证书编号
		$certificateNo =$this->getCartNo();

		// 证书模板路径配置
		$templates = [
			1 => '/assets/img/certificate/ylgw_template.jpg',  // 养老院长模板
			3 => '/assets/img/certificate/sqdl_template.jpg',  // 社区代理模板
			2 => '/uploads/20250624/bdb722d333622b8299468fbb0d252688.jpg'   // 区域代理模板
		];
		
		// 修正路径拼接方式
		$ds = DIRECTORY_SEPARATOR;
		$publicPath = ROOT_PATH . 'public' . $ds;

		// 获取模板路径
		$templatePath = $publicPath . str_replace('/', $ds, ltrim($templates[$identity], '/'));


		// 检查模板文件是否存在
		if (!file_exists($templatePath)) {
			throw new \Exception('证书模板文件不存在: ' . $templatePath);
		}

		// 创建图片资源
		$imageInfo = getimagesize($templatePath);
		$imageType = $imageInfo[2];

		switch ($imageType) {
			case IMAGETYPE_JPEG:
				$templateImage = imagecreatefromjpeg($templatePath);
				break;
			case IMAGETYPE_PNG:
				$templateImage = imagecreatefrompng($templatePath);
				break;
			case IMAGETYPE_GIF:
				$templateImage = imagecreatefromgif($templatePath);
				break;
			default:
				throw new \Exception('不支持的图片格式');
		}

		if (!$templateImage) {
			throw new \Exception('无法从模板创建图片资源，请检查PHP GD库是否正常。');
		}

		// 设置字体颜色（黑色）
		$textColor = imagecolorallocate($templateImage, 0, 0, 0);

		// 字体文件路径（优先使用存在的字体）
		$fontPaths = [
			$publicPath . 'assets' . $ds . 'fonts' . $ds . 'kaiti.ttf',
	
			$publicPath . 'assets' . $ds . 'fonts' . $ds . 'SourceHanSansK-Regular.ttf',
			
			$publicPath . 'assets' . $ds . 'fonts' . $ds . 'verdana.ttf'
		];

		$fontPath = '';
		$useFont = false;
		foreach ($fontPaths as $path) {
			if (file_exists($path)) {
				$fontPath = $path;
				$useFont = true;
				break;
			}
		}

		// 根据身份类型设置文字位置
		$positions = $this->getCertificatePositions($identity);

		if ($useFont) {
			// 使用TTF字体
			if (imagettftext($templateImage, $positions['name_size'], 0,
						$positions['name_x'], $positions['name_y'],
						$textColor, $fontPath, $name) === false) {
				throw new \Exception('写入姓名失败，请检查PHP GD库FreeType支持是否开启。');
			}

			if (imagettftext($templateImage, $positions['idcard_size'], 0,
						$positions['idcard_x'], $positions['idcard_y'],
						$textColor, $fontPath, $idcard) === false) {
				throw new \Exception('写入身份证号失败，请检查PHP GD库FreeType支持是否开启。');
			}

			// 添加证书编号到右上角
			if (imagettftext($templateImage, $positions['cert_no_size'], 0,
						$positions['cert_no_x'], $positions['cert_no_y'],
						$textColor, $fontPath, $certificateNo) === false) {
				throw new \Exception('写入证书编号失败，请检查PHP GD库FreeType支持是否开启。');
			}
		} else {
			// 如果没有可用的字体文件，则报错
			throw new \Exception('未找到可用的中文字体文件，无法生成证书。');
		}

		// 生成文件名
		$fileName = 'certificate_' . $this->auth->id . '_' . time() . '.jpg';
		$savePath = ROOT_PATH . 'public/uploads/certificate/';

		// 创建目录
		if (!is_dir($savePath)) {
			mkdir($savePath, 0755, true);
		}

		$fullPath = $savePath . $fileName;

		// 保存图片
		$result = imagejpeg($templateImage, $fullPath, 90);

		// 释放内存
		imagedestroy($templateImage);

		if ($result) {
			// 上传到OSS
			$ossUrl = $this->uploadCertificateToOss($savePath . $fileName, '/uploads/certificate/' . $fileName);

			// 返回证书信息数组
			return [
				'url' => $ossUrl ?: '/uploads/certificate/' . $fileName,
				'certificate_no' => $certificateNo
			];
		}

		return false;
	}

	/**
	 * 获取证书文字位置配置
	 * @param int $identity 身份类型
	 * @return array
	 */
	private function getCertificatePositions($identity)
	{
		// 根据不同身份类型返回不同的文字位置
		// 这些坐标需要根据实际的证书模板图片来调整
		$positions = [
			1 => [ // 养老院长
				'name_x' => 770,
				'name_y' => 1150,
				'name_size' => 70,
				'idcard_x' => 770,
				'idcard_y' => 1320,
				'idcard_size' => 60,
				'cert_no_x' => 1700,  // 证书编号X坐标（右上角）
				'cert_no_y' => 470,   // 证书编号Y坐标
				'cert_no_size' => 50 // 证书编号字体大小
			],
			2 => [ // 社区代理
				'name_x' => 770,
				'name_y' => 1150,
				'name_size' => 70,
				'idcard_x' => 770,
				'idcard_y' => 1320,
				'idcard_size' => 60,
				'cert_no_x' => 1700,  // 证书编号X坐标（右上角）
				'cert_no_y' => 470,   // 证书编号Y坐标
				'cert_no_size' => 50 // 证书编号字体大小
			],
			3 => [ // 区域代理
			    'name_x' => 770,
				'name_y' => 1150,
				'name_size' => 70,
				'idcard_x' => 770,
				'idcard_y' => 1320,
				'idcard_size' => 60,
				'cert_no_x' => 1700,  // 证书编号X坐标（右上角）
				'cert_no_y' => 470,   // 证书编号Y坐标
				'cert_no_size' => 50 // 证书编号字体大小
			]
		];

		return $positions[$identity];
	}

	

	/**
	 * 验证证书编号
	 * @ApiMethod   (POST)
	 * @param string $certificate_no 证书编号
	 */
	public function verifyCertificate()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$certificateNo = $this->request->post('certificate_no', '', 'trim');

			if (!$certificateNo) {
				$this->error('请输入证书编号');
			}

			try {
				// 查询证书信息
				$user = \app\common\model\User::where('ylgw_number_no', $certificateNo)->find();

				if ($user) {
					// 解析证书编号获取身份类型
					$identityMap = [
						'YLGW' => '养老院长',
						'SQDL' => '社区代理',
						'QYDL' => '区域代理'
					];

					$prefix = substr($certificateNo, 0, 4);
					$identityText = isset($identityMap[$prefix]) ? $identityMap[$prefix] : '未知';

					$this->success('证书验证成功', [
						'certificate_no' => $certificateNo,
						'name' => $user->realname,
						'identity' => $identityText,
						'certificate_url' => $user->ylgw_zs,
						'issue_date' => substr($certificateNo, 4, 8), // 从编号中提取日期
						'status' => '有效'
					]);
				} else {
					$this->error('证书编号不存在或无效');
				}
			} catch (\Exception $e) {
				$this->error('证书验证失败：' . $e->getMessage());
			}
		}
		$this->error(__('非法请求'));
	}

	/**
	 * 上传证书图片到OSS
	 * @param string $localFilePath 本地文件路径
	 * @param string $ossFilePath OSS文件路径
	 * @return string|false 返回OSS文件URL或false
	 */
	private function uploadCertificateToOss($localFilePath, $ossFilePath)
	{
		try {
			// 检查本地文件是否存在
			if (!file_exists($localFilePath)) {
				return false;
			}

			// 获取OSS配置
			$config = get_addon_config('alioss');
			if (empty($config) || !isset($config['accessKeyId'])) {
				return false;
			}

			// 读取文件内容
			$fileData = file_get_contents($localFilePath);
			if ($fileData === false) {
				return false;
			}

			// 使用OSS客户端上传
			$accessKeyId = $config['accessKeyId'];
			$accessKeySecret = $config['accessKeySecret'];
			$endpoint = $config['endpoint'];
			$bucket = $config['bucket'];

			$ossClient = new \OSS\OssClient($accessKeyId, $accessKeySecret, $endpoint);
			$result = $ossClient->putObject($bucket, ltrim($ossFilePath, '/'), $fileData);

			if ($result) {
				// 删除本地文件（可选）
				@unlink($localFilePath);

				// 返回OSS文件URL
				$cdnUrl = $config['cdnurl'] ?? '';
				if ($cdnUrl) {
					return rtrim($cdnUrl, '/') . $ossFilePath;
				} else {
					return 'https://' . $bucket . '.' . $endpoint . $ossFilePath;
				}
			}

			return false;
		} catch (\Exception $e) {
			// 记录错误日志
			\think\Log::error('证书上传OSS失败: ' . $e->getMessage());
			return false;
		}
	}

}