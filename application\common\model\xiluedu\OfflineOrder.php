<?php

namespace app\common\model\xiluedu;

use app\common\library\Auth;
use app\common\model\User;
use think\Db;
use think\Exception;
use think\Hook;
use think\Model;
use function fast\array_get;


class OfflineOrder extends Model
{
    // 表名
    protected $name = 'xiluedu_offline_order';

    protected $autoWriteTimestamp = 'int';

    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    protected $append = [
        'state',
        'state_text',
        'course_start_time_text',
        'course_end_time_text',
    ];

    public function user() {
        return $this->belongsTo(User::class, 'user_id')->setEagerlyType(0);
    }

    public function offlineCourse() {
        return $this->belongsTo('offlineCourse', 'course_id')->setEagerlyType(1);
    }

    public function getCourseStartTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['course_start_time']) ? $data['course_start_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i", $value) : $value;
    }

    public function getCourseEndTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['course_end_time']) ? $data['course_end_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i", $value) : $value;
    }

    public function getStateAttr($value,$data){
        if($data['course_start_time'] > time()){
            return 1; //即将开始
        }else if($data['course_start_time'] < time()){
            return 2; //已经开始
        }else{
            return 0; //已经过期
        }
    }

    public function getStateTextAttr($value,$data){
        $state_attr = [1 => '未开始', 2 => '进行中', 3 => '已结束'];
        $state = $this->getAttr('state');
        return $state_attr[$state];
    }

    /**
     * 报名订单
     * @param $params
     * @return OfflineOrder
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function createOrder($params) {
        $offline_course_id = array_get($params,'offline_course_id');
        $username = array_get($params,'username');
        $mobile = array_get($params,'mobile');
        $remark = array_get($params,'remark');
        if(!$offline_course_id || !$username || !$mobile){
            throw new Exception("非法参数");
        }
        $offline_course = OfflineCourse::get($offline_course_id);
        if(!$offline_course || $offline_course->status == '0'){
            throw new Exception("课程不存在或已下架");
        }
        if($offline_course->state == 3){
            throw new Exception("课程已结束");
        }
        $count = self::where('course_id','=',$offline_course_id)->count();
        if($offline_course['total_count'] <= $count){
            throw new Exception("课程人数已满");
        }
        $auth = Auth::instance();
        #报名数据
        $data = [
            'platform'          =>  array_get($params,'platform','wxpublic'),
            'user_id'           =>  $auth->id,
            'order_no'          =>  "B".date('YmdHis').mt_rand(10,9999),
            'pay_price'         =>  $offline_course->salesprice,
            'username'          =>  $username,
            'mobile'            =>  $mobile,
            'remark'            =>  $remark,
            'course_id'         =>  $offline_course_id,
            'course_name'       =>  $offline_course->name,
            'course_introduce'  =>  $offline_course->introduce,
            'course_image'      =>  $offline_course->thumb_image,
            'course_salesprice' =>  $offline_course->salesprice,
            'course_teacher_id' =>  $offline_course->teacher_id,
            'course_teacher_name'=>  $offline_course->teacher ? $offline_course->teacher->name : '',
            'course_content'    =>  $offline_course->content,
            'course_start_time' =>  $offline_course->start_time,
            'course_end_time'   =>  $offline_course->end_time,
            'course_address'    =>  $offline_course->address,
            'course_mobile'     =>  $offline_course->mobile,
            'course_is_ylgw'     =>  $offline_course->is_ylgw,
            'ip'                =>  request()->ip()
        ];
        $offline_order = self::create($data);
        Hook::listen("xiluedu_order_divide",$offline_order,['type'=>'offline']);
        return $offline_order;
    }
    public static function lists($params){
        $auth = Auth::instance();
        $tab = array_get($params,'tab');
        $course_id = array_get($params,'course_id');
        if($course_id){
            $where['course_id'] = $course_id;
        }
        $pagesize = array_get($params,'pagesize',10);
        $is_my = array_get($params,'is_my');
        if($is_my){
            $where['user_id'] = $auth->id;
        }
        // $where['user_id'] = $auth->id;
        // if($tab == 0){
        //     $where['pay_status'] = 0;
        // }else if($tab == 1){
        //     $where['pay_status'] = 1;
        //     $where['refund_status'] = 0;
        // }else if($tab == 2){
        //     #已退款
        //     $where['pay_status'] = 1;
        //     $where['refund_status'] = 3;
        // }
        return static::field('wxconfig,transaction_id',true)
            ->where($where)
            ->order('paytime','desc')
            ->paginate($pagesize);
    }

    public static function detail($order_id){

        return self::where('id',$order_id)
            ->find();
    }

    /**
     * 线下课程
     * @param $params
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public static function offlineOrders($params){
        $auth = Auth::instance();
        $pagesize = array_get($params,'pagesize',10);
        return static::with(['offlineCourse'=>function($query){
            $query->withField(['id','name','address','start_time','end_time','thumb_image','total_count','enroll_count']);
        }])
            ->field('wxconfig,transaction_id',true)
            ->where('user_id',$auth->id)
            //->where('pay_status','1')
            ->order('paytime','desc')
            ->paginate($pagesize);
    }


    public static function pay_notify($order_no){
        $order = self::where('order_trade_no',$order_no)->find();
        if(!$order){
            throw new Exception('订单不存在');
        }
        if($order->pay_status == '1'){
            throw new Exception('订单已支付完毕');
        }

        //如果是养老院长则把当前用户设为代理
        if($order->course_is_ylgw == 1){
            $user = User::get($order->user_id);
            if($user->is_sqdl == 0) {
                $user->is_sqdl = 1;
                $user->save();
            }
        }

        #分销佣金更新为已支付、待分配
        if($divide = OrderDivide::checkExist($order->id,'offline')){
            $divide->save(['status'=>2]);
        }
        $order->pay_status = '1';
        $order->paytime = time();
        $order->save();
        Hook::listen("xiluedu_offline_buy",$order);
        Hook::listen("xiluedu_divide",$order,['type'=>'offline']);

        if($order->pay_price > 0) {
            self::shareProfit($order->id, 2);
        }

        return $order;
    }

    public static function shareProfit($order_id,$type=1){
        $order = OfflineOrder::where(['pay_status' => 1, 'id' => $order_id])->find();
        if (!$order)  return;
        $goods = OfflineCourse::where('id', $order['course_id'])->field('id,shequ_bili,quyu_bili,service_bili')->find();

        if($goods['service_bili']){
            $service_money = truncateDecimal($order['pay_price'] * ($goods['service_bili'] / 100));
        }else{
            $service_money = truncateDecimal($order['pay_price'] * (config('site.course_teacher_rate') / 100));
        }
        $residue=$order['pay_price']-$service_money;
        if($goods['shequ_bili'] && $goods['shequ_bili']>0){
            $sq_money = truncateDecimal($residue * ($goods['shequ_bili'] / 100));
        }else{
            $sq_money = truncateDecimal($residue * (config('site.course_shequ_rate') / 100));
        }
        if($goods['quyu_bili'] && $goods['quyu_bili']>0){
            $qy_money = truncateDecimal($residue * ($goods['quyu_bili'] / 100));
        }else{
            $qy_money = truncateDecimal($residue * (config('site.course_quyu_rate') / 100));
        }
        // 对于课程10，重新计算分佣金额
        if($order['course_id'] == 10) {
            // 课程10特殊处理：养老院长分佣 = 课程价格 * 50% = 3400元
            $sq_money = truncateDecimal($order['pay_price'] * 0.5);
        }

        // 计算养老顾问分成 - 基于养老院长分佣金额的50%
        // 对于课程10(6800元)，养老院长分佣应该是3400元，养老顾问分佣应该是1700元
        if($order['course_id'] == 10) {
            // 养老顾问分佣 = 养老院长分佣 * 50% = 1700元
            $ylgw_money = truncateDecimal($sq_money * 0.5);
        } else {
            // 其他课程按原有逻辑计算
            $ylgw_money = truncateDecimal($sq_money * (config('site.gjia_bili') / 100));
        }
        //发放养老院长佣金
        if($sq_money > 0){
            $user = User::where('id',$order['user_id'])->field('id,parent_id,is_sqdl')->find();

            // 如果购买用户自己就是养老院长
            if($user['is_sqdl'] == 1) {
                // 购买用户自己是养老院长，不发放分佣（自己买自己的不分佣）
                // 但保留$sq_money用于计算养老顾问分佣
            } else {
                // 如果购买用户不是养老院长，查找上级养老院长
                if($user['parent_id']){
                    $sq_user = \app\common\model\User::getOneSq($order['user_id']);
                    if(!empty($sq_user)) {
                        \app\common\model\User::money($sq_money,$sq_user['id'],'养老院长分成',2,$order['order_no'],'fenyong');
                        $residue-=$sq_money;
                    }
                }
            }
        }

        //发放养老顾问佣金
        if($ylgw_money > 0){
            $ylgw_user = \app\common\model\User::getOneYlgw($order['user_id']);
            if(!empty($ylgw_user)) {
                // 检查找到的养老顾问是否就是购买用户自己，且购买用户已经是养老院长
                $purchase_user = User::where('id',$order['user_id'])->field('id,parent_id,is_sqdl')->find();
                if($ylgw_user['id'] == $order['user_id'] && $purchase_user['is_sqdl'] == 1) {
                    // 如果购买用户自己就是养老顾问且已升级为养老院长，跳过养老顾问分佣
                    // 避免重复分佣，优先保证养老院长分佣
                } else {
                    // 检查是否是推荐分佣（购买用户的直接上级就是养老顾问）
                    $is_referral_commission = ($purchase_user['parent_id'] == $ylgw_user['id']);

                    // 无论是推荐分佣还是普通分佣，都要检查养老顾问是否有上级
                    $ylgw_parent = \app\common\model\User::where('id', $ylgw_user['id'])->field('parent_id,is_sqdl,is_qydl')->find();
                    $has_superior = false;

                    if($ylgw_parent && $ylgw_parent['parent_id'] > 0) {
                        // 检查上级是否是养老院长或城市运营商
                        $parent_user = \app\common\model\User::where('id', $ylgw_parent['parent_id'])->field('is_sqdl,is_qydl')->find();
                        if($parent_user && ($parent_user['is_sqdl'] == 1 || $parent_user['is_qydl'] == 1)) {
                            $has_superior = true;
                        }
                    }

                    if($is_referral_commission) {
                        if (!$has_superior) {
                            // 推荐分佣且无上级：直接发放到账户余额
                            \app\common\model\User::money($ylgw_money,$ylgw_user['id'],'养老顾问推荐分成',2,$order['order_no'],'fenyong');
                            $residue-=$ylgw_money;
                        } else {
                            // 推荐分佣但有上级：按线下分佣处理
                            $parent_user_full = \app\common\model\User::where('id', $ylgw_user['parent_id'])->find();
                            if ($parent_user_full) {
                                // 1. 佣金实际分给上级账户
                                \app\common\model\User::money($ylgw_money, $parent_user_full['id'], '下级养老顾问推荐分佣-课程(来自用户' . $order['user_id'] . ')', 2, $order['order_no'], 'fenyong');

                                // 2. 记录养老顾问应得金额
                                $ylgw_user_full = \app\common\model\User::where('id', $ylgw_user['id'])->field('ylgw_total_commission')->find();
                                $current_commission = $ylgw_user_full['ylgw_total_commission'] ?: 0;
                                \app\common\model\User::where('id', $ylgw_user['id'])->setInc('ylgw_total_commission', $ylgw_money);

                                // 3. 记录养老顾问的应得分佣详情
                                \app\common\model\MoneyLog::create([
                                    'user_id' => $ylgw_user['id'],
                                    'money' => $ylgw_money,
                                    'before' => $current_commission,
                                    'after' => $current_commission + $ylgw_money,
                                    'memo' => '应得推荐分佣-线下课程(已分给上级' . $parent_user_full['id'] . ',待线下分账)',
                                    'type' => 'pending_commission',
                                    'createtime' => time()
                                ]);
                                $residue-=$ylgw_money;
                            }
                        }
                    } else {
                        // 普通分佣：使用前面已经检查的上级状态
                        if(!$has_superior) {
                            // 普通分佣且无上级：直接发放到账户余额
                            \app\common\model\User::money($ylgw_money,$ylgw_user['id'],'养老顾问分成',2,$order['order_no'],'fenyong');
                            $residue-=$ylgw_money;
                        } else {
                            // 普通分佣且有上级：按线下分佣处理
                            $parent_user_full = \app\common\model\User::where('id', $ylgw_user['parent_id'])->find();
                            if ($parent_user_full) {
                                // 1. 佣金实际分给上级账户
                                \app\common\model\User::money($ylgw_money, $parent_user_full['id'], '下级养老顾问分佣-课程(来自用户' . $order['user_id'] . ')', 2, $order['order_no'], 'fenyong');

                                // 2. 记录养老顾问应得金额
                                $ylgw_user_full = \app\common\model\User::where('id', $ylgw_user['id'])->field('ylgw_total_commission')->find();
                                $current_commission = $ylgw_user_full['ylgw_total_commission'] ?: 0;
                                \app\common\model\User::where('id', $ylgw_user['id'])->setInc('ylgw_total_commission', $ylgw_money);

                                // 3. 记录养老顾问的应得分佣详情
                                \app\common\model\MoneyLog::create([
                                    'user_id' => $ylgw_user['id'],
                                    'money' => $ylgw_money,
                                    'before' => $current_commission,
                                    'after' => $current_commission + $ylgw_money,
                                    'memo' => '应得分佣-课程(已分给上级' . $parent_user_full['id'] . ',待线下分账)',
                                    'type' => 'pending_commission',
                                    'createtime' => time()
                                ]);
                                $residue-=$ylgw_money;
                            }
                        }
                    }
                }
            }
        }

        //发放城市运营商佣金
//        $qy_user_id = \app\common\model\UserArea::where('district',$order['district'])->value('user_id');
        if(!empty($sq_user) && $qy_money > 0){
            \app\common\model\User::money($qy_money,$sq_user['parent_id'],'城市运营商分成',1,$order['order_no'],'fenyong');
            $residue-=$qy_money;
        }
        //放发平台收益
        if($residue>0){
            \app\common\model\User::money($residue,1,'平台收益',1,$order['order_no'],'fenyong');
        }
        //给服务者发放佣金
        if($service_money > 0){
            \app\common\model\User::money($service_money,1,'服务者获得佣金',1,$order['order_no'],'fenyong');
        }



//        self::where(['id'=>$params['id']])->update(['user_confirm_time'=>time(),'shequ_money'=>$sq_money,'quyu_money'=>$qy_money,'service_money'=>$service_money,'user_confirm'=>1]);
        //发放分佣end

        // $userOpenid = UserInfo::getOpenid($order['user_id'],0);
        // Order::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'note'=>'有问题请联系客服','type'=>0,'templateAttr'=>'user_order_template','openid'=>$userOpenid]);
    }

}
