<style type="text/css">
    .spuBox{
        display: inline-block;
    }
    .actionBox{
        padding: 7px 15px 0;
    }
    .actionBox .add{
        color: #18bc9c;
    }
    .actionBox .del{
        color: #f75444;
        margin-right: 20px;
    }
    .spuLine{
        background: #f4f4f4;
        padding: 10px 0;
        margin: 10px 0;
        clear: both;
    }
    .tableBox{
        margin-top: 20px;
        clear: both;
    }
    .skuLine{
        clear: both;
        margin-top: 10px;
    }
</style>
<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div id="app">
        <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
            
                <label class="control-label col-xs-12 col-sm-2">分类:</label>

            
<div class="form-inline col-xs-12 col-sm-4" data-toggle="cxselect" data-selects="first,second">
<select class="first form-control" data-rule="required" name="row[category_id]" data-url="service/goods/category" >
    <option value="{$row.category_id|htmlentities}" selected=""></option>
</select>
<!--<select class="second form-control" data-rule="required" name="row[two_category_id]" data-url="service/goods/category" data-query-name="pid">-->
<!--    <option value="{$row.two_category_id|htmlentities}" selected=""></option>-->
<!--</select>-->
</div>
        </div>

        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('服务首页板块')}:</label>
            <div class="col-xs-12 col-sm-8">
                <select id="c-driver_uid" data-rule="" class="form-control selectpicker" data-live-search="true" name="row[module_id]">
                    <option  value="0" >无</option>
                    {foreach name="moduleList" item="vo"}
                    <option  value="{$vo.id}" {in name="$vo.id" value="$module_id"} selected {/in}>{$vo.name}</option>
                    {/foreach}
                </select>

            </div>
        </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Skill_cate_ids')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-skill_cate_ids" data-rule="required" min="0"  data-source="service/skill/skillcate/index" data-multiple="true" class="form-control selectpage" name="row[skill_cate_ids]" type="text" value="{$row.skill_cate_ids|htmlentities}">
        </div>
    </div>
<!--    <div class="form-group">-->
<!--            <label class="control-label col-xs-12 col-sm-2">{:__('Tag_name')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <input id="c-tag_name" data-rule="required" class="form-control" name="row[tag_name]" value="{$row.tag_name|htmlentities}" type="text" placeholder="标签用 | 符合间隔">-->
<!--            </div>-->
<!--            &lt;!&ndash;<div ><span>标签用 | 符合间隔</span></div>&ndash;&gt;-->
<!--        </div>-->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('项目左角标')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-goods_tip" data-rule="" class="form-control" name="row[goods_tip]" value="{$row.goods_tip|htmlentities}" type="text" placeholder="">
            </div>
            <!--<div ><span>标签用 | 符合间隔</span></div>-->
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('商品标签(回车确认)')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-tag_name" type="text" class="form-control" name="row[tag_name]" data-role="tagsinput" value="{$row.tag_name|htmlentities}" placeholder="回车确认"/>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('单位')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-goods_unit" data-rule="" class="form-control" name="row[unit]" value="{$row.unit|htmlentities}" type="text" placeholder="">
            </div>
            <!--<div ><span>标签用 | 符合间隔</span></div>-->
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('项目类型')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="radio">
                    {foreach name="goodsTypeList" item="vo"}
                    <label for="row[goods_type]-{$key}"><input id="row[goods_type]-{$key}" name="row[goods_type]" v-model="goods_type"  type="radio" value="{$key}" {in name="key" value="0"}checked{/in} /> {$vo}</label>
                    {/foreach}
                </div>

            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('医护服务')}:</label>
            <div class="col-xs-12 col-sm-8">

                <input  id="c-is_yh" name="row[is_yh]" type="hidden" value="{$row.is_yh}">
                <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_yh" data-yes="1" data-no="0" >
                    <i class="fa fa-toggle-on text-success {eq name="$row.is_yh" value="0"}fa-flip-horizontal text-gray{/eq} fa-2x"></i>
                </a>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('购买须知')}:</label>
            <div class="col-xs-12 col-sm-8">

                <input  id="c-is_bx" name="row[is_bx]" type="hidden" value="{$row.is_bx}">
                <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_bx" data-yes="1" data-no="0" >
                    <i class="fa fa-toggle-on text-success {eq name="$row.is_bx" value="0"}fa-flip-horizontal text-gray{/eq} fa-2x"></i>
                </a>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('是否热门')}:</label>
            <div class="col-xs-12 col-sm-8">

                <input  id="c-is_hot" name="row[is_hot]" type="hidden" value="{$row.is_hot}">
                <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_hot" data-yes="1" data-no="0" >
                    <i class="fa fa-toggle-on text-success fa-flip-horizontal text-gray fa-2x"></i>
                </a>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Choose_skill_type')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="radio">
                    {foreach name="chooseSkillTypeList" item="vo"}
                    <label for="row[choose_skill_type]-{$key}"><input id="row[choose_skill_type]-{$key}" name="row[choose_skill_type]"  v-model="choose_skill_type"  type="radio" value="{$key}" {in name="key" value="$row.choose_skill_type"}checked{/in} /> {$vo}</label>
                    {/foreach}
                </div>
                
               

            </div>
        </div>
<!--        <div class="form-group"  v-show="choose_skill_type == 1">-->
<!--            <label class="control-label col-xs-12 col-sm-2">{:__('Is_travel')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <div class="radio">-->
<!--                    {foreach name="isTravelList" item="vo"}-->
<!--                    <label for="row[is_travel]-{$key}"><input id="row[is_travel]-{$key}" name="row[is_travel]"  type="radio" value="{$key}" {in name="key" value="$row.is_travel"}checked{/in} /> {$vo}</label>-->
<!--                    {/foreach}-->
<!--                </div>-->
<!--                -->
<!--               -->

<!--            </div>-->
<!--        </div>-->

    <!--<div class="form-group">-->
    <!--    <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>-->
    <!--    <div class="col-xs-12 col-sm-8">-->
                        
    <!--        <select  id="c-type" data-rule="required" class="form-control selectpicker" name="row[type]">-->
    <!--            {foreach name="typeList" item="vo"}-->
    <!--                <option value="{$key}" {in name="key" value="$row.type"}selected{/in}>{$vo}</option>-->
    <!--            {/foreach}-->
    <!--        </select>-->

    <!--    </div>-->
    <!--</div>-->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('介绍视频')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-video" data-rule="" class="form-control" size="50" name="row[video]" type="text" value="{$row.video|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-video" class="btn btn-danger faupload" data-input-id="c-video" data-mimetype="mp4" data-multiple="false" data-preview-id="p-video"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-video" class="btn btn-primary fachoose" data-input-id="c-video" data-mimetype="mp4" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                    </div>
                    <span class="msg-box n-right" for="c-video"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-video"></ul>
            </div>
        </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" data-rule="required" class="form-control" size="50" name="row[image]" type="text" value="{$row.image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Images')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-images" data-rule="required" class="form-control" size="50" name="row[images]" type="text" value="{$row.images|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-images" class="btn btn-danger faupload" data-input-id="c-images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-images" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-images"></ul>
        </div>
    </div>

<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Response_hour')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-response_hour" data-rule="required" min="0" class="form-control" name="row[response_hour]" type="number" value="{$row.response_hour|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="form-group" v-show="choose_skill_type == 0">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Start_hour')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-start_hour" min="0" class="form-control"  name="row[start_hour]" type="number" value="{$row.start_hour|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group" v-show="choose_skill_type == 0">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('End_hour')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-end_hour" min="0" class="form-control"  name="row[end_hour]" type="number" value="{$row.end_hour|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('City')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-city" class="form-control" name="row[city]" data-toggle="city-picker"  type="text" value="{$row.city|htmlentities}" data-level="district">
            <a class="button" id="rest_city">重置</a>
<!--            <input id="c-city" class="form-control" name="row[city]" data-toggle="city-picker"  type="text" value="{$row.city|htmlentities}">-->
<!--            <input id="c-city" data-rule="required" data-search-field = "name" data-primary-key = 'name' data-multiple = "true" data-source="general/area/indexCity?level=2"-->
<!--                   data-params='{"custom[level]":"2"}' class="form-control selectpage" name="row[city]" type="text" value="{$row.city|htmlentities}">-->
        </div>
    </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('关联全国通用项目')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-all_city_goods_id" data-rule="" min="0" data-source="service/goods/index" class="form-control selectpage" name="row[all_city_goods_id]" type="text" value="{$row.all_city_goods_id|htmlentities}" >
            </div>
        </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Spec_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        

             <div class="radio">
                    {foreach name="specTypeList" item="vo"}
                    <label for="row[spec_type]-{$key}"><input id="row[spec_type]-{$key}" name="row[spec_type]"  type="radio" v-model="spec_type" value="{$key}" {in name="key" value="spec_type"}checked{/in} /> {$vo}</label>
                    {/foreach}
                </div>
                
                <!--<div class="radio">-->
                <!--{foreach name="specTypeList" item="vo"}-->
                <!--<label for="row[spec_type]-{$key}"><input id="row[spec_type]-{$key}" name="row[spec_type]" v-model="spec_type" type="radio" value="{$key}" {in name="key" value="0"}checked{/in} /> {$vo}</label> -->
                <!--{/foreach}-->
                <!--</div>-->

        </div>
    </div>
        <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-price" data-rule="required" min="0" class="form-control" step="0.01" name="row[price]" type="number" value="{$row.price|htmlentities}">
        </div>
    </div>
        <div class="form-group" >
            <label class="control-label col-xs-12 col-sm-2">{:__('市场价')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-market_price" data-rule="required" min="0" class="form-control" step="0.01" name="row[market_price]" type="number" value="{$row.market_price|htmlentities}">
            </div>
        </div>
        <div class="form-group" >
            <label class="control-label col-xs-12 col-sm-2">{:__('夜间费用')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-night_money" data-rule="" min="0" class="form-control" name="row[night_money]" type="number" value="{$row.night_money|htmlentities}">
            </div>
        </div>
        <div class="form-group" v-if="spec_type == 0">
            <label class="control-label col-xs-12 col-sm-2">{:__('Cost_seconds')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-cost_seconds" data-rule="required" min="0" class="form-control" step="1" name="row[cost_seconds]" type="number" value="{$row.cost_seconds|htmlentities}" placeholder="请输入时长/次数">
            </div>
        </div>
        <div class="form-group"   v-if="spec_type == 1">
            <label class="control-label col-xs-12 col-sm-2"></label>
                <div class="col-xs-12 col-sm-8 ">
                    <input  class="hidden" :value="JSON.stringify(spu)" name="row[spu]"  type="text">
                    
                    <input  class="hidden" :value="JSON.stringify(sku)" name="row[sku]"  type="text">
                    <template v-for="(item, index) of spu">
                        <div class="spuLine">
                           <label class="control-label col-xs-12 col-sm-3">规格名称:</label>
                            <div class="spuBox col-xs-12 col-sm-5">
                                <input  data-rule="required" class="form-control" size="50" v-model="item.name"  type="text" placeholder="请输入规格名称">
                            </div>
    
                            <div class="actionBox">
                                <span class="del" v-if="spu.length > 1" @click="delSpuName(index)">删除</span>
<!--                                <span class="add"  @click="addSpuName">增加</span>-->
                            </div>
                        </div>
                        <div class="skuLine" v-for="(val,key) of item.info">
                            <label class="control-label col-xs-12 col-sm-3">{{key === 0 ? '规格值': ''}}</label>
                            <div class="spuBox col-xs-12 col-sm-5">
                                <input  data-rule="required" class="form-control" size="50" v-model="val.name"  type="text" placeholder="请输入规格值">
                            </div>
    
                            <div class="actionBox">
                                <span class="del" v-if="item.info.length > 1" @click="delSkuName(key,item.info)">删除</span>
                                <span class="add"  @click="addSkuName(item.info)">增加</span>
                            </div>
                        </div>
                    </template>
    
                    <div class="tableBox">
                        <table  class="table table-striped table-bordered table-hover table-nowrap">
                            <thead>
                                <tr>
                                    <th v-for="item of spu">{{item.name}}</th>
                                    <th >次数</th>
                                    <th >价格</th>
                                    <th >市场价</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item of sku">
                                    <td v-for="value of item.name.split(',')">{{value}}</th>
                                    <td >
                                        <input  data-rule="required" class="form-control"  size="50" v-model="item.cost_seconds" type="number" placeholder="请输入时长（分钟）">
                                    </td>
                                    <td >
                                        <input  data-rule="required" class="form-control"  size="50" v-model="item.price" type="number" placeholder="请输入价格">
                                    </td>
                                    <td >
                                        <input  data-rule="required" class="form-control"  size="50" v-model="item.market_price" type="number" placeholder="请输入价格">
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
    
                </div>
        </div> 
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Flow_path_images')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input id="c-flow_path_images" data-rule="required" class="form-control" size="50" name="row[flow_path_images]" type="text" value="{$row.flow_path_images|htmlentities}">-->
<!--                <div class="input-group-addon no-border no-padding">-->
<!--                    <span><button type="button" id="faupload-flow_path_images" class="btn btn-danger faupload" data-input-id="c-flow_path_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-flow_path_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>-->
<!--                    <span><button type="button" id="fachoose-flow_path_images" class="btn btn-primary fachoose" data-input-id="c-flow_path_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>-->
<!--                </div>-->
<!--                <span class="msg-box n-right" for="c-flow_path_images"></span>-->
<!--            </div>-->
<!--            <ul class="row list-inline faupload-preview" id="p-flow_path_images"></ul>-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Illustrate_images')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input id="c-illustrate_images" data-rule="required" class="form-control" size="50" name="row[illustrate_images]" type="text" value="{$row.illustrate_images|htmlentities}">-->
<!--                <div class="input-group-addon no-border no-padding">-->
<!--                    <span><button type="button" id="faupload-illustrate_images" class="btn btn-danger faupload" data-input-id="c-illustrate_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-illustrate_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>-->
<!--                    <span><button type="button" id="fachoose-illustrate_images" class="btn btn-primary fachoose" data-input-id="c-illustrate_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>-->
<!--                </div>-->
<!--                <span class="msg-box n-right" for="c-illustrate_images"></span>-->
<!--            </div>-->
<!--            <ul class="row list-inline faupload-preview" id="p-illustrate_images"></ul>-->
<!--        </div>-->
<!--    </div>-->
        <div class="form-group selected3">
            <label class="control-label col-xs-12 col-sm-2">{:__('项目详情')}:</label>
            <div class="col-xs-12 col-sm-8">
                <textarea id="c-content" data-rule="" class="form-control editor" rows="5" name="row[content]" cols="50">{$row.content}</textarea>
            </div>
        </div>
<!--        <div class="form-group">-->
<!--            <label class="control-label col-xs-12 col-sm-2">{:__('保险名称')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <input id="c-bx_name" data-rule="" min="" class="form-control" name="row[bx_name]" type="text" value="{$row.bx_name}">-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="form-group">-->
<!--            <label class="control-label col-xs-12 col-sm-2">{:__('保险描述')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <textarea id="c-bx_desc" data-rule="" rows="8" class="form-control" name="row[bx_desc]">{$row.bx_desc}</textarea>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="form-group selected3">-->
<!--            <label class="control-label col-xs-12 col-sm-2">{:__('保险详情')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <textarea id="c-bx_content" data-rule="" class="form-control editor" rows="5" name="row[bx_content]" cols="50">{$row.bx_content}</textarea>-->
<!--            </div>-->
<!--        </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Salenums')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-salenums" data-rule="required" min="0" class="form-control" name="row[salenums]" type="number" value="{$row.salenums|htmlentities}">
        </div>
    </div>
    <div class="form-group" v-show="status == 0">
        <label class="control-label col-xs-12 col-sm-2">{:__('Shop_state')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="shopStateList" item="vo"}
            <label for="row[shop_state]-{$key}"><input id="row[shop_state]-{$key}" name="row[shop_state]" type="radio" value="{$key}" {in name="key" value="$row.shop_state"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('养老院长分成比例')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-shequ_bili" data-rule="" class="form-control" name="row[shequ_bili]" type="number" value="{$row.shequ_bili}"  placeholder="请输入养老院长分成比例">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('城市运营商分成比例')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-quyu_bili" data-rule="" class="form-control" name="row[quyu_bili]" type="number" value="{$row.quyu_bili}"  placeholder="请输入城市运营商分成比例">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('服务者分成比例')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-service_bili" data-rule="" class="form-control" name="row[service_bili]" type="number" value="{$row.service_bili}"  placeholder="请输入服务者分成比例">
            </div>
        </div>
    <div class="form-group" v-show="status == 0">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="{$row.weigh|htmlentities}">
        </div>
    </div> 
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
