<?php
namespace app\api\controller\service;

use app\api\model\service\GoodsSku;
use app\common\controller\Api;
use think\Db;
use think\Loader;
use think\Exception;
/**
 * 首页接口
 */
class Goods extends Api
{
    protected $noNeedLogin = ['categoryGoods','getList','goodsInfo'];
    protected $noNeedRight = ['*'];




    /**
     * 申请调价
     * <AUTHOR>
     * @date 2024/9/12  下午4:
     * @notes
     */
    public function applyPrice()
    {
        if($this->auth->is_qydl != 1){
            $this->error('非城市运营商');
        }
        $goods_id = input('goods_id/d',0);
        $price = input('price/f');
        if(!$goods_id || !$price){
            $this->error('数据错误');
        }
        $sku_id = input('sku_id/d',0);
        $userArea = \app\common\model\UserArea::where(['user_id'=>$this->auth->id])->find();
        if(!$userArea)
        {
            $this->error('未绑定城市运营商');
        }
        $data = [
            'user_id'=>$this->auth->id,
            'goods_id'=>$goods_id,
            'sku_id'=>$sku_id,
            'user_area_id'=>$userArea['id'],
            'old_price'=>$sku_id==0 ? \app\api\model\service\Goods::where(['id'=>$goods_id])->value('price') : GoodsSku::where(['id'=>$sku_id])->value('price') ?? null,
            'price'=>$price,
            'city'=>$userArea['city'],
        ];
        $applyPrice = \app\common\model\UserAreaPrice::create($data);
        if($applyPrice){
            $this->success('申请调价成功',$applyPrice);
        }else{
            $this->error('申请调价失败');
        }
    }

    /**
     * 获取申请调价记录
     * <AUTHOR>
     * @date 2024/9/12  下午5:04
     * @notes
     */
    public function getApplyPriceLog()
    {
        $list = \app\common\model\UserAreaPrice::where(['user_id'=>$this->auth->id])->order('createtime desc')->paginate(10)->each(function ($item){
            $item->goods_name = \app\api\model\service\Goods::getName($item['goods_id']);
            $item->sku_name = $item['sku_id']?\app\api\model\service\GoodsSku::where(['id'=>$item['sku_id']])->value('name'):'';

            $item->createtime = date('Y-m-d H:i:s',$item['createtime']);
            return $item;
        });
        $this->success('信息返回成功',$list);
    }


    /**
     * 搜索查询项目
     * @return void
     */
    public function getList()
    {
        $uid = $this->auth->isLogin()?$this->auth->id:'';
        $get = $this->request->param();
        $get['limit'] = 10;
        $get['page'] = input('page/d',1);
        // $get['city'] = urldecode($this->request->header('city'));
        if(array_key_exists('name',$get) && $uid)
        {
            \app\api\model\service\SearchLog::create(['user_id'=>$uid,'name'=>$get['name']]);
        }
        $get['list']=1;
        $list = \app\api\model\service\Goods::searchGoods($get);
        if($list)
        {
            foreach ($list as &$value)
            {
                $value['shopname'] = $value['shop_id']?\app\api\model\service\Shop::getName($value['shop_id']):'';
                $value['goods_tip'] = '';
            }
        }
        $this->success('信息返回成功',$list);
    }


    /**
     * 获取附加服务
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2024/8/22  下午4:42
     * @notes
     */
    public function goodsAddList()
    {
        $id = input('id/d','');
        $type = input('type/d',0);
        $where = [];
        $list = \app\api\model\service\GoodsAdd::where(['goods_id'=>$id,'state'=>1,'type'=>$type])->select();
        if($list)
        {
            foreach ($list as &$value)
            {
                if($value['hcb']){
                    $value['hcb'] = json_decode($value['hcb'],true);
                }
            }
        }
        $this->success('信息返回成功',$list);
    }


    /**
     * 首页分类项目列表
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function categoryGoods()
    {
        $city = input('city','');
        $categoryList = model('app\api\model\service\Category')
            ->where(['status'=>'normal','pid'=>0,'is_show'=>1])
            ->field('id,name')
            ->order('weigh desc')
            ->select();
        if($categoryList)
        {
            foreach ($categoryList as &$value)
            {
                $value['goodsList'] = \app\api\model\service\Goods::searchGoods(['category_id'=>$value['id'],'page'=>1,'limit'=>4,'city'=>$city]);
            }
        }
        $this->success('信息返回成功',$categoryList);
    }

    /**
     * 用户端项目详情
     * @return void
     */
    public function goodsInfo()
    {
        $uid = $this->auth->isLogin()?$this->auth->id:'';
        $id = input('id/d','');
        $uid = $this->auth->isLogin()?$this->auth->id:'';
        $info = \app\api\model\service\Goods::getInfo($id,$uid);
        !$info && $this->error('项目信息异常');
        $info['isPlus'] = $uid?\app\api\model\service\UserInfo::where('user_id',$uid)->value('is_plus'):0;
        $this->success('信息返回成功',$info);
    }


    /**
     * 服务人员更新服务项目
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function skillCategoryGoods()
    {
        $uid = $this->auth->id;
        $shop_id = \app\api\model\service\Skill::where(['user_id'=>$uid])->value('shop_id');
        $categoryList = \app\api\model\service\Category::where(['status'=>'normal','pid'=>0])->field('id,name')->select();
        $where['shop_id'] = $shop_id?['in',[0,$shop_id]]:0;
        $where['state'] = 1;
        foreach ($categoryList as &$value)
        {
            $where['category_id'] = $value['id'];
            $value['goodsList'] = \app\api\model\service\Goods::where($where)->field('id,name,image')->select();
        }
        $this->success('信息返回成功',$categoryList);
    }


    /**
     * 店铺添加项目
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function shopAddGoods()
    {
        $uid = $this->auth->id;
        $shop = \app\api\model\service\Shop::where(['user_id'=>$uid])->field('id,to_shop,province,city,district')->find();
        $post = input('post.','','trim,strip_tags');
        $post['shop_id'] = $shop['id'];
        $post['shop_user_id'] = $uid;
        $post['type'] = 1;
        $post['province'] = $shop['province'];
        $post['city'] = $shop['city'];
        $post['district'] = $shop['district'];
        (strstr($post['to_shop'],'shop') && $shop['to_shop'] == 0) && $this->error('未开通上门服务权限');
        $validate = Loader::validate('service.Goods');
        if(!$validate->scene('add')->check($post)){
            $this->error($validate->getError());
        }
        Db::startTrans();
        try{
            \app\api\model\service\Goods::addGoods($post);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('项目添加失败',$e->getMessage());
        }
        $this->success('项目已添加,等待管理员审核');
    }


    /**
     * 商户获得添加项目信息
     * @return void
     */
    public function shopGoodsInfo()
    {
        $id = input('id/d','');
        $goods = \app\api\model\service\Goods::getShopGoods($id);
        !$goods['id'] && $this->error('商品信息异常');
        $this->success('信息返回成功',$goods);
    }

    /**
     * 商户修改项目
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function shopEditGoods()
    {
        $uid = $this->auth->id;
        $id = input('id/d','');
        $shop = \app\api\model\service\Shop::where(['user_id'=>$uid])->field('id,to_shop')->find();
        $goodsId = \app\api\model\service\Goods::where(['id'=>$id,'shop_user_id'=>$uid])->value('id');
        !$goodsId && $this->error('商品信息异常');
        $post = input('post.','','trim,strip_tags');
        (strstr($post['to_shop'],'shop') && $shop['to_shop'] == 0) && $this->error('未开通上门服务权限');
        $validate = Loader::validate('service.Goods');
        if(!$validate->scene('edit')->check($post)){
            $this->error($validate->getError());
        }
        Db::startTrans();
        try{
            \app\api\model\service\Goods::editGoods($post);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('项目编辑失败',$e->getMessage());
        }
        $this->success('项目已编辑,等待管理员审核');
    }

    /**
     * 商户下架项目
     * @return void
     */
    public function downGoods()
    {
        $uid = $this->auth->id;
        $id = input('id/d','');
        $goodsId = \app\api\model\service\Goods::where(['id'=>$id,'shop_user_id'=>$uid,'status'=>'normal'])->value('id');
        !$goodsId && $this->error('当前商品无法下架');
        \app\api\model\service\Goods::downGoods($id);
        $this->success('商品已下架');
    }

    /**
     * 商户上架项目
     * @return void
     *
     */
    public function upGoods()
    {
        $uid = $this->auth->id;
        $id = input('id/d','');
        $goodsId = \app\api\model\service\Goods::where(['id'=>$id,'shop_user_id'=>$uid,'shop_state'=>1,'status'=>'hidden'])->value('id');
        !$goodsId && $this->error('当前商品无法上架');
        \app\api\model\service\Goods::upGoods($id);
        $this->success('商品已上架');
    }

    public function getUserGoods()
    {
        $uid = $this->auth->id;
        $type = input('type/d','');
        $userInfo = \app\api\model\service\UserInfo::where(['user_id'=>$uid])->field('id,user_id,is_skill,is_shop')->find();
        (($type == 0 && $userInfo['is_skill'] != 1) || ($type == 1 && $userInfo['is_shop'] != 1)) && $this->error('当前身份不符,无法查看');
        $goodsIds = $type == 0?\app\api\model\service\Skill::where(['user_id'=>$uid])->value('goods_ids'):\app\api\model\service\Shop::where(['user_id'=>$uid])->value('goods_ids');
        $goodsList = $type == 0?\app\api\model\service\Goods::getSkillGoods($goodsIds):\app\api\model\service\Goods::getShopPlatformGoods($goodsIds,$uid);

        $this->success('信息返回成功',$goodsList);
    }

    public function getApplyGoods()
    {
        $uid = $this->auth->id;
        $type = input('type/d','');
        $city = input('city','','trim,strip_tags');
        $category_id = input('category_id/d');
        $page = input('page/d','');
        $userInfo = \app\api\model\service\UserInfo::where(['user_id'=>$uid])->field('id,user_id,is_skill,is_shop')->find();
        $page = empty($page)?0:($page-1)*10;
        $limit = $page.',10';
        $config = config('database');
        $db = $config['prefix'].'service_goods';
        if(($type == 0 && $userInfo['is_skill'] == 0) || $type == 1)
        {
            $list = Db::query("SELECT id,name,shop_id,image,price,tag_name,response_hour,salenums,type,skill_cate_ids,category_id,city FROM {$db} WHERE (category_id={$category_id} AND status='normal' AND shop_id=0 AND shop_state=1) AND (city='{$city}' OR city IS NULL) AND (deletetime IS NULL) ORDER BY id DESC LIMIT {$limit}");
        }elseif($type == 0 && $userInfo['is_skill'] == 1)
        {
            $shop_id = \app\api\model\service\Skill::where(['user_id'=>$uid])->value('shop_id');
            if($shop_id){

                $list = Db::query("SELECT id,name,shop_id,image,price,tag_name,response_hour,salenums,type,skill_cate_ids,category_id,city FROM {$db} WHERE (category_id={$category_id} AND shop_state=1 AND status='normal' AND shop_id IN(0,'{$shop_id}')) AND (city='{$city}' OR city IS NULL) AND (deletetime IS NULL) ORDER BY id DESC LIMIT {$limit}");

            }else{

                $list = Db::query("SELECT id,name,shop_id,image,price,tag_name,response_hour,salenums,type,skill_cate_ids,category_id,city FROM {$db} WHERE (category_id={$category_id} AND shop_state=1 AND status='normal' AND shop_id=0) AND (city='{$city}' OR city IS NULL) AND (deletetime IS NULL) ORDER BY id DESC LIMIT {$limit}");
            }
        }
        $this->success('信息返回成功',$list);
    }



}