<?php

return [
    'Id'             => '主键ID',
    'Orderid'        => '订单编号',
    'Trade_no'       => '交易单号',
    'Area_user_id'   => '城市运营商会员ID',
    'Kt_mobile'      => '开通手机号',
    'Price'          => '订单金额',
    'Paytype'        => '支付类型',
    'Paytype 0'      => '微信小程序',
    'Paytype 1'      => '微信APP',
    'Paytype 2'      => '公众号支付',
    'Paytype 3'      => '支付宝支付',
    'Paytype 4'      => '余额',
    'Paytime'        => '支付时间',
    'Memo'           => '备注',
    'Createtime'     => '添加时间',
    'Updatetime'     => '更新时间',
    'Status'         => '状态',
    'Status created' => '未支付',
    'Set status to created'=> '设为未支付',
    'Status paid'    => '已支付',
    'Set status to paid'=> '设为已支付',
    'User.nickname'  => '昵称'
];
