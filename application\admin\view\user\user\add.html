<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('所属上级')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-parent_id" data-source="user/user/index?no_service=1"  data-format-item="<img class='img-sm img-center img-circle' src='{avatar}'> {nickname} - {mobile}" data-field="mobile" class="form-control selectpage" name="row[parent_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-password" data-rule="required|password" class="form-control" name="row[password]" type="password" value="" autocomplete="new-password" />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('养老院长')}:</label>
        <div class="col-xs-12 col-sm-4">
            <select  id="c-is_sqdl" data-rule="required" class="form-control selectpicker" name="row[is_sqdl]">
                {foreach name="sqdlList" item="vo"}
                <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('城市运营商')}:</label>
        <div class="col-xs-12 col-sm-4">
            <select  id="c-is_qydl" data-rule="required" class="form-control selectpicker" name="row[is_qydl]">
                {foreach name="qydlList" item="vo"}
                <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('养老顾问')}:</label>
        <div class="col-xs-12 col-sm-4">
            <select  id="c-is_ylgw" data-rule="required" class="form-control selectpicker" name="row[is_ylgw]">
                {foreach name="ylgwList" item="vo"}
                <option value="{$key}" {in name="key" value="0"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    {if condition="$canManageQuota"}
    <div class="form-group" id="ylgw-quota-group" style="display: none;">
        <label class="control-label col-xs-12 col-sm-2">{:__('养老顾问开通额度')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-ylgw_quota" data-rule="integer" class="form-control" name="row[ylgw_quota]" type="number" min="0" value="0" placeholder="请输入开通额度">
            <span class="help-block">只有城市运营商和养老院长才能拥有此额度</span>
        </div>
    </div>
    {/if}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('区域')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-addr" data-rule="" class="form-control" data-toggle="city-picker" name="row[addr]" type="text" value=""></div>
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-mobile" data-rule="mobile" class="form-control" name="row[mobile]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label for="c-avatar" class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="" class="form-control" size="50" name="row[avatar]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[gender]', ['1'=>__('Male'), '0'=>__('Female')], '1')}
        </div>
    </div>
    <div class="form-group">
        <label for="c-birthday" class="control-label col-xs-12 col-sm-2">{:__('Birthday')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-birthday" data-rule="" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[birthday]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label for="c-money" class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-money" data-rule="required" class="form-control" name="row[money]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label for="c-card_no" class="control-label col-xs-12 col-sm-2">{:__('身份证号')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-card_no" data-rule="" class="form-control" name="row[card_no]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label for="c-realname" class="control-label col-xs-12 col-sm-2">{:__('真实姓名')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-realname" data-rule="" class="form-control" name="row[realname]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label for="c-ylgw_number_no" class="control-label col-xs-12 col-sm-2">{:__('编号')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-ylgw_number_no" data-rule="" class="form-control" name="row[ylgw_number_no]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], 'normal')}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    // 监听身份选择变化，控制养老顾问开通额度字段的显示
    function toggleQuotaField() {
        var isSqdl = $('#c-is_sqdl').val(); // 养老院长
        var isQydl = $('#c-is_qydl').val(); // 城市运营商
        var quotaGroup = $('#ylgw-quota-group');

        if (quotaGroup.length > 0) {
            // 只有当用户是城市运营商(1)或养老院长(1)时才显示额度字段
            if (isSqdl == '1' || isQydl == '1') {
                quotaGroup.show();
            } else {
                quotaGroup.hide();
                // 隐藏时清空值
                $('#c-ylgw_quota').val('0');
            }
        }
    }

    // 页面加载时检查一次
    toggleQuotaField();

    // 监听身份选择变化
    $('#c-is_sqdl, #c-is_qydl').on('change', function() {
        toggleQuotaField();
    });
});
</script>
