{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "养老顾问API", "description": "养老顾问管理系统API接口文档", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "养老顾问管理", "id": 58468296, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "养老顾问相关的所有API接口", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 5575437, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "用户管理", "id": 58468342, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "用户绑定、解绑等管理功能", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 5575437, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "绑定下级用户", "api": {"id": "305602323", "method": "post", "path": "/api/xiluedu.ylgw/bind_user", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266189", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，1表示成功"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "null", "description": "返回数据"}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "绑定成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [{"id": "example1", "name": "绑定成功示例", "data": {"code": 1, "msg": "绑定成功", "time": "1640995200", "data": null}}], "requestBody": {"type": "application/json", "parameters": [{"id": "jtmN46JLWL", "name": "mobile", "required": true, "description": "用户手机号", "example": "13800138000", "type": "string", "schema": {"description": "用户手机号", "type": "string", "pattern": "^1\\d{10}$"}}, {"id": "jtmN46JLWM", "name": "name", "required": true, "description": "用户姓名", "example": "张三", "type": "string", "schema": {"description": "用户姓名", "type": "string"}}], "oasExtensions": ""}, "description": "养老顾问绑定下级用户，目标用户必须没有上级且不是养老顾问或更高级别", "tags": ["用户管理"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 1, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取下级用户列表", "api": {"id": "305602324", "method": "get", "path": "/api/xiluedu.ylgw/sub_users", "parameters": {"path": [], "query": [{"id": "9yp8TiSEHT", "name": "page", "required": false, "description": "页码", "example": "1", "type": "integer", "schema": {"type": "integer", "default": 1}}, {"id": "9yp8TiSEHU", "name": "limit", "required": false, "description": "每页数量", "example": "10", "type": "integer", "schema": {"type": "integer", "default": 10}}], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266190", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总数量"}, "per_page": {"type": "integer", "description": "每页数量"}, "current_page": {"type": "integer", "description": "当前页码"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "nickname": {"type": "string", "description": "用户昵称"}, "mobile": {"type": "string", "description": "手机号（脱敏）"}, "avatar": {"type": "string", "description": "头像URL"}, "money": {"type": "string", "description": "余额"}, "createtime_text": {"type": "string", "description": "注册时间"}}}}}}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "获取成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取养老顾问的下级用户列表（不包括养老顾问、养老院长、城市运营商）", "tags": ["用户管理"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 2, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "搜索用户", "api": {"id": "305602325", "method": "get", "path": "/api/xiluedu.ylgw/search_user", "parameters": {"path": [], "query": [{"id": "9yp8TiSEHV", "name": "mobile", "required": true, "description": "手机号", "example": "13800138000", "type": "string", "schema": {"type": "string", "pattern": "^1\\d{10}$"}}], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266191", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "nickname": {"type": "string", "description": "用户昵称"}, "mobile": {"type": "string", "description": "手机号"}, "avatar": {"type": "string", "description": "头像URL"}, "status": {"type": "string", "description": "用户状态：可绑定/已有上级/城市运营商/养老院长/养老顾问"}, "can_bind": {"type": "boolean", "description": "是否可以绑定"}, "createtime_text": {"type": "string", "description": "注册时间"}}}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "搜索成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "根据手机号搜索用户，用于绑定前的用户查找和状态检查", "tags": ["用户管理"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 3, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "解绑下级用户", "api": {"id": "305602326", "method": "post", "path": "/api/xiluedu.ylgw/unbind_user", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266192", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，1表示成功"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "null", "description": "返回数据"}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "解绑成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [{"id": "jtmN46JLWN", "name": "user_id", "required": true, "description": "用户ID", "example": "123", "type": "integer", "schema": {"description": "用户ID", "type": "integer"}}], "oasExtensions": ""}, "description": "解绑下级用户，只能解绑普通用户，不能解绑已升级为代理的用户", "tags": ["用户管理"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 4, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "开通下级用户为养老顾问", "api": {"id": "305602327", "method": "post", "path": "/api/xiluedu.ylgw/upgrade_user", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266193", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，1表示成功"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "null", "description": "返回数据"}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "开通成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [{"id": "jtmN46JLWO", "name": "user_id", "required": true, "description": "用户ID", "example": "123", "type": "integer", "schema": {"description": "用户ID", "type": "integer"}}], "oasExtensions": ""}, "description": "为下级用户开通养老顾问身份，需要支付课程16的费用", "tags": ["用户管理"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 5, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}, {"name": "统计分析", "id": 58468343, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "收益统计、团队统计等分析功能", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 5575437, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "获取收益统计", "api": {"id": "305602328", "method": "get", "path": "/api/xiluedu.ylgw/income_stats", "parameters": {"path": [], "query": [{"id": "9yp8TiSEHW", "name": "date", "required": false, "description": "统计类型：today/week/month", "example": "today", "type": "string", "schema": {"type": "string", "enum": ["today", "week", "month"], "default": "today"}}], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266194", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "object", "properties": {"period_income": {"type": "string", "description": "指定时间段收益"}, "total_income": {"type": "string", "description": "总收益"}, "sub_user_count": {"type": "integer", "description": "下级用户数量"}, "current_balance": {"type": "string", "description": "当前余额"}, "date_type": {"type": "string", "description": "统计类型"}}}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "获取成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取养老顾问的收益统计数据", "tags": ["统计分析"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 6, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取收益明细", "api": {"id": "305602329", "method": "get", "path": "/api/xiluedu.ylgw/income_detail", "parameters": {"path": [], "query": [{"id": "9yp8TiSEHX", "name": "page", "required": false, "description": "页码", "example": "1", "type": "integer", "schema": {"type": "integer", "default": 1}}, {"id": "9yp8TiSEHY", "name": "limit", "required": false, "description": "每页数量", "example": "10", "type": "integer", "schema": {"type": "integer", "default": 10}}], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266195", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总数量"}, "per_page": {"type": "integer", "description": "每页数量"}, "current_page": {"type": "integer", "description": "当前页码"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "记录ID"}, "money": {"type": "string", "description": "收益金额"}, "memo": {"type": "string", "description": "收益说明"}, "nickname": {"type": "string", "description": "购买用户昵称"}, "avatar": {"type": "string", "description": "购买用户头像"}, "createtime_text": {"type": "string", "description": "收益时间"}}}}}}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "获取成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取养老顾问的收益明细列表", "tags": ["统计分析"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 7, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取团队统计", "api": {"id": "305602330", "method": "get", "path": "/api/xiluedu.ylgw/team_stats", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266196", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "object", "properties": {"team_count": {"type": "integer", "description": "团队总人数（递归统计）"}, "direct_count": {"type": "integer", "description": "直接下级人数"}, "today_new": {"type": "integer", "description": "今日新增人数"}, "month_new": {"type": "integer", "description": "本月新增人数"}}}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "获取成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取养老顾问的团队统计数据", "tags": ["统计分析"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 8, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取收益排行榜", "api": {"id": "305602331", "method": "get", "path": "/api/xiluedu.ylgw/income_rank", "parameters": {"path": [], "query": [{"id": "9yp8TiSEHZ", "name": "type", "required": false, "description": "排行类型：today/month/total", "example": "total", "type": "string", "schema": {"type": "string", "enum": ["today", "month", "total"], "default": "total"}}, {"id": "9yp8TiSEI0", "name": "limit", "required": false, "description": "排行数量", "example": "10", "type": "integer", "schema": {"type": "integer", "default": 10}}], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266197", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "array", "items": {"type": "object", "properties": {"rank": {"type": "integer", "description": "排名"}, "user_id": {"type": "integer", "description": "用户ID"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatar": {"type": "string", "description": "用户头像"}, "income": {"type": "string", "description": "收益金额"}}}}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "获取成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取下级用户的收益排行榜", "tags": ["统计分析"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 9, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}, {"name": "信息查询", "id": 58468344, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "养老顾问信息查询相关接口", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 5575437, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "获取养老顾问信息", "api": {"id": "305602332", "method": "get", "path": "/api/xiluedu.ylgw/info", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266198", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "object", "properties": {"user_info": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "nickname": {"type": "string", "description": "用户昵称"}, "mobile": {"type": "string", "description": "手机号"}, "avatar": {"type": "string", "description": "头像URL"}, "money": {"type": "string", "description": "余额"}, "is_ylgw": {"type": "integer", "description": "是否为养老顾问"}}}, "stats": {"type": "object", "properties": {"sub_user_count": {"type": "integer", "description": "下级用户数量"}, "total_income": {"type": "string", "description": "总收益"}, "today_income": {"type": "string", "description": "今日收益"}}}}}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "获取成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取当前养老顾问的基本信息和统计数据", "tags": ["信息查询"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 10, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取下级养老顾问列表", "api": {"id": "305602333", "method": "get", "path": "/api/xiluedu.ylgw/sub_ylgw_list", "parameters": {"path": [], "query": [{"id": "9yp8TiSEI1", "name": "page", "required": false, "description": "页码", "example": "1", "type": "integer", "schema": {"type": "integer", "default": 1}}, {"id": "9yp8TiSEI2", "name": "limit", "required": false, "description": "每页数量", "example": "10", "type": "integer", "schema": {"type": "integer", "default": 10}}], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": true, "description": "用户认证Token", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266199", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "msg": {"type": "string", "description": "返回消息"}, "time": {"type": "string", "description": "时间戳"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总数量"}, "per_page": {"type": "integer", "description": "每页数量"}, "current_page": {"type": "integer", "description": "当前页码"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "nickname": {"type": "string", "description": "用户昵称"}, "mobile": {"type": "string", "description": "手机号（脱敏）"}, "avatar": {"type": "string", "description": "头像URL"}, "money": {"type": "string", "description": "余额"}, "sub_count": {"type": "integer", "description": "下级用户数量"}, "total_income": {"type": "string", "description": "总收益"}, "createtime_text": {"type": "string", "description": "注册时间"}}}}}}}, "x-apifox-orders": ["code", "msg", "time", "data"]}, "description": "获取成功", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取下级养老顾问列表", "tags": ["信息查询"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 11, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [], "schemaCollection": [], "securitySchemeCollection": [], "requestCollection": [], "environments": [{"id": "env1", "name": "开发环境", "baseUrl": "https://service.jiaqingfu.com.cn", "variables": []}, {"id": "env2", "name": "生产环境", "baseUrl": "https://service.jiaqingfu.com.cn", "variables": []}], "commonScripts": [], "globalVariables": [], "commonParameters": null, "projectSetting": {"id": "6561092", "auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务"}], "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "isDefaultUrlEncoding": 2, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "NEW"}}, "initialDisabledMockIds": [], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "customFunctions": [], "projectAssociations": []}