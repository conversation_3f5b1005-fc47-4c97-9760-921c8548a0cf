<?php

namespace app\api\controller\xiluedu;

use app\common\controller\Api;
use app\common\model\User;
use app\common\model\xiluedu\CourseOrder;
use app\common\model\xiluedu\CoursePackage;
use app\common\model\xiluedu\UserCourse;
use think\Db;
use think\Exception;

/**
 * 课程套餐功能控制器
 */
class Package extends Api
{
    protected $noNeedLogin = ['config'];
    protected $noNeedRight = ['*'];

    /**
     * 获取课程16套餐配置和价格
     */
    public function course16()
    {
        // 从数据库获取课程16的套餐配置
        $packages = CoursePackage::getCoursePackages(16);

        if (!$packages) {
            $this->error('暂无可用套餐');
        }

        // 基础配置
        $config = [
            'course_id' => 16,
            'platform' => 'wxmin',
            'total_packages' => count($packages)
        ];

        // 处理套餐数据
        $packageList = [];
        foreach ($packages as $package) {
            $packageData = [
                'id' => $package->id,
                'name' => $package->name,
                'description' => $package->description,
                'quantity' => $package->quantity,
                'original_price' => floatval($package->original_price),
                'qydl_price' => floatval($package->qydl_price),
                'sqdl_price' => floatval($package->sqdl_price),
                'ylgw_price' => floatval($package->ylgw_price),
                'user_types' => explode(',', $package->user_types),
                'sort' => $package->sort,
                'status' => $package->status
            ];

            $packageList[] = $packageData;
        }

        // 如果用户已登录，返回用户对应的价格和可购买套餐
        $user_info = null;
        $available_packages = [];

        if ($this->auth->isLogin()) {
            $user = $this->auth->getUser();
            $user_info = [
                'is_qydl' => $user->is_qydl,
                'is_sqdl' => $user->is_sqdl,
                'is_ylgw' => $user->is_ylgw,
            ];

            // 确定用户类型
            $userType = 'user';
            if ($user->is_qydl == 1) {
                $userType = 'qydl';
            } elseif ($user->is_sqdl == 1) {
                $userType = 'sqdl';
            } elseif ($user->is_ylgw == 1) {
                $userType = 'ylgw';
            }

            // 筛选用户可购买的套餐并添加用户价格
            foreach ($packageList as $package) {
                if (in_array($userType, $package['user_types'])) {
                    $package['user_price'] = $package[$userType . '_price'];
                    $package['user_type'] = $this->getUserTypeName($userType);
                    $package['discount'] = $package['original_price'] - $package['user_price'];
                    $package['can_buy'] = true;
                    $available_packages[] = $package;
                }
            }
        } else {
            // 未登录用户只能看到普通用户可购买的套餐
            foreach ($packageList as $package) {
                if (in_array('user', $package['user_types'])) {
                    $package['user_price'] = $package['original_price'];
                    $package['user_type'] = '普通用户';
                    $package['discount'] = 0;
                    $package['can_buy'] = true;
                    $available_packages[] = $package;
                }
            }
        }

        $result = [
            'config' => $config,
            'packages' => $available_packages,
            'all_packages' => $packageList, // 所有套餐信息（用于管理端）
            'user_info' => $user_info,
        ];

        $this->success('获取配置成功', $result);
    }

    /**
     * 获取用户类型名称
     */
    private function getUserTypeName($userType)
    {
        $typeNames = [
            'qydl' => '城市运营商',
            'sqdl' => '养老院长',
            'ylgw' => '养老顾问',
            'user' => '普通用户'
        ];

        return isset($typeNames[$userType]) ? $typeNames[$userType] : '普通用户';
    }

    /**
     * 获取套餐详情
     */
    public function packageDetail()
    {
        $package_id = $this->request->param('package_id');

        if (!$package_id) {
            $this->error('套餐ID不能为空');
        }

        $package = CoursePackage::where('id', $package_id)
            ->where('course_id', 16)
            ->where('status', 1)
            ->find();

        if (!$package) {
            $this->error('套餐不存在或已下架');
        }

        $packageData = [
            'id' => $package->id,
            'name' => $package->name,
            'description' => $package->description,
            'quantity' => $package->quantity,
            'original_price' => floatval($package->original_price),
            'qydl_price' => floatval($package->qydl_price),
            'sqdl_price' => floatval($package->sqdl_price),
            'ylgw_price' => floatval($package->ylgw_price),
            'user_types' => explode(',', $package->user_types),
            'sort' => $package->sort,
            'status' => $package->status
        ];

        // 如果用户已登录，添加用户相关信息
        if ($this->auth->isLogin()) {
            $user = $this->auth->getUser();

            // 确定用户类型
            $userType = 'user';
            if ($user->is_qydl == 1) {
                $userType = 'qydl';
            } elseif ($user->is_sqdl == 1) {
                $userType = 'sqdl';
            } elseif ($user->is_ylgw == 1) {
                $userType = 'ylgw';
            }

            $packageData['can_buy'] = $package->canUserBuy($userType);
            $packageData['user_price'] = $package->getPriceByUserType($userType);
            $packageData['user_type'] = $this->getUserTypeName($userType);
            $packageData['discount'] = $packageData['original_price'] - $packageData['user_price'];
            $packageData['total_price'] = $packageData['user_price'] * $packageData['quantity'];
        } else {
            $packageData['can_buy'] = in_array('user', $packageData['user_types']);
            $packageData['user_price'] = $packageData['original_price'];
            $packageData['user_type'] = '普通用户';
            $packageData['discount'] = 0;
            $packageData['total_price'] = $packageData['original_price'] * $packageData['quantity'];
        }

        $this->success('获取套餐详情成功', $packageData);
    }

    /**
     * 创建课程16套餐订单
     */
    public function createOrder()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $package_id = $this->request->param('package_id');
        $platform = $this->request->param('platform', 'wxmin');

        if (!$package_id) {
            $this->error('请选择套餐');
        }

        // 获取套餐信息
        $package = CoursePackage::where('id', $package_id)
            ->where('course_id', 16)
            ->where('status', 1)
            ->find();

        if (!$package) {
            $this->error('套餐不存在或已下架');
        }

        $user = $this->auth->getUser();

        // 确定用户类型
        $userType = 'user';
        if ($user->is_qydl == 1) {
            $userType = 'qydl';
        } elseif ($user->is_sqdl == 1) {
            $userType = 'sqdl';
        } elseif ($user->is_ylgw == 1) {
            $userType = 'ylgw';
        }

        // 检查用户是否可以购买此套餐
        if (!$package->canUserBuy($userType)) {
            $this->error('您无权购买此套餐');
        }

        // 获取用户对应的价格
        $unit_price = $package->getPriceByUserType($userType);
        $quantity = $package->quantity;
        $total_price = $unit_price ;

        Db::startTrans();
        try {
            // 创建订单
            $order_data = [
                'platform' => $platform,
                'user_id' => $user->id,
                'order_no' => "C16" . date("YmdHis") . mt_rand(10, 9999),
                'course_id' => 16,
                'package_id' => $package_id,
                'package_type' => $quantity > 1 ? 1 : 0, // 兼容旧字段
                'package_quantity' => $quantity,
                'total_price' => $total_price,
                'pay_price' => $total_price,
                'favourable_price' => 0,
                'ip' => request()->ip(),
                'pay_status' => 1, // 待支付
                'is_service' => 0,
            ];

            $order = CourseOrder::create($order_data);

            Db::commit();
            $this->success('订单创建成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'package_id' => $package_id,
                'package_name' => $package->name,
                'total_price' => $total_price,
                'quantity' => $quantity,
                'unit_price' => $unit_price,
                'user_type' => $this->getUserTypeName($userType),
            ]);

        } catch (Exception $e) {
            Db::rollback();
            $this->error('订单创建失败：' . $e->getMessage());
        }
    }

    /**
     * 余额支付订单
     */
    public function payWithBalance()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $order_id = $this->request->param('order_id');

        if (!$order_id) {
            $this->error('订单ID不能为空');
        }

        $order = CourseOrder::where('id', $order_id)
            ->where('user_id', $this->auth->id)
            ->where('pay_status', 1)
            ->where('course_id', 16)
            ->find();

        if (!$order) {
            $this->error('订单不存在或已支付');
        }

        if ($this->auth->money < $order->pay_price) {
            $this->error('余额不足，当前余额：' . $this->auth->money . '元，需要：' . $order->pay_price . '元');
        }

        // 获取套餐信息
        $package = null;
        if (isset($order->package_id) && $order->package_id > 0) {
            $package = CoursePackage::where('id', $order->package_id)->find();
        }

        Db::startTrans();
        try {
            // 扣除余额
            $memo = $package ? '购买养老顾问套餐-' . $package->name : '购买养老顾问身份套餐';
            User::money(-$order->pay_price, $this->auth->id, $memo, 1, $order->order_no, 'pay');

            // 更新订单状态
            $order->pay_status = 2;
            $order->pay_type = 2;
            $order->paytime = time();
            $order->order_trade_no = 'C16' . date('YmdHis') . mt_rand(10, 9999);
            $order->save();

            // 创建用户课程关联（根据套餐数量）
            for ($i = 0; $i < $order->package_quantity; $i++) {
                UserCourse::create([
                    'user_id' => $order->user_id,
                    'course_id' => 16,
                    'from_type' => 1, // 购买获得
                ]);
            }

            // 获取用户信息
            $user = User::where('id', $order->user_id)->find();

            // 只有普通用户购买课程16才开通养老顾问身份
            // 城市运营商和养老院长购买套餐是为了获得开通额度，不改变自己的身份
            if ($user && $user->is_ylgw == 0 && $user->is_qydl == 0 && $user->is_sqdl == 0) {
                $user->is_ylgw = 1;
                $user->save();
            }

            // 如果购买的是套餐（数量大于1），增加开通额度
            // 所有用户购买套餐都会获得开通额度
            if ($order->package_quantity >= 1) {
                $user->ylgw_quota = $user->ylgw_quota + $order->package_quantity;
                $user->save();
            }

            // 执行新的分佣逻辑
            // $this->executeNewCommission($order, $package);
            // 使用统一的分佣逻辑
            $this->processCourse16Commission($user,'养老顾问身份套餐', $order);
            Db::commit();
            $this->success('支付成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'package_name' => $package ? $package->name : '养老顾问身份套餐',
                'quantity' => $order->package_quantity,
                'total_price' => $order->pay_price,
            ]);

        } catch (Exception $e) {
            Db::rollback();
            $this->error('支付失败：' . $e->getMessage());
        }
    }

     /**
     * 统一处理课程16分佣逻辑（供控制器和钩子共同使用）
     */
    public static function processCourse16Commission($user, $package_name, $order)
    {
        // 获取订单金额
        $order_amount = isset($order->total_price) ? $order->total_price : $order->pay_price;
        \think\Log::info("课程16分佣-订单金额: {$order_amount}");

        // 获取购买者的直接上级
        $buyer_direct_parent_id = $user['parent_id'];
        $buyer_direct_parent = null;
        if ($buyer_direct_parent_id && $buyer_direct_parent_id > 0) {
            $buyer_direct_parent = \app\common\model\User::where('id', $buyer_direct_parent_id)->field('id,is_ylgw,is_sqdl,is_qydl,parent_id')->find();
        }
        \think\Log::info("课程16分佣-购买者ID: {$user['id']}, 直接上级ID: {$buyer_direct_parent_id}");


        // 获取各级上级代理
        $nearest_ylgw = \app\common\model\User::getOneYlgw($user['id']);
        $nearest_sqdl = \app\common\model\User::getOneSq($user['id']);
        $nearest_qydl = \app\common\model\User::getOneQy($user['id']);

        \think\Log::info("课程16分佣-最近养老顾问: " . ($nearest_ylgw ? $nearest_ylgw['id'] : '无') . ", 最近养老院长: " . ($nearest_sqdl ? $nearest_sqdl['id'] : '无') . ", 最近城市运营商: " . ($nearest_qydl ? $nearest_qydl['id'] : '无'));

        // 定义佣金比例和金额 (基于用户提供的规则)
        $ylgw_rate = 25; // 养老顾问基础分佣比例
        $sqdl_rate = 50; // 养老院长基础分佣比例
        $qydl_indirect_rate = 10; // 城市运营商间接推荐分佣比例
        $qydl_direct_rate = 60; // 城市运营商直接推荐分佣比例

        $ylgw_share_amount = truncateDecimal($order_amount * ($ylgw_rate / 100)); // 91.25
        $sqdl_share_amount = truncateDecimal($order_amount * ($sqdl_rate / 100)); // 182.5
        $qydl_indirect_share_amount = truncateDecimal($order_amount * ($qydl_indirect_rate / 100)); // 36.5
        $qydl_direct_share_amount = truncateDecimal($order_amount * ($qydl_direct_rate / 100)); // 219


        $qydl_distributed = false;
        $sqdl_distributed = false;
        $ylgw_distributed = false;

        // 1. 城市运营商 (QYDL) 分佣
        if ($nearest_qydl) {
            // 规则1: A(QYDL) -> B(YLGW) -> C(New YLGW). A获得60%
            if ($buyer_direct_parent && $buyer_direct_parent['is_ylgw'] == 1 && $nearest_qydl['id'] == $buyer_direct_parent['parent_id']) {
                 \app\common\model\User::money($qydl_direct_share_amount, $nearest_qydl['id'], '城市运营商直接推荐分佣-' . $package_name, 2, $order->order_no, 'fenyong');
                 \think\Log::info("课程16分佣-场景1：城市运营商（A）直接推荐养老顾问（B）的下级（C）成为养老顾问，A获得60% - 用户ID: {$nearest_qydl['id']}, 金额: {$qydl_direct_share_amount}");
                 $qydl_distributed = true;
            }
            // 规则2, 7: A(QYDL) -> B(SQDL) -> C(YLGW) -> D(New YLGW). A获得10%
            // 检查直接上级是否是养老院长，且最近城市运营商是养老院长的上级
            elseif ($buyer_direct_parent && $buyer_direct_parent['is_sqdl'] == 1 && $nearest_qydl['id'] == $buyer_direct_parent['parent_id']) {
                \app\common\model\User::money($qydl_indirect_share_amount, $nearest_qydl['id'], '城市运营商间接推荐分佣-' . $package_name, 2, $order->order_no, 'fenyong');
                \think\Log::info("课程16分佣-场景2/7：城市运营商（A）间接推荐，A获得10% - 用户ID: {$nearest_qydl['id']}, 金额: {$qydl_indirect_share_amount}");
                $qydl_distributed = true;
            }
        }

        // 2. 养老院长 (SQDL) 分佣
        if ($nearest_sqdl) {
            // 规则2, 3: B(SQDL) -> C(YLGW) -> D 或 A(SQDL) -> B(YLGW) -> C. SQDL获得50%
            // 这适用于SQDL是直接上级，或者YLGW是直接上级且SQDL是YLGW的上级
            if ($nearest_sqdl['id'] == $buyer_direct_parent_id || ($buyer_direct_parent && $buyer_direct_parent['is_ylgw'] == 1 && $nearest_sqdl['id'] == $buyer_direct_parent['parent_id'])) {
                \app\common\model\User::money($sqdl_share_amount, $nearest_sqdl['id'], '养老院长分佣-' . $package_name, 2, $order->order_no, 'fenyong');
                \think\Log::info("课程16分佣-场景2/3/4：养老院长（B/A）获得50% - 用户ID: {$nearest_sqdl['id']}, 金额: {$sqdl_share_amount}");
                $sqdl_distributed = true;
            }
        }

        // 3. 养老顾问 (YLGW) 分佣
        if ($nearest_ylgw) {
            // 规则1, 2, 3, 5: YLGW获得25%
            // 这适用于YLGW是直接上级
            if ($nearest_ylgw['id'] == $buyer_direct_parent_id) {
                // 检查YLGW是否有更高级别上级（SQDL或QYDL）
                $ylgw_parent_of_ylgw = null;
                if ($nearest_ylgw['parent_id'] && $nearest_ylgw['parent_id'] > 0) {
                    $ylgw_parent_of_ylgw = \app\common\model\User::where('id', $nearest_ylgw['parent_id'])->field('is_sqdl,is_qydl')->find();
                }
                $ylgw_has_higher_superior = ($ylgw_parent_of_ylgw && ($ylgw_parent_of_ylgw['is_sqdl'] == 1 || $ylgw_parent_of_ylgw['is_qydl'] == 1));

                if ($ylgw_has_higher_superior) {
                    // YLGW有更高级别上级，记录到ylgw_total_commission
                    \app\common\model\User::where('id', $nearest_ylgw['id'])->setInc('ylgw_total_commission', $ylgw_share_amount);
                    \think\Log::info("课程16分佣-养老顾问（B/C）有上级，记录到ylgw_total_commission - 用户ID: {$nearest_ylgw['id']}, 金额: {$ylgw_share_amount}");
                } else {
                    // YLGW没有更高级别上级，直接入账（规则5）
                    \app\common\model\User::money($ylgw_share_amount, $nearest_ylgw['id'], '养老顾问分佣-' . $package_name, 2, $order->order_no, 'fenyong');
                    \think\Log::info("课程16分佣-养老顾问（A/B/C）无上级，直接分佣到money - 用户ID: {$nearest_ylgw['id']}, 金额: {$ylgw_share_amount}");
                }
                $ylgw_distributed = true;
            }
        }

        // 规则6：普通用户的下级成为养老顾问,则都不分佣金.
        // 这由 nearest_ylgw, nearest_sqdl, nearest_qydl 为空的初始检查处理。
        // 如果上级链中没有找到代理，则不会进入任何佣金块。

        // 最终日志检查
        if (!$qydl_distributed && !$sqdl_distributed && !$ylgw_distributed) {
            \think\Log::info("课程16分佣-未分佣，可能用户无符合条件上级或自购");
        }
    }
 /**
     * 处理上级是养老顾问的情况（新价格差逻辑）
     */
    private static function handleYlgwParentNew($ylgw_user, $commission_amount, $package_name, $order)
    {
        // 检查养老顾问的上级
        if (!$ylgw_user['parent_id'] || $ylgw_user['parent_id'] == 0) {
            // 情况5：养老顾问无上级，直接分给养老顾问到money，不记录ylgw_total_commission
            \app\common\model\User::money($commission_amount, $ylgw_user['id'], '养老顾问分佣-' . $package_name, 2, $order->order_no, 'fenyong');
            \think\Log::info("课程16分佣-情况5：养老顾问无上级，直接分佣到money - 用户ID: {$ylgw_user['id']}, 金额: {$commission_amount}");
            return;
        }

        // 有上级的养老顾问才记录ylgw_total_commission
        \app\common\model\User::where('id', $ylgw_user['id'])->setInc('ylgw_total_commission', $commission_amount);
        \think\Log::info("课程16分佣-有上级的养老顾问获得ylgw_total_commission - 用户ID: {$ylgw_user['id']}, 金额: {$commission_amount}");

        // 这里不再继续向上分佣，因为价格差已经分给了直接上级
    }
     /**
     * 处理上级是养老院长的情况（新价格差逻辑）
     */
    private static function handleSqdlParentNew($sqdl_user, $commission_amount, $package_name, $order)
    {
        // 养老院长获得价格差分佣
        \app\common\model\User::money($commission_amount, $sqdl_user['id'], '养老院长分佣-' . $package_name, 2, $order->order_no, 'fenyong');
        \think\Log::info("课程16分佣-养老院长价格差分佣 - 用户ID: {$sqdl_user['id']}, 金额: {$commission_amount}");

        // 这里不再继续向上分佣，因为价格差已经分给了直接上级
    }
    
    /**
     * 处理上级是城市运营商的情况（新价格差逻辑）
     */
    private static function handleQydlParentNew($qydl_user, $commission_amount, $package_name, $order)
    {
        // 城市运营商获得价格差分佣
        \app\common\model\User::money($commission_amount, $qydl_user['id'], '城市运营商分佣-' . $package_name, 2, $order->order_no, 'fenyong');
        \think\Log::info("课程16分佣-城市运营商价格差分佣 - 用户ID: {$qydl_user['id']}, 金额: {$commission_amount}");
    }
    /**
     * 执行新的分佣逻辑
     */
    private function executeNewCommission($order, $package = null)
    {
        
        // 获取分佣配置
        $sq_rate = config('site.course_sq_commission_rate') ?: 50; // 养老院长分佣比例
        $qy_rate = config('site.course_qy_commission_rate') ?: 10; // 城市运营商分佣比例
        $ylgw_rate = config('site.course_ylgw_commission_rate') ?: 50; // 养老顾问推荐分佣比例

        // 计算分佣金额（基于订单总价）
        $sq_money = truncateDecimal($order->total_price * ($sq_rate / 100));
        $qy_money = truncateDecimal($order->total_price * ($qy_rate / 100));
        $ylgw_money = truncateDecimal($sq_money * ($ylgw_rate / 100));

        $user = User::where('id', $order->user_id)->field('id,parent_id,is_sqdl,is_qydl')->find();
        $package_name = $package ? $package->name : ' ';

        // 发放养老院长佣金
        if ($sq_money > 0 && $user['parent_id'] > 0 && $user['is_sqdl'] != 1) {
            $sq_user = \app\common\model\User::getOneSq($order->user_id);
            if (!empty($sq_user)) {
                \app\common\model\User::money($sq_money, $sq_user['id'], '养老顾问套餐养老院长分成' . $package_name, 2, $order->order_no, 'fenyong');
            }
        }

        // 发放城市运营商佣金
        if ($qy_money > 0) {
            $qy_user = \app\common\model\User::getOneQy($order->user_id);
            if (!empty($qy_user)) {
                \app\common\model\User::money($qy_money, $qy_user['id'], '养老顾问套餐城市运营商分成-' . $package_name, 2, $order->order_no, 'fenyong');
            }
        }

        // 发放养老顾问推荐佣金
        if ($ylgw_money > 0) {
            $ylgw_user = \app\common\model\User::getOneYlgw($order->user_id);
            if (!empty($ylgw_user)) {
                // 检查是否是推荐分佣（购买用户的直接上级就是养老顾问）
                $is_referral_commission = ($user['parent_id'] == $ylgw_user['id']);

                if ($is_referral_commission) {
                    // 推荐分佣：直接发放，不受上级关系影响
                    \app\common\model\User::money($ylgw_money, $ylgw_user['id'], '养老顾问推荐分成-' . $package_name, 2, $order->order_no, 'fenyong');
                } else {
                    // 普通分佣：按原有逻辑处理
                    $ylgw_parent = \app\common\model\User::where('id', $ylgw_user['id'])->field('parent_id,is_sqdl,is_qydl')->find();
                    $has_superior = false;

                    if ($ylgw_parent && $ylgw_parent['parent_id'] > 0) {
                        $parent_user = \app\common\model\User::where('id', $ylgw_parent['parent_id'])->field('is_sqdl,is_qydl')->find();
                        if ($parent_user && ($parent_user['is_sqdl'] == 1 || $parent_user['is_qydl'] == 1)) {
                            $has_superior = true;
                        }
                    }

                    if (!$has_superior) {
                        // 没有上级，直接发放到账户余额
                        \app\common\model\User::money($ylgw_money, $ylgw_user['id'], '养老顾问套餐养老顾问佣金-' . $package_name, 2, $order->order_no, 'fenyong');
                    } else {
                        // 有上级，记录到养老顾问总佣金字段
                        \app\common\model\User::where('id', $ylgw_user['id'])->setInc('ylgw_total_commission', $ylgw_money);
                        // 记录佣金日志
                        \app\common\model\MoneyLog::create([
                            'user_id' => $ylgw_user['id'],
                            'money' => $ylgw_money,
                            'before' => $ylgw_user['ylgw_total_commission'],
                            'after' => $ylgw_user['ylgw_total_commission'] + $ylgw_money,
                            'memo' => '养老顾问佣金-' . $package_name,
                            'type' => 'fenyong',
                            'createtime' => time()
                        ]);
                    }
                }
            }
        }
    }
}

/**
 * 截取小数位数
 */
function truncateDecimal($number, $decimals = 2) {
    $factor = pow(10, $decimals);
    return floor($number * $factor) / $factor;
}
