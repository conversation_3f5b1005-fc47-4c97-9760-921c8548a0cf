<?php
/**
 * 测试用户身份唯一性验证功能
 */

// 模拟POST数据测试身份唯一性验证逻辑
function testIdentityValidation($params) {
    $identityCount = 0;
    $identityFields = ['is_sqdl', 'is_qydl', 'is_ylgw', 'is_shop'];
    
    foreach ($identityFields as $field) {
        if (isset($params[$field]) && $params[$field] == 1) {
            $identityCount++;
        }
    }
    
    if ($identityCount > 1) {
        return '用户只能有一个身份，请选择其中一个身份';
    }
    
    return '验证通过';
}

// 测试用例
echo "=== 用户身份唯一性验证测试 ===\n\n";

// 测试用例1：只选择一个身份（养老顾问）
$test1 = [
    'is_sqdl' => 0,
    'is_qydl' => 0,
    'is_ylgw' => 1,
    'is_shop' => 0
];
echo "测试用例1 - 只选择养老顾问：" . testIdentityValidation($test1) . "\n";

// 测试用例2：选择多个身份（养老院长 + 养老顾问）
$test2 = [
    'is_sqdl' => 1,
    'is_qydl' => 0,
    'is_ylgw' => 1,
    'is_shop' => 0
];
echo "测试用例2 - 选择养老院长+养老顾问：" . testIdentityValidation($test2) . "\n";

// 测试用例3：选择多个身份（城市运营商 + 供货商）
$test3 = [
    'is_sqdl' => 0,
    'is_qydl' => 1,
    'is_ylgw' => 0,
    'is_shop' => 1
];
echo "测试用例3 - 选择城市运营商+供货商：" . testIdentityValidation($test3) . "\n";

// 测试用例4：不选择任何身份
$test4 = [
    'is_sqdl' => 0,
    'is_qydl' => 0,
    'is_ylgw' => 0,
    'is_shop' => 0
];
echo "测试用例4 - 不选择任何身份：" . testIdentityValidation($test4) . "\n";

// 测试用例5：只选择养老院长
$test5 = [
    'is_sqdl' => 1,
    'is_qydl' => 0,
    'is_ylgw' => 0,
    'is_shop' => 0
];
echo "测试用例5 - 只选择养老院长：" . testIdentityValidation($test5) . "\n";

echo "\n=== 测试完成 ===\n";
