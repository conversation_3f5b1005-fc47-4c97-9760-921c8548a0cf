-- 完善养老顾问分佣系统
-- 添加缺失的数据库字段和配置项

-- 1. 为商城商品表添加养老顾问分成比例字段
ALTER TABLE `fa_wanlshop_goods` ADD COLUMN `ylgw_bili` decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问分成比例' AFTER `quyu_bili`;

-- 2. 为服务商品表添加养老顾问分成比例字段
ALTER TABLE `fa_service_goods` ADD COLUMN `ylgw_bili` decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问分成比例' AFTER `quyu_bili`;

-- 3. 添加全局配置项到fa_config表
INSERT INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) VALUES 
('goods_ylgw_bili', 'site', '商城商品养老顾问分成比例', '设置商城商品的养老顾问分成比例，单位为百分比', 'number', '5', '', 'required', ''),
('service_ylgw_bili', 'site', '服务商品养老顾问分成比例', '设置服务商品的养老顾问分成比例，单位为百分比', 'number', '5', '', 'required', ''),
('course_ylgw_bili', 'site', '课程养老顾问分成比例', '设置课程的养老顾问分成比例，单位为百分比', 'number', '5', '', 'required', '');

-- 4. 检查现有配置项是否存在，如果不存在则添加
INSERT IGNORE INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) VALUES 
('gjia_bili', 'site', '全局养老顾问分成比例', '设置全局的养老顾问分成比例，单位为百分比', 'number', '5', '', 'required', ''),
('course_teacher_rate', 'site', '课程老师分成比例', '设置课程老师的分成比例，单位为百分比', 'number', '30', '', 'required', ''),
('course_shequ_rate', 'site', '课程养老院长分成比例', '设置课程的养老院长分成比例，单位为百分比', 'number', '10', '', 'required', ''),
('course_quyu_rate', 'site', '课程城市运营商分成比例', '设置课程的城市运营商分成比例，单位为百分比', 'number', '15', '', 'required', ''),
('goods_shequ_bili', 'site', '商城商品养老院长分成比例', '设置商城商品的养老院长分成比例，单位为百分比', 'number', '10', '', 'required', ''),
('goods_quyu_bili', 'site', '商城商品城市运营商分成比例', '设置商城商品的城市运营商分成比例，单位为百分比', 'number', '15', '', 'required', ''),
('service_shequ_bili', 'site', '服务商品养老院长分成比例', '设置服务商品的养老院长分成比例，单位为百分比', 'number', '10', '', 'required', ''),
('service_quyu_bili', 'site', '服务商品城市运营商分成比例', '设置服务商品的城市运营商分成比例，单位为百分比', 'number', '15', '', 'required', '');

-- 5. 查看添加结果
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'fa_wanlshop_goods' 
AND COLUMN_NAME = 'ylgw_bili';

SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'fa_service_goods' 
AND COLUMN_NAME = 'ylgw_bili';

-- 6. 查看配置项
SELECT name, title, value FROM fa_config WHERE name LIKE '%ylgw%' OR name LIKE '%bili%' ORDER BY name;
