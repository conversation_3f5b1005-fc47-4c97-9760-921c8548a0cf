<?php

return [
    'Id'                => 'ID',
    'Order_no'          => '订单号',
    'User_id'           => '用户ID',
    'Course_id'         => '课程ID',
    'Platform'          => '开通方式',
    'Platform quota'    => '额度开通',
    'Platform admin'    => '管理员开通',
    'Platform ylgw'     => '养老顾问开通',
    'Platform wxmin'    => '自主购买(小程序)',
    'Platform h5'       => '自主购买(H5)',
    'Platform app'      => '自主购买(APP)',
    'Total_price'       => '订单金额',
    'Pay_price'         => '支付金额',
    'Pay_type'          => '支付方式',
    'Pay_type 0'        => '无',
    'Pay_type 1'        => '微信',
    'Pay_type 2'        => '余额',
    'Pay_type 3'        => '额度支付', 
    'Pay_status'        => '支付状态',
    'Pay_status 1'      => '待支付',
    'Pay_status 2'      => '已支付',
    'Paytime'           => '支付时间',
    'Createtime'        => '创建时间',
    'Updatetime'        => '更新时间',
    'User.nickname'     => '用户昵称',
    'User.mobile'       => '手机号',
    'Course.name'       => '课程名称',
    'Opener_info'       => '开通者信息',
    'Operation not allowed' => '不允许此操作'
];
