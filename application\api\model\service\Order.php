<?php

namespace app\api\model\service;

use app\admin\model\Invoice;
use app\admin\model\ShareGoodsOrder;
use app\common\model\UserArea;
use app\service\ServiceScore;
use stdClass;
use think\App;
use think\Model;
use traits\model\SoftDelete;
use think\Exception;
use fast\Random;
class Order extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'service_order';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    /**
     * 项目结算
     * @param $param
     * @return array
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function settle($params)
    {
        $params['night_money'] = $params['night_money'] ?? 0;
        $goods = Goods::where(['id' => $params['goods_id'], 'status' => 'normal'])->field('id,image,is_yh,response_hour,start_hour,end_hour,shop_id,name,type,price,night_money,cost_seconds,to_shop,is_travel,choose_skill_type,goods_type,skill_cate_ids')->find();
        if (!$goods) {
            throw new Exception('项目信息异常,无法下单');
        }

        $data['goods'] = $goods;
        $data['goods_type'] = $goods['goods_type'];
        $data['sumprice'] = bcmul($goods['price'], $params['num'], 2);

        $data['shop'] = $goods['type'] == 1 ? Shop::getShop($goods['shop_id']) : '';

        $data['skill_cate_ids']=$goods['skill_cate_ids'];

        if ($params['goods_sku_id']) {
            $goodsSku = GoodsSku::where(['id' => $params['goods_sku_id'], 'goods_id' => $goods['id'], 'status' => 'normal'])->field('id,name,price,cost_seconds')->find();
            if (!$goodsSku['id']) {
                throw new Exception('规格信息异常');
            }
            $data['goodsSku'] = $goodsSku;
            $goods['price'] = $goodsSku['price'];
            $data['sumprice'] = sprintf("%.2f", $data['goodsSku']['price'] * $params['num']);
        }
        $fj_money = 0;
        //有附加服务
        if(isset($params['goodsadd_ids']) && $params['goodsadd_ids']){
            $fj_money = \app\api\model\service\GoodsAdd::where('id','in',$params['goodsadd_ids'])->sum('price');
        }

        $yh_money = 0;
        //有医护工具
        if(isset($params['goodsadd2_ids']) && $params['goodsadd2_ids']){
            $yh_money = \app\api\model\service\GoodsAdd::where('id','in',$params['goodsadd2_ids'])->sum('price');
        }
        $data['sumprice'] += $params['night_money'];//夜间费
        $data['sumprice'] += $fj_money;//附加服务
        $data['sumprice'] += $yh_money;//医护工具
        if ($params['coupon_id']) {
            $coupon = UserCoupon::checkCoupon($params['coupon_id'], $goods,3);
            if (!$coupon) {
                throw new Exception('优惠券无法使用');
            }
            $data['coupon_id'] = $params['coupon_id'];
            $data['coupon_price'] = sprintf("%.2f", $coupon['reduce']);
            $data['sumprice'] = bcsub($data['sumprice'], $data['coupon_price'],2);
        }
        return $data;
    }
    /**
     * 创建订单服务包
     * @param $params
     * @return array
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function createOrderNum($params)
    {
        $goods = self::settle($params);

        $userInfo = UserInfo::where(['user_id' => $params['user_id']])->field('discount,is_plus')->find();
        $data = ['user_id' => $params['user_id'],'goods_id'=>$goods['goods']['id'],'discount'=>$userInfo['discount'],'city'=>$params['city'],'traveltype' => $params['traveltype'], 'travel_price' => $params['travel_price'],'distance'=>$params['distance'], 'choose_skill_type' => $params['choose_skill_type'], 'paytype' => $params['paytype'], 'to_shop' => $params['to_shop'], 'orderId' => 'GoodsOrd' . Random::alnum(4) . '-' . $params['user_id'] . '-' . time(), 'memo' => $params['memo']];

        if($params['shop_id'])
        {
            $data['shop_id'] = $params['shop_id'];
        }elseif($goods['goods']['shop_id']){
            $data['shop_id'] = $goods['goods']['shop_id'];
        }
        $data['goods_type'] = $goods['goods_type'];
        $data['total_cost_seconds'] = isset($goods['goodsSku']) ? intval($goods['goodsSku']['cost_seconds'] * $params['num']) : intval($goods['goods']['cost_seconds'] * $params['num']);
        $data['total_cost_sy'] = $data['total_cost_seconds'];

        $price = isset($goods['goodsSku']) ? $goods['goodsSku']['price'] : $goods['goods']['price'];

        $data['price'] = bcmul($price, $params['num'], 2);
        $data['sumprice'] = bcadd($data['price'], $params['travel_price'], 2);
        $fj_money = 0;
        //有附加服务
        if(isset($params['goodsadd_ids']) && $params['goodsadd_ids']){
            $fj_money = \app\api\model\service\GoodsAdd::where('id','in',$params['goodsadd_ids'])->sum('price');
        }

        $yh_money = 0;
        //有医护工具
        if(isset($params['goodsadd2_ids']) && $params['goodsadd2_ids']){
            $yh_money = \app\api\model\service\GoodsAdd::where('id','in',$params['goodsadd2_ids'])->sum('price');
        }
        $data['sumprice'] += $params['night_money'];//夜间费
        $data['sumprice'] += $fj_money;//附加服务
        $data['sumprice'] += $yh_money;//医护工具

        $data['goods_total_price'] = $data['sumprice'];
        $data['payprice'] = $data['sumprice'];
        if (!strstr($goods['goods']['to_shop'], $params['to_shop'])) {
            throw new Exception('当前服务并无所选出行方式');
        }
        $needtime = $data['total_cost_seconds'] * 60;
        if($params['to_shop'] == 'shop' && $params['skill_id'])
        {
            $params['skill_id'] = '';
        }

        if ($params['skill_id']) {

            $skill = Skill::skillInfo($params['skill_id']);
            if($skill['is_rest'] == 1)
            {
                throw new Exception('服务者休息中,无法预约');
            }
            $data['skill_id'] = $skill['id'];
            // $data['shop_id'] = $skill['shop_id'];
            $data['choose_skill_type'] = 1;
            // $timeInfo = SkillTime::where(['skill_id' => $params['skill_id'], 'id' => $params['time_id'], 'state' => 0])->field('id,starttime')->find();
            // if (!$timeInfo) {
            //     throw new Exception('当前时间已占用,无法预约');
            // }
            // $data['starttime'] = $timeInfo['starttime'];
            // $data['endtime'] = $timeInfo['starttime'] + $needtime;
            // $data['actendtime'] = $data['endtime'] + 1799;
            // $timeExist = SkillTime::where(['state' => ['>', 0], 'skill_id' => $params['skill_id'], 'starttime' => ['between', [$data['starttime'], $data['actendtime']]]])->value('id');
            // if ($timeExist) {
            //     throw new Exception('当前时间区间有被预约,请更换其他时间区间');
            // }
        }
        if ($params['starttime'] < time()) {
            throw new Exception('选择时间不可小于当前时间');
        }
        $data['starttime'] = $params['starttime'];
        $data['endtime'] = $params['starttime'] + $needtime;
        $data['actendtime'] = $data['endtime'] + 1799;

        if ($params['coupon_id']) {
            $coupon = UserCoupon::checkCoupon($params['coupon_id'], $goods,3);
            if (!$coupon) {
                throw new Exception('优惠券无法使用');
            }
            $data['coupon_id'] = $params['coupon_id'];
            $data['coupon_price'] = sprintf("%.2f", $coupon['reduce']);
            $data['payprice'] = bcsub($data['payprice'], $data['coupon_price'],2);
        }
        if(empty($data['shop_id']) && empty($params['skill_id']))
        {
            $data['is_pool'] = 1;
        }
        $userMoney = User::getMoney($params['user_id']);
        if ($params['paytype'] == 4 && $userMoney < $data['payprice']) {
            throw new Exception('余额不足,请及时充值');
        }
        $address = model('app\api\model\wanlshop\Address')
            ->where(['id' => $params['address_id']])
            ->field('name,mobile,province,city,district,address,formatted_address as area,location')
            ->find()->toArray();
        if(!$address){
            throw new Exception('用户地址错误');
        }
        $data['district'] = $address['district']?:'';
        $data['goodsadd_ids'] = isset($params['goodsadd_ids']) ? $params['goodsadd_ids'] : '';
        $data['goodsadd2_ids'] = isset($params['goodsadd2_ids']) ? $params['goodsadd2_ids'] : '';
        $order = new Order($data);
        $order->allowField(true)->save();
        OrderLog::create(['order_id' => $order->id, 'user_id' => $params['user_id'], 'content' => '用户创建订单成功']);
        if ($params['to_shop'] == 'door' && $params['address_id']) {

            $location = explode(',',$address['location']);
            $address['lng'] = $location[0];
            $address['lat'] = $location[1];
            unset($address['location']);
            if($params['skill_id'])
            {
                $skillCity = Skill::where('id',$params['skill_id'])->value('city');
                if($skillCity != $address['city'])
                {
                    throw new Exception('上门地址请选择服务者所在城市');
                }
            }
            $address['user_id'] = $params['user_id'];
            $address['order_id'] = $order->id;
            OrderAddress::create($address);
        }
        $sku_name = isset($goods['goodsSku']) ? $goods['goodsSku']['name'] : '';
        $detailData = ['user_id' => $params['user_id'], 'order_id' => $order->id, 'goods_id' => $params['goods_id'], 'goods_sku_id' => $params['goods_sku_id'], 'skill_id' => $params['skill_id'], 'name' => $goods['goods']['name'], 'image' => $goods['goods']['image'], 'num' => $params['num'], 'sku_name' => $sku_name, 'price' => $price, 'sumprice' => $data['sumprice']];
        $detailData['bfw_id'] = isset($params['bfw_id']) ? $params['bfw_id'] : 0;
        $detailData['bfw_name'] = isset($params['bfw_name']) ? $params['bfw_name'] : '';
        $detailData['bfw_mobile'] = isset($params['bfw_mobile']) ? $params['bfw_mobile'] : '';
        $detailData['bfw_sex'] = isset($params['bfw_sex']) ? $params['bfw_sex'] : '';
        $detailData['service_time_json'] = isset($params['service_time_json']) ? json_encode($params['service_time_json']): '';
        $detailData['case'] = isset($params['case']) ? $params['case'] : '';
        $detailData['is_yh_tools'] = isset($params['is_yh_tools']) ? $params['is_yh_tools'] : 0;
        $detailData['is_gm'] = isset($params['is_gm']) ? $params['is_gm'] : 0;
        $detailData['gm_text'] = isset($params['gm_text']) ? $params['gm_text'] : '';
        $detailData['cf_images'] = isset($params['cf_images']) ? $params['cf_images'] : '';
        $detailData['yp_images'] = isset($params['yp_images']) ? $params['yp_images'] : '';
        $orderDetail = new OrderDetail($detailData);
        $orderDetail->allowField(true)->save();
        $re = [];
        if (in_array($params['paytype'], [0, 1, 2, 3])) {
            $re['pay'] = \addons\service\library\Pay::payOrder(['amount' => $data['payprice'], 'orderid' => $data['orderId'], 'title' => '支付项目费用'], $params['paytype'], $params['user_id'], 0);
        } elseif ($params['paytype'] == 4) {
            User::money(-$data['payprice'], $data['user_id'], '支付' . $goods['goods']['name'] . '项目费用');
            $order->save(['status' => 1, 'paytime' => time()], ['id' => $order->id]);
            $re['pay'] = $order;
            self::finishPay($order->id);
        }
        return $re;
    }

    /**
     * 创建订单
     * @param $params
     * @return array
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function createOrder($params)
    {
        $goods = self::settle($params);
        $userInfo = UserInfo::where(['user_id' => $params['user_id']])->field('discount,is_plus')->find();
        $data = ['user_id' => $params['user_id'],'goods_id'=>$goods['goods']['id'],'discount'=>$userInfo['discount'],'city'=>$params['city'],'traveltype' => $params['traveltype'], 'travel_price' => $params['travel_price'],'distance'=>$params['distance'], 'choose_skill_type' => $params['choose_skill_type'], 'paytype' => $params['paytype'], 'to_shop' => $params['to_shop'], 'orderId' => 'GoodsOrd' . Random::alnum(4) . '-' . $params['user_id'] . '-' . time(), 'memo' => $params['memo']];
        if($params['shop_id'])
        {
            $data['shop_id'] = $params['shop_id'];
        }elseif($goods['goods']['shop_id']){
            $data['shop_id'] = $goods['goods']['shop_id'];
        }
        $data['goods_type'] = $goods['goods_type'];
        $data['total_cost_seconds'] = isset($goods['goodsSku']) ? intval($goods['goodsSku']['cost_seconds'] * $params['num']) : intval($goods['goods']['cost_seconds'] * $params['num']);
        $data['total_cost_sy'] = $data['total_cost_seconds'];

        $price = isset($goods['goodsSku']) ? $goods['goodsSku']['price'] : $goods['goods']['price'];

        $data['price'] = bcmul($price, $params['num'], 2);
        $data['sumprice'] = bcadd($data['price'], $params['travel_price'], 2);
        $fj_money = 0;
        //有附加服务
        if(isset($params['goodsadd_ids']) && $params['goodsadd_ids']){
            $fj_money = \app\api\model\service\GoodsAdd::where('id','in',$params['goodsadd_ids'])->sum('price');
        }

        $yh_money = 0;
        //有医护工具
        if(isset($params['goodsadd2_ids']) && $params['goodsadd2_ids']){
            $yh_money = \app\api\model\service\GoodsAdd::where('id','in',$params['goodsadd2_ids'])->sum('price');
        }
        $data['sumprice'] += $params['night_money'];//夜间费
        $data['sumprice'] += $fj_money;//附加服务
        $data['sumprice'] += $yh_money;//医护工具

        $data['goods_total_price'] = $data['sumprice'];
        $data['payprice'] = $data['sumprice'];
        $data['night_money'] = $params['night_money'];

        if (!strstr($goods['goods']['to_shop'], $params['to_shop'])) {
            throw new Exception('当前服务并无所选出行方式');
        }

        $needtime = $data['total_cost_seconds'] * 60;

        if($params['to_shop'] == 'shop' && $params['skill_id'])
        {
            $params['skill_id'] = '';
        }

        if (isset($params['skill_id']) && $params['skill_id']) {

            $skill = Skill::skillInfo($params['skill_id']);
            if($skill['is_rest'] == 1)
            {
                throw new Exception('服务者休息中,无法预约');
            }
            $data['skill_id'] = $skill['id'];
            // $data['shop_id'] = $skill['shop_id'];
            $data['choose_skill_type'] = 1;
            // $timeInfo = SkillTime::where(['skill_id' => $params['skill_id'], 'id' => $params['time_id'], 'state' => 0])->field('id,starttime')->find();
            // if (!$timeInfo) {
            //     throw new Exception('当前时间已占用,无法预约');
            // }
            // $data['starttime'] = $timeInfo['starttime'];
            // $data['endtime'] = $timeInfo['starttime'] + $needtime;
            // $data['actendtime'] = $data['endtime'] + 1799;
            // $timeExist = SkillTime::where(['state' => ['>', 0], 'skill_id' => $params['skill_id'], 'starttime' => ['between', [$data['starttime'], $data['actendtime']]]])->value('id');
            // if ($timeExist) {
            //     throw new Exception('当前时间区间有被预约,请更换其他时间区间');
            // }
        }
        // if ($params['starttime'] < time()) {
        //     throw new Exception('选择时间不可小于当前时间');
        // }
        if($params['starttime']){
            $data['starttime'] = strtotime($params['starttime']);
            // $data['endtime'] = $params['starttime'] + $needtime;
            // $data['actendtime'] = $data['endtime'] + 1799;
        }

        $data['is_wt'] = $params['is_wt'];
        $data['sex'] = isset($params['sex']) ? $params['sex'] : 0;
        $data['goodsadd_ids'] = isset($params['goodsadd_ids']) ? $params['goodsadd_ids'] : '';
        $data['goodsadd2_ids'] = isset($params['goodsadd2_ids']) ? $params['goodsadd2_ids'] : '';
        if ($params['coupon_id']) {
            $coupon = UserCoupon::checkCoupon($params['coupon_id'], $goods,3);
            if (!$coupon) {
                throw new Exception('优惠券无法使用');
            }
            $data['coupon_id'] = $params['coupon_id'];
            $data['coupon_price'] = sprintf("%.2f", $coupon['reduce']);
            $data['payprice'] = bcsub($data['payprice'], $data['coupon_price'],2);
        }
        if(empty($data['shop_id']) && empty($params['skill_id']))
        {
            $data['is_pool'] = 1;
        }
        $userMoney = User::getMoney($params['user_id']);
        if ($params['paytype'] == 4 && $userMoney < $data['payprice']) {
            throw new Exception('余额不足,请及时充值');
        }
        $address = model('app\api\model\wanlshop\Address')
            ->where(['id' => $params['address_id']])
            ->field('name,mobile,province,city,district,address,formatted_address as area,location')
            ->find()->toArray();
        if(!$address){
            throw new Exception('用户地址错误');
        }

        if(!empty($params['share_id'])){
            $share_order=ShareGoodsOrder::where(['id'=>$params['share_id'],'goods_type'=>2])->find();
            if(!empty($share_order) && $share_order['reduce']>0) {
                $data['payprice'] = bcsub($data['payprice'], $share_order['reduce'], 2);
                $share_order->save(['status'=>3]);
            }
        }
//        $data['province'] = $address['province'];
        $data['city'] = $address['city']?:'';
        $data['district'] = $address['district']?:'';
        $data['pay_end_time'] = time() + 1800;
        if($data['total_cost_sy']>1 && $data['goods_id']!=28){
            $data['is_fwb']=1;
        }
        if($data['district']==''){
            throw new Exception('当前选择地址不详细,请重新编辑地址');
        }
        $data['skill_cate_ids'] = $goods['goods']['skill_cate_ids'];
        $order = new Order($data);
        $order->allowField(true)->save();
        OrderLog::create(['order_id' => $order->id, 'user_id' => $params['user_id'], 'content' => '用户创建订单成功']);
        if ($params['to_shop'] == 'door' && $params['address_id']) {

            $location = explode(',',$address['location']);
            $address['lng'] = $location[0]??'';
            $address['lat'] = $location[1]??'';
            unset($address['location']);
            if(!empty($params['skill_id']))
            {
                $skillCity = Skill::where('id',$params['skill_id'])->value('city');
                if($skillCity != $address['city'])
                {
                    throw new Exception('上门地址请选择服务者所在城市');
                }
            }
            $address['user_id'] = $params['user_id'];
            $address['order_id'] = $order->id;
            $address['address_id'] = $params['address_id'];
            OrderAddress::create($address);
        }
        $sku_name = isset($goods['goodsSku']) ? $goods['goodsSku']['name'] : '';
        $detailData = ['is_yh'=>$goods['goods']['is_yh'],'user_id' => $params['user_id'], 'order_id' => $order->id, 'goods_id' => $params['goods_id'], 'goods_sku_id' => $params['goods_sku_id'], 'skill_id' => $params['skill_id'], 'name' => $goods['goods']['name'], 'image' => $goods['goods']['image'], 'num' => $params['num'], 'sku_name' => $sku_name, 'price' => $price, 'sumprice' => $data['sumprice']];
        $detailData['bfw_id'] = isset($params['bfw_id']) ? $params['bfw_id'] : 0;
        $detailData['bfw_name'] = isset($params['bfw_name']) ? $params['bfw_name'] : '';
        $detailData['bfw_mobile'] = isset($params['bfw_mobile']) ? $params['bfw_mobile'] : '';
        $detailData['bfw_sex'] = isset($params['bfw_sex']) ? $params['bfw_sex'] : '';
        $detailData['service_time_json'] = isset($params['service_time_json']) ? json_encode($params['service_time_json']): '';
        $detailData['case'] = isset($params['case']) ? $params['case'] : '';
        $detailData['is_yh_tools'] = isset($params['is_yh_tools']) ? $params['is_yh_tools'] : 0;
        $detailData['is_gm'] = isset($params['is_gm']) ? $params['is_gm'] : 0;
        $detailData['gm_text'] = isset($params['gm_text']) ? $params['gm_text'] : '';
        $detailData['cf_images'] = isset($params['cf_images']) ? $params['cf_images'] : '';
        $detailData['yp_images'] = isset($params['yp_images']) ? $params['yp_images'] : '';
        $detailData['wt_desc'] = isset($params['wt_desc']) ? $params['wt_desc'] : '';
        $detailData['wt_images'] = isset($params['wt_images']) ? $params['wt_images'] : '';
        $orderDetail = new OrderDetail($detailData);
        $orderDetail->allowField(true)->save();
        $re = [];
        if (in_array($params['paytype'], [0, 1, 2, 3])) {
            $re['pay'] = \addons\service\library\Pay::payOrder(['amount' => $data['payprice'], 'orderid' => $data['orderId'], 'title' => '支付项目费用'], $params['paytype'], $params['user_id'], 0);
        } elseif ($params['paytype'] == 4) {
            User::money(-$data['payprice'], $data['user_id'], '支付' . $goods['goods']['name'] . '项目费用');
            $order->save(['status' => 1, 'paytime' => time()], ['id' => $order->id]);
            $re['pay'] = $order;
            self::finishPay($order->id);
        }
        return $re;
    }


    public static function pay($params)
    {
        $order = self::where(['id'=>$params['id'],'status'=>0,'user_id'=>$params['user_id']])->field('id,p_order_id,skill_id,shop_id,goods_id,payprice,fwb_price,night_money,user_id,to_shop,starttime,actendtime,coupon_id')->find();
        if(!$order)
        {
            throw new Exception('订单已超时,无法支付');
        }
        if($order['p_order_id']){
            $order['payprice'] = $order['fwb_price']+$order['night_money'];
        }
        $orderDetail = OrderDetail::where(['order_id'=>$order['id']])->field('goods_id,goods_sku_id,price,name')->find();
        $price = $orderDetail['goods_sku_id'] ?GoodsSku::where(['id'=>$orderDetail['goods_sku_id'],'status'=>'normal'])->value('price'):Goods::where(['id'=>$order['goods_id'],'status'=>'normal'])->value('price');
        // if(!$price || $price!=$orderDetail['price'])
        // {
        //     throw new Exception('订单项目信息发生变化,请重新下单');
        // }
       
        if ($order['coupon_id']) {
            $couponState = UserCoupon::where(['id'=>$order['coupon_id'],'user_id'=>$order['user_id']])->value('state');
            if ($couponState!=0) {
                throw new Exception('优惠券无法使用,请重新下单');
            }
        }
        if($order['starttime']<time())
        {
            throw new Exception('预约时间已超出,请重新下单');
        }
        if($order['skill_id'])
        {
            $skill = Skill::where(['id'=>$order['skill_id'],'state'=>1])->value('id');
            if(!$skill)
            {
                throw new Exception('当前服务者暂时无法接单,请重新下单');
            }
            $timeExist = SkillTime::where(['state' => ['>', 0], 'skill_id' => $order['skill_id'], 'starttime' => ['between', [$order['starttime'], $order['actendtime']]]])->value('id');
            if ($timeExist) {
                throw new Exception('当前服务者时间区间有被预约,请重新下单');
            }
        }
        $userMoney = User::getMoney($order['user_id']);
        if ($params['paytype'] == 4 && $userMoney < $order['payprice']) {
            throw new Exception('余额不足,请及时充值');
        }
        $orderId = 'GoodsOrd' . Random::alnum(4) . '-' . $params['user_id'] . '-' . time();
        self::where(['id'=>$order['id']])->update(['orderId'=>$orderId,'paytype'=>$params['paytype']]);
        $re = [];
        if (in_array($params['paytype'], [0, 1, 2, 3])) {
            $re['pay'] = \addons\service\library\Pay::payOrder(['amount' => $order['payprice'], 'orderid' => $orderId, 'title' => '支付项目费用'], $params['paytype'], $params['user_id'], 0);
        } elseif ($params['paytype'] == 4) {
            User::money(-$order['payprice'], $params['user_id'], '支付' .$orderDetail['name']. '项目费用');
            $order->save(['status' => 1, 'paytime' => time()], ['id' => $order['id']]);
            $re['pay'] = '';
            self::finishPay($order['id']);
        }
        return $re;

    }

    public static function allocationOrder($params)
    {
        $order = self::where(['id'=>$params['id'],'shop_id'=>$params['shop_id'],'status'=>1,'is_service'=>['in',[0,-1]]])->field('id,user_id,to_shop,starttime,actendtime,is_service,status')->find();
        if($order['to_shop'] != 'door' || !$order)
        {
            throw new Exception('当前订单无法分配服务者');
        }
        $skillId = Skill::where(['id'=>$params['skill_id'],'state'=>1])->value('id');
        if(!$skillId)
        {
            throw new Exception('当前服务者无法接单');
        }
        $exist = SkillTime::where(['skill_id'=>$skillId,'state'=>['in',[1,2]],'starttime'=>['between',[$order['starttime'],$order['actendtime']]]])->value('id');
        if($exist)
        {
            throw new Exception('当前服务者时间有被预约');
        }
        \app\api\model\service\SkillTime::updateSkillTime(['skill_id'=>$params['skill_id'],'starttime'=>$order['starttime'],'actendtime'=>$order['actendtime']],1);
        OrderLog::create(['order_id'=>$order['id'],'type'=>14,'content'=>'订单已分配','user_id'=>$order['user_id']]);
        self::where(['id'=>$order['id']])->update(['skill_id'=>$skillId]);
        $skillOpenid = UserInfo::getOpenid($params['skill_id'],1);
        self::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'type'=>1,'templateAttr'=>'skill_order_template','openid'=>$skillOpenid]);
        return true;
    }

    public static function finishPay($id)
    {
        $order = self::where(['id'=>$id,'status'=>1])->find();
        if(!$order)
        {
            return false;
        }
        if($order['skill_id'])
        {
            // SkillTime::updateSkillTime($order,1);
        }
        if($order['coupon_id']){
            UserCoupon::where('id',$order['coupon_id'])->update(['state'=>1,'updatetime'=>time()]);
        }
        if($order['to_shop'] == 'shop')
        {
            $update['status'] = 2;
            $update['check_name'] = time().$order['id'];
            $update['qrcode_image'] = \addons\service\library\Common::createQrcode($update['check_name']);
        }
        $update['act_travel_price'] = sprintf("%.2f",$order['travel_price']);
        self::where(['id'=>$id])->update($update);
        OrderLog::create(['order_id'=>$id,'user_id'=>$order['user_id'],'type'=>1,'content'=>'用户订单已支付']);
        Goods::where('id',$order['goods_id'])->setInc('salenums');
        if($order['skill_id'])
        {
            // $skillOpenid = UserInfo::getOpenid($order['skill_id'],1);
            // self::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'type'=>1,'templateAttr'=>'skill_order_template','openid'=>$skillOpenid]);
        }

        if($order['shop_id'])
        {
            $shopOpenid = UserInfo::getOpenid($order['shop_id'],2);
            self::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'type'=>2,'templateAttr'=>'shop_order_template','openid'=>$shopOpenid]);
        }
        return true;
    }

    public static function getTotalAccept($params)
    {

        extract($params);
        $where['status'] = ['>',1];
        if(isset($skill_id) && $skill_id != '')
        {
            $where['skill_id'] = $skill_id;
        }
        if(isset($shop_id) && $shop_id != '')
        {
            $where['shop_id'] = $shop_id;
        }
        $starttime = strtotime(date("Y-m-d",$starttime));
        $where['starttime'] = ['>=',$starttime];
        return self::where($where)->count();
    }


    /**
     * 查询订单列表s
     * @param $params
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getOrderList($params)
    {
        extract($params);
        $where['is_service'] = ['<',1];
        if (isset($status) && $status != '') {
            if($status == 3) {
                $where['status'] = ['in', [3,4,8]];
            }else if ($status == 1){//待接单
                $where['status'] = 1;
                // $where['zj_skill_id'] = $to_skill_id;
            }else{
                $where['status'] = $status;
            }
        }

        if (isset($id) && $id != '') {
            $where['id'] = $id;
        }

        // 新增：订单号搜索
        if (isset($orderId) && $orderId != '') {
            $where['orderId'] = ['like', '%' . $orderId . '%'];
        }

        // 新增：用户手机号搜索
        if (isset($user_mobile) && $user_mobile != '') {
            $userIds = \app\common\model\User::where('mobile', 'like', '%' . $user_mobile . '%')->column('id');
            if ($userIds) {
                $where['user_id'] = ['in', $userIds];
            } else {
                $where['user_id'] = 0; // 没有找到用户时返回空结果
            }
        }

        // 新增：用户昵称搜索
        if (isset($user_nickname) && $user_nickname != '') {
            $userIds = \app\common\model\User::where('nickname', 'like', '%' . $user_nickname . '%')->column('id');
            if ($userIds) {
                $where['user_id'] = ['in', $userIds];
            } else {
                $where['user_id'] = 0; // 没有找到用户时返回空结果
            }
        }

        // 新增：服务者姓名搜索
        if (isset($skill_name) && $skill_name != '') {
            $skillIds = \app\api\model\service\Skill::where('name', 'like', '%' . $skill_name . '%')->column('id');
            if ($skillIds) {
                $where['skill_id'] = ['in', $skillIds];
            } else {
                $where['skill_id'] = 0; // 没有找到服务者时返回空结果
            }
        }

        // 新增：商家名称搜索
        if (isset($shop_name) && $shop_name != '') {
            $shopIds = \app\api\model\service\Shop::where('name', 'like', '%' . $shop_name . '%')->column('id');
            if ($shopIds) {
                $where['shop_id'] = ['in', $shopIds];
            } else {
                $where['shop_id'] = 0; // 没有找到商家时返回空结果
            }
        }

        // 新增：服务项目名称搜索
        if (isset($goods_name) && $goods_name != '') {
            $goodsIds = \app\api\model\service\Goods::where('name', 'like', '%' . $goods_name . '%')->column('id');
            if ($goodsIds) {
                $where['goods_id'] = ['in', $goodsIds];
            } else {
                $where['goods_id'] = 0; // 没有找到服务项目时返回空结果
            }
        }

        // 新增：订单金额范围搜索
        if (isset($min_price) && $min_price != '') {
            $where['payprice'] = ['>=', $min_price];
        }
        if (isset($max_price) && $max_price != '') {
            if (isset($where['payprice'])) {
                $where['payprice'] = ['between', [$min_price, $max_price]];
            } else {
                $where['payprice'] = ['<=', $max_price];
            }
        }

        // 新增：创建时间范围搜索
        if (isset($start_time) && $start_time != '') {
            $startTimestamp = is_numeric($start_time) ? $start_time : strtotime($start_time);
            $where['createtime'] = ['>= ', $startTimestamp];
        }
        if (isset($end_time) && $end_time != '') {
            $endTimestamp = is_numeric($end_time) ? $end_time : strtotime($end_time);
            if (isset($where['createtime'])) {
                $startTimestamp = isset($start_time) ? (is_numeric($start_time) ? $start_time : strtotime($start_time)) : 0;
                $where['createtime'] = ['between', [$startTimestamp, $endTimestamp]];
            } else {
                $where['createtime'] = ['<=', $endTimestamp];
            }
        }

        // 新增：服务时间范围搜索
        if (isset($service_start_time) && $service_start_time != '') {
            $startTimestamp = is_numeric($service_start_time) ? $service_start_time : strtotime($service_start_time);
            $where['starttime'] = ['>=', $startTimestamp];
        }
        if (isset($service_end_time) && $service_end_time != '') {
            $endTimestamp = is_numeric($service_end_time) ? $service_end_time : strtotime($service_end_time);
            if (isset($where['starttime'])) {
                $startTimestamp = isset($service_start_time) ? (is_numeric($service_start_time) ? $service_start_time : strtotime($service_start_time)) : 0;
                $where['starttime'] = ['between', [$startTimestamp, $endTimestamp]];
            } else {
                $where['starttime'] = ['<=', $endTimestamp];
            }
        }

        if (isset($city) && $city != '') {
            $where['city'] = $city;
        }
        if (isset($district) && $district != '') {
            $where['district'] = ['in',$district];
        }
        //服务类型
        if (isset($goods_type) && $goods_type != '') {
            $where['goods_type'] = ['in',$goods_type];
        }
        if (isset($district) && $district != '') {
            $where['district'] = ['in',$district];
        }
        if (isset($is_pool) && $is_pool != '') {
            $where['is_pool'] = $is_pool;
            // $where['status'] = ['>',0];
        }
        if (isset($finish) && $finish == 1) {
            $where['status'] = ['in',[6,7]];
        }
        if (isset($not_in_finish) && $not_in_finish == 1) {
            $where['status'] = ['in',[2,3,4,5]];
        }
        //不等于未支付
        if (isset($not_pay) && $not_pay == 1) {
            $where['status'] = ['neq',0];
        }
        if (isset($not_accept) && $not_accept == 1) {
            $where['status'] = 1;
            $where['skill_id'] = ['neq',''];
        }
        if (isset($not_allocation) && $not_allocation == 1) {
            $where['status'] = 1;
            $where['skill_id'] = ['null',''];
        }
        //待出发
        if(isset($status) && $status == 2){

            $where['starttime'] = ['>',time()];
        }
        //进行中
        if (isset($not_finish) && $not_finish == 1) {
            $where['status'] = ['in',[2,3,4,5]];
        }
        //已完成
        if (isset($finish) && $finish == 1) {
            $where['status'] = ['in',[6,7]];
        }
        //售后订单
        if (isset($in_service) && $in_service == 1) {
            $where['is_service'] = ['in',[1,2,-1]];
        }
        if (isset($user_id) && $user_id != '') {
            $where['user_id'] = $user_id;
            $where['is_fwb'] = 0;
        }
        if(isset($is_all) && $is_all != '')
        {
            $where['is_service'] = ['in',[1,2,0,-1]];
        }
        if(isset($goods_ids) && $goods_ids != '')
        {
            $where['goods_id'] = ['in',$goods_ids];
        }
        if(isset($shop_id) && $shop_id != '')
        {
            $where['shop_id'] = $shop_id;
        }
        if(isset($skill_id) && $skill_id != '')
        {
            $where['skill_id'] = $skill_id;
        }
        if(isset($is_service) && $is_service != '')
        {
            $where['is_service'] = $is_service;
        }

        if(isset($qiangdan) && $qiangdan==1){
            $where['is_fwb']= 0;
//            $where['is_wt']= 0;
            $mi=config('site.djs');
            $where['paytime'] = ['>',time()-$mi*60];
        }

        if(isset($skill_cate_ids) && $skill_cate_ids!=''){
            $where['skill_cate_ids'] = ['in',explode(',',$skill_cate_ids)];
        }

        if(!empty($is_wt)) $where['is_wt'] = 1;

        if(!empty($my_fwb)){
            $where['is_wt']=0;
            $where['is_fwb']=1;
            $where['total_cost_sy'] = ['>',0] ;
        }

        if(!empty($service_fwb) && $service_fwb == 1){
            $where['is_fwb'] = 1;
            $where['goods_type'] = 1;
            $where['total_cost_sy'] = ['>',0] ;
            unset($where['paytime']);
        }

        if(!empty($p_order_id)){
            $where['p_order_id'] =$p_order_id;
        }

        if(!empty($user_skill))
        {
            $where['skill_cate_ids'] = ['in',explode(',',$user_skill)];
        }
        //服务时间查询
        if(isset($fw_time) && $fw_time != ''){
            if($fw_time == 'day1'){
                $times = strtotime('+1 day');
            }else if($fw_time == 'day3'){
                $times = strtotime('+3 day');
            }else if($fw_time == 'day7'){
                $times = strtotime('+7 day');
            }else if($fw_time == 'day14'){
                $times = strtotime('+14 day');
            }
            $where['starttime'] = ['<=',$times];
        }
        if(isset($fw_prize) && $fw_prize != ''){
            $prize = explode('-',$fw_prize);
            $where['payprice'] = ['between',[$prize[0],$prize[1]]];
        }
        $limit = 10;
        //调度中心-待接单
        if(isset($diaodu) && $diaodu == 'djd'){
            $where['status'] = 1;
            $limit = 20;
            $mi=config('site.djs');
            $where['paytime'] = ['<',time()-$mi*60];
//            $where['starttime'] = ['<',time()];
        }
        $whereraw = '1=1';
        //调度中心-待上门
        if(isset($diaodu) && $diaodu == 'dsm'){
            $limit = 20;
            $where['status'] = ['in',[2]];
            $whereraw .= ' and starttime < '. time() ;
        }
        $distance = isset($distance) ? $distance : 0;
        if(!isset($lat)) $lat = 0;
        if(!isset($lng)) $lng = 0;
        $subquery = "(6371 * acos(  
            cos(radians($lat)) *   
            cos(radians(lat)) *   
            cos(radians(lng) - radians($lng)) +   
            sin(radians($lat)) *   
            sin(radians(lat))  
          ))";
        if($distance > 0){
            $whereraw .= " and ($subquery) <= $distance";
        }

        if(isset($sort_type) && $sort_type != ''){
            if($sort_type == 1){//距离
                $order = 'distance asc';
            }elseif($sort_type == 2){//服务费
                $order = 'payprice desc';
            }elseif($sort_type == 3){//时间
                $order = 'starttime asc';
            }else{
                $order = 'createtime desc';
            }
        }else{
            $order = 'createtime desc';
        }
        $list = self::where($where)->where($whereraw)
            ->field("id,$subquery AS distance,user_id,lat,lng,skill_id,shop_id,orderId,payprice,status,refund_price,is_service,starttime,createtime,pay_end_time,price,total_cost_seconds,total_cost_sy,to_shop,memo,is_settle,goods_type,goodsadd2_ids,night_money,user_confirm,zj_time,zj_skill_id,paytime,user_confirm_time,skill_cate_ids,p_order_id,is_fwb,is_wt")
            ->order($order)->page($page ?? 1)->limit($limit)->select();

//        $list3 = self::where(['status'=>1,'goods_type'=>1])
//            ->field("id,$subquery AS distance,user_id,lat,lng,skill_id,shop_id,payprice,status,refund_price,is_service,starttime,createtime,pay_end_time,price,total_cost_seconds,total_cost_sy,to_shop,memo,is_settle,goods_type,goodsadd2_ids,night_money,user_confirm,zj_time,zj_skill_id,paytime,user_confirm_time,skill_cate_ids")
//            ->order($order)->page($page)->limit(10)->select();
//        if(!empty($list)) $list=array_merge($list,$list3);
        if(!empty($skill_id) && isset($where['status']) && $where['status']==2) {
            $list2 = self::where(['zj_skill_id' => $skill_id, 'status' => 2])->field("*,$subquery AS distance")->select();
            $list = array_merge($list, $list2 ?? []);
        }

        if(!empty($qiangdan)  && $where['status']==1) {
            $list2 = self::where(['zj_time' => ['>',time()- config('site.zjdjs')*10 ], 'status' => 1])->field("*,$subquery AS distance")->select();
            $list = array_merge($list, $list2 ?? []);
        }

        if(!empty($diaodu)  && $diaodu == 'djd') {
            $list2 = self::where(['zj_time' => ['<',time()- config('site.zjdjs')*10 ], 'status' => 1])->field("*,$subquery AS distance")->select();
            $list = array_merge($list, $list2 ?? []);
        }

        foreach ($list as &$value)
        {
            if($value['lng'] && $value['lat'] && $lat && $lng){
                $value['distance'] = round($value['distance'],2);
            }else{
                $value['distance'] = 0;
            }

            $value['sy_time'] = $value['pay_end_time'] - time();
            $value['user'] = User::where(['id'=>$value['user_id']])->field('id,nickname,avatar')->find();
            $value['address'] = OrderAddress::where(['order_id'=>$value['id']])->find();
            $value['orderDetail'] = OrderDetail::where(['order_id'=>$value['id']])->find();

            $value['shopName'] = $value['shop_id']?Shop::where('id',$value['shop_id'])->field('name,logo_image,trade_hour')->find():'';
            $value['skillInfo'] = $value['skill_id']?Skill::getOrderSkill($value['skill_id']):'';
            $value['refundInfo'] = $value['is_service'] != 0?RefundOrder::where(['order_id'=>$value['id']])->field('refund_reason,refund_price,content,note')->order('id desc')->find():'';
            $value['starttime'] = date('m月d日 H:i',$value['starttime']);
            $pkg_date=json_decode($value['orderDetail']['service_time_json'],true);

            if( isset($pkg_date['type']) && $pkg_date['type']==2 && $value['is_fwb']==1) {

                foreach ($pkg_date['data'] as $k => $v) {
                    $service_time = strtotime($v['day'] . ' ' . $v['time']);
                    if($service_time >= time()- 3600 * 2) {
                        $value['starttime'] = date('m月d日 H:i', $service_time);
                        break;
                    }
                }
            }

            $value['goods_info']=Goods::get($value['orderDetail']['goods_id'] ?? null );

            $value['goods_add2_info']= \app\api\model\service\GoodsAdd::where('id','in',$value['goodsadd2_ids'])->select();
            if(!empty($value['orderDetail']) && $value['orderDetail']['service_time_json']){
                $value['orderDetail']['service_time_json'] = self::generateTimes($value['orderDetail']['service_time_json']);
            }
            $value['invoice'] = Invoice::where(['order_no' => $value->orderId])->find();

        }
        return $list;
    }

    /**
     * 获取服务包时间
     * <AUTHOR>
     * @date 2024/9/24  下午5:54
     * @notes
     */
    public static function generateTimes($json)
    {
        $arr = json_decode($json,true);
        if(!$arr){
            return '';
        }
        $res = '';
        if($arr['type'] == 1){//周
            $time_type = '每周';
        }else{
            $time_type = '每月';
        }
        $time = '';
        foreach ($arr['data'] as $vv){
            $time .= $vv['day'].'、';
        }
        if(isset($arr['data'][0]['time']))
        $res = $time_type.rtrim($time,'、').'  '.$arr['data'][0]['time'];

        return $res;
    }

    public static function searchSettleOrder($params)
    {
        extract($params);
        $order = 'finishtime desc';
        $where['is_settle'] = ['>',0];
        $rebateWhere = ['user_id'=>$user_id,'type'=>$type,'rebatetype'=>['<>',2]];
        if(isset($shop_id))
        {
            $where['shop_id'] = $shop_id;
        }
        if(isset($skill_id) && $skill_id != '')
        {
            $where['skill_id'] = $skill_id;
        }
        if(isset($types))
        {
            $where['shop_id'] = $types == 0?['null','']:['not null',''];
        }
        if(isset($is_settle) && $is_settle != '')
        {
            $where['is_settle'] = $is_settle;
        }
        if(isset($starttime) && isset($endtime))
        {
            $where['finishtime'] = ['between',[$starttime,$endtime]];
            $rebateWhere['createtime'] = ['between',[$starttime,$endtime]];
        }
        $list = self::where($where)->field('id,user_id,skill_id,shop_id,payprice,status,is_settle,is_service,starttime,sumprice,coupon_price,premium_price,add_price,act_travel_price,refund_price,settle_price,total_cost_seconds,memo,orderId,createtime,finishtime')->order($order)->page($page)->limit(10)->select();
        foreach ($list as $key=>$value)
        {
            $list[$key]['orderDetail'] = OrderDetail::where(['order_id'=>$value['id']])->field('goods_id,name,image,sku_name,num,price')->find();
            $list[$key]['rebatePrice'] = Rebate::where(['order_id'=>$value['id'],'type'=>$type,'rebatetype'=>['<>',2]])->value('num');
            $list[$key]['skill'] = $value['skill_id']?Skill::where('id',$value->skill_id)->field('name,image')->find():'';
        }
        $orderIds = self::where($where)->column('id');
        $rebateWhere['order_id'] = ['in',$orderIds];
        $sumPrice = sprintf("%.2f",Rebate::where($rebateWhere)->sum('num'));
        return ['sumPrice'=>$sumPrice,'list'=>$list];
    }

    public static function getShopSkillOrder($params)
    {
        extract($params);
        $order = 'createtime desc';
        if(isset($shop_id) && $shop_id != '')
        {
            $where['shop_id'] = $shop_id;
        }
        if(isset($id) && $id != '')
        {
            $where['skill_id'] = $id;
        }
        if((isset($starttime) && $starttime != '') && (isset($endtime) && $endtime != ''))
        {
            $where['createtime'] = ['between',[$starttime,$endtime]];
        }
        $list = self::where($where)->field('id,user_id,skill_id,shop_id,payprice,status,is_settle,is_service,starttime,sumprice,coupon_price,premium_price,add_price,act_travel_price,refund_price,settle_price,total_cost_seconds,memo,orderId,createtime,finishtime')->order($order)->page($page)->limit(10)->select();
        foreach ($list as $key=>$value)
        {
            $list[$key]['address'] = OrderAddress::where(['order_id'=>$value['id']])->field('name,sex,mobile,province,city,district,address,area,lng,lat')->find();
            $list[$key]['orderDetail'] = OrderDetail::where(['order_id'=>$value['id']])->field('goods_id,name,image,sku_name,num,price')->find();
        }
        return $list;
    }


    public static function getOrderCount($params)
    {
        return self::where($params)->count();
    }
    public static function getOrderPrice($params,$attr)
    {
        return self::where($params)->sum($attr);
    }

    public static function getOrder($params)
    {
        return self::where($params)->field('id,payprice,starttime,status')->with('detail')->order('id desc')->select();
    }

    public static function getSkillOrderCount($params)
    {
        $start= strtotime(date("Y-m-d",time()));
        $endTime = $start+86399;
        for($a = 0;$a <= 2; $a++){
            $params['starttime'] = ['between',[86400*$a+$start,86400*$a+$endTime]];
            $week['date'] = date("Y-m-d",86400*$a+$start);
            $week['count'] = self::where($params)->count();
            $list[] = $week;
        }
        return $list;
    }

    public static function getTotalOrder()
    {
        $daytime = strtotime(date("Y-m-d",time()));
        $start= $daytime-86400*6;
        $end = $daytime;
        $day = [];
        $orderTotal = [];
        $total = ceil(($end-$start)/86400);;
        for($a = 0;$a <=$total; $a++){
            $where['finishtime'] = ['between',[86400*$a+$start,86400*$a+86399+$start]];
            $where['status'] = ['>',5];
            $day[] = date("m-d",86400*$a+$start);
            $orderTotal[] = self::where($where)->count();
        }
        return ['day'=>$day,'total'=>$orderTotal];
    }

    public static function getForm($params)
    {
        $daytime = strtotime(date("Y-m-d",time()));
        extract($params);
        $start= isset($starttime)?$starttime:$daytime-86400*6;
        $end = isset($endtime)?$endtime:$daytime;
        $total = ceil(($end-$start)/86400);
        if(isset($shop_id) && $shop_id != '')
        {
            $where['shop_id'] = $shop_id;
        }
        if(isset($skill_id) && $skill_id == 1)
        {
            $where['skill_id'] = $skill_id;
        }
        $where['status'] = ['>',5];
        $list = [];

        for($a = 0;$a <=$total; $a++){
            $where['finishtime'] = ['between',[86400*$a+$start,86400*$a+$start+86399]];
            $week['date'] = date("Y-m-d",86400*$a+$start);
            $week['price'] = sprintf("%.2f",self::where($where)->sum('price'));
            $week['count'] = self::where($where)->count();
            $list[] = $week;
        }
        return $list;
    }



    /**
     * 获取近3天订单数量
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getDayServiceList($params)
    {
        $daytime = strtotime(date("Y-m-d",time()));
        $list = [];
        for ($a = 0; $a < 3; $a++)
        {
            $starttime = $a>0 ?$a*86400+$daytime:$daytime;
            $endtime = $starttime+86399;
            $params['starttime'] = ['between',[$starttime,$endtime]];
            $day['time'] = $starttime;
            $day['orderCount'] = self::where($params)->count();
            $list[] = $day;
        }
        return $list;
    }

    public static function getCount($params)
    {
        $where['user_id'] = $params['user_id'];
        $where['is_service'] = ['in',[0,-1]];
        $where['status'] = 0;
        $data['unpayCount'] = self::where($where)->count();
        $where['status'] = ['in',[1,2,3,4]];
        $data['unServiceCount'] = self::where($where)->count();
        $where['status'] = 5;
        $data['serviceCount'] = self::where($where)->count();
        $where['status'] = 6;
        $data['notCommentCount'] = self::where($where)->count();
        unset($where['status']);
        $where['is_service'] = 1;
        $data['refundCount'] = self::where($where)->count();
        return $data;
    }


    public static function getOrderInfo($id)
    {
        $where = [
            'id' => $id,
        ];
        $order = self::where($where)->field('*')
            ->order('id desc')->find();

        if(empty($order)) return [];
        // $order['adddetail'] = AddOrder::getOrderDetailList($id);
        $order['goodsadd'] = \app\api\model\service\GoodsAdd::where('id','in',$order['goodsadd_ids'])->select();
        $order['goodsadd2'] = \app\api\model\service\GoodsAdd::where('id','in',$order['goodsadd2_ids'])->select();
        $goodsDetail = OrderDetail::where(['order_id'=>$order['id']])->find();
        $order['detail'] = $goodsDetail;
        if($order['detail']['service_time_json']){
            $order['detail']['service_time_json'] = self::generateTimes($order['detail']['service_time_json']);
        }
        $order['orderAddress'] = $order['to_shop'] == 'door'?OrderAddress::where(['order_id'=>$id])->find():'';
        $order['orderLog'] = OrderLog::where(['order_id'=>$id])->field('id,type,createtime,content')->order('id desc')->select();
        $order['shopInfo'] = $order['shop_id']?Shop::getShop($order['shop_id']):'';
        $order['skillInfo'] = $order['skill_id']?Skill::getOrderSkill($order['skill_id']):'';
        $order['sy_time'] = $order['pay_end_time'] - time();
        $p = MoneyLog::getProfit($id);
        $order['yg_service_money'] = $p['service'] ?? "";
        $order['service_money'] = $p['service'] ?? "";
        $user_area=UserArea::where(['district'=>$order['district']])->find();
        if($user_area){
            $order['qydl_info']=\app\common\model\User::getSimpleUser($user_area['user_id']);
        }else{
            $order['qydl_info']= null;
        }
        return $order;
    }

    public static function getTemplateOrderInfo($id)
    {
        $order = db('service_order')->where('id',$id)->field('skill_id,to_shop,orderId,memo as note,status,starttime as time,payprice as price')->find();
        $order['cate'] = OrderDetail::where('order_id',$id)->value('name');
        $order['address'] = $order['to_shop'] == 'door'?implode('',db('service_order_address')->where(['order_id'=>$id])->field('district,address')->find()):'到店服务';
        $order['status'] = self::getStatus($order['status']);
        $order['name'] = $order['skill_id']?Skill::where('id',$order['skill_id'])->value('name'):'';
        return $order;
    }


    public static function getStatus($status)
    {
        $statusAttr = ['待支付','待接单','待出发','已出发','已到达','开始服务','已完成','已评价'];
        return $status == -1?'已取消':$statusAttr[$status];
    }


    public static function shopAccept($order)
    {
        if(!$order)
        {
            throw new Exception('当前订单无法选取接单,请联系管理员');
        }
        OrderLog::create(['order_id'=>$order['id'],'user_id'=>$order['user_id'],'type'=>15,'content'=>'订单已被商户接取']);
        $update['status'] = 1;
        $update['is_pool'] = 0;
        $update['shop_id'] = $order['shop_id'];
        self::where(['id'=>$order['id']])->update($update);
        Shop::where('id',$order['shop_id'])->setInc('salenums');
        return true;
    }

    public static function createComplaintTemplateParams($params)
    {
        $params['status'] = $params['state'] == 1?'已处理':'已拒绝';
        $templateParams = array_merge($params,['type'=>$params['type'],'templateAttr'=>$params['templateAttr'],'openid'=>$params['openid']]);
        (new \addons\service\library\MiniSms())->sendMessage($templateParams);
        return true;
    }


    public static function createRefundTemplateParams($params)
    {
        $templateOrder = self::refundOrderTemplate($params);
        $templateParams = array_merge($templateOrder,['id'=>$params['id'],'type'=>$params['type'],'templateAttr'=>$params['templateAttr'],'openid'=>$params['openid'],'note'=>$params['note']]);
        (new \addons\service\library\MiniSms())->sendMessage($templateParams);
        return true;
    }

    public static function refundOrderTemplate($params)
    {
        $refundOrder =RefundOrder::where('order_id',$params['id'])->field('id,state')->find();
        $params['cate'] = OrderDetail::where('order_id',$params['id'])->value('name');
        $params['status'] = $refundOrder?$refundOrder['state'] == 1?'审核通过':'审核拒绝':'审核通过';
        $params['time'] = time();
        return $params;
    }


    public static function accept($params)
    {
        $order = self::where(['status'=>1,'id'=>$params['id']])->field('id,user_id,skill_id,is_pool,starttime,actendtime,to_shop')->find();
        if(!$order)
        {
            throw new Exception('当前订单无法选取接单,请联系管理员');
        }
        OrderLog::create(['order_id'=>$order['id'],'user_id'=>$order['user_id'],'type'=>2,'content'=>'订单已被服务者接取']);
        $update['accepttime']= time();
        $update['status'] = 2;
        if($order['is_pool'] == 1)
        {
            $update['is_pool'] = 0;
            $update['skill_id'] = $params['skill_id'];
            $update['shop_id'] = $params['shop_id'];
            $exist = SkillTime::where(['skill_id'=>$params['skill_id'],'state'=>['in',[1,2]],'starttime'=>['between',[$order['starttime'],$order['actendtime']]]])->value('id');
            if($exist)
            {
                throw new Exception('当前项目时间区间有被预约');
            }
            SkillTime::updateSkillTime(['skill_id'=>$params['skill_id'],'starttime'=>$order['starttime'],'actendtime'=>$order['actendtime']],1);
            Skill::where('id',$params['skill_id'])->inc('salenums')->update();
        }
        self::where(['id'=>$order['id']])->update($update);
        $userOpenid = UserInfo::getOpenid($order['user_id'],0);
        self::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'type'=>0,'note'=>'有问题请联系客服','templateAttr'=>'user_notice_template','openid'=>$userOpenid]);
        return true;
    }

    public static function createOrderTemplateParams($order)
    {
        $templateOrder = self::getTemplateOrderInfo($order['id']);
        $templateParams = array_merge($templateOrder,['id'=>$order['id'],'type'=>$order['type'],'templateAttr'=>$order['templateAttr'],'openid'=>$order['openid']]);
        (new \addons\service\library\MiniSms())->sendMessage($templateParams);
        return true;
    }

    public static function go($params)
    {
        $order = self::where(['status'=>2,'id'=>$params['id']])->field('id,user_id,to_shop,skill_id,goods_id')->find();
        if(!$order)
        {
            throw new Exception('当前订单状态异常');
        }
        OrderLog::create(['order_id'=>$params['id'],'user_id'=>$order['user_id'],'type'=>3,'content'=>'服务人员已出发']);
        self::where(['id'=>$params['id']])->update(['reachtime'=>time(),'status'=>3]);
        return true;
    }

    public static function reach($params)
    {
        $order = self::where(['status'=>3,'id'=>$params['id']])->field('id,user_id,to_shop,starttime,goods_id')->find();
        if(!$order) throw new Exception('当前订单状态异常');
//准时分
        $times=(time()-$order['starttime'])/60;
        $sc=ServiceScore::getZdScore($times);
        if($sc > 0){
            Skill::service_score($params['user_id'],-$sc,'服务迟到'.$times.'分',$params['id'],1);
        }
        $goods_name = \app\admin\model\service\Goods::where(['id'=>$order['goods_id']])->value('name');
        \app\api\model\wanlshop\Notice::sendServiceMsg($order['user_id'],'服务通知','您的('.$goods_name.'）订单，服务者已到达签到，请及时确认，如有任何问题或需求，可随时与服务者联系','service',$order['id'],1);
        OrderLog::create(['order_id'=>$params['id'],'user_id'=>$order['user_id'],'type'=>4,'content'=>'服务人员已到达']);
        self::where(['id'=>$params['id']])->update(['reachtime'=>time(),'status'=>4,'reach_images'=> $params['reach_images'] ?? '']);
        return true;
    }

    public static function skillStart($params)
    {
        $order = self::where(['status'=>8,'id'=>$params['id']])->field('id,user_id,to_shop,skill_id')->find();
        if(!$order)
        {
            throw new Exception('当前订单状态异常');
        }
        OrderLog::create(['order_id'=>$params['id'],'user_id'=>$order['user_id'],'type'=>5,'content'=>'服务人员已开始服务']);
        self::where(['id'=>$params['id']])->update(['servicetime'=>time(),'status'=>5,'start_images'=> $params['start_images'] ?? '']);
        // $userOpenid = UserInfo::getOpenid($order['user_id'],0);
        // Order::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'note'=>'有问题请联系客服','type'=>0,'templateAttr'=>'user_order_template','openid'=>$userOpenid]);
        return true;
    }

    public static function userConfirm($params)
    {
        $order = self::where(['status'=>['in',[6,7]],'id'=>$params['id']])->find();

        if(!$order)
        {
            throw new Exception('当前订单状态异常');
        }
        if($order['user_confirm'] == 1){
            throw new Exception('请勿重复确认');
        }

        $goods_name = \app\admin\model\service\Goods::where(['id'=>$order['goods_id']])->value('name');
        \app\api\model\wanlshop\Notice::sendServiceMsg($order['user_id'],'服务通知','您的('.$goods_name.'）订单已完成，请在服务订单中确认。并对服务进行评价，您的反馈将帮助我们不断提升服务质量。如有后续需求，请随时下单，我们将竭诚为您服务！','service',$order['id'],1);
        OrderLog::create(['order_id'=>$params['id'],'user_id'=>$order['user_id'],'type'=>20,'content'=>'用户确认订单']);

        //发放分佣start
        $goods = Goods::where('id',$order['goods_id'])->field('id,shequ_bili,quyu_bili,service_bili')->find();

        if($goods['service_bili'] && $goods['quyu_bili']>0){
            $service_money = truncateDecimal($order['sumprice'] * ($goods['service_bili'] / 100));
        }else{
            $service_money = truncateDecimal($order['sumprice'] * (config('site.fwz_bili') / 100));
        }
        $residue=$order['sumprice']-$service_money-$order['coupon_price'];

        $pt_rate=100;
        if($goods['shequ_bili'] && $goods['shequ_bili']>0){
            $sq_money = truncateDecimal($residue * ($goods['shequ_bili'] / 100));
            $pt_rate-=$goods['shequ_bili'];
        }else{
            $sq_money = truncateDecimal($residue * (config('site.shequ_bili') / 100));
            $pt_rate-=config('site.shequ_bili');
        }
        if($goods['quyu_bili'] && $goods['quyu_bili']>0){
            $qy_money = truncateDecimal($residue * ($goods['quyu_bili'] / 100));
            $pt_rate-=$goods['quyu_bili'];
        }else{
            $qy_money = truncateDecimal($residue * (config('site.quyu_bili') / 100));
            $pt_rate-=config('site.quyu_bili');
        }

        // 计算养老顾问分成 - 基于养老院长分佣金额
        $ylgw_money = truncateDecimal($sq_money * (config('site.gjia_bili') / 100));
        //发放养老院长佣金
        if($sq_money > 0){
            $user = User::where('id',$order['user_id'])->field('id,parent_id,is_sqdl')->find();

            // 如果购买用户自己就是养老院长
            if($user['is_sqdl'] == 1) {
                // 购买用户自己是养老院长，不发放分佣（自己买自己的不分佣）
                // 但保留$sq_money用于计算养老顾问分佣
            } else {
                // 如果购买用户不是养老院长，查找上级养老院长
                if($user['parent_id']){
                    $sq_user = \app\common\model\User::getOneSq($order['user_id']);
                    if(!empty($sq_user)) {
                        \app\common\model\User::money($sq_money, $sq_user['id'], '养老院长佣金', 2, $order['orderId'], 'fenyong');
                        $residue -= $sq_money;
                    }
                }
            }
        }

        //发放城市运营商佣金
        $qy_user_id = \app\common\model\UserArea::where('district',$order['district'])->value('user_id');
        if($qy_user_id && $qy_money > 0){
            \app\common\model\User::money($qy_money,$qy_user_id,'城市运营商佣金',2,$order['orderId'],'fenyong');
            $residue-=$qy_money;
        }

        //发放养老顾问佣金
        if($ylgw_money > 0){
            $ylgw_user = \app\common\model\User::getOneYlgw($order['user_id']);
            if(!empty($ylgw_user)) {
                // 检查找到的养老顾问是否就是购买用户自己，且购买用户已经是养老院长
                $purchase_user = User::where('id',$order['user_id'])->field('id,parent_id,is_sqdl')->find();
                if($ylgw_user['id'] == $order['user_id'] && $purchase_user['is_sqdl'] == 1) {
                    // 如果购买用户自己就是养老顾问且已升级为养老院长，跳过养老顾问分佣
                    // 避免重复分佣，优先保证养老院长分佣
                } else {
                    // 检查是否是推荐分佣（购买用户的直接上级就是养老顾问）
                    $is_referral_commission = ($purchase_user['parent_id'] == $ylgw_user['id']);

                    if($is_referral_commission) {
                        // 推荐分佣：直接发放，不受上级关系影响
                        \app\common\model\User::money($ylgw_money, $ylgw_user['id'], '养老顾问推荐分成', 2, $order['orderId'], 'fenyong');
                        $residue -= $ylgw_money;
                    } else {
                        // 普通分佣：按原有逻辑处理
                        $ylgw_parent = \app\common\model\User::where('id', $ylgw_user['id'])->field('parent_id,is_sqdl,is_qydl')->find();
                        $has_superior = false;

                        if($ylgw_parent && $ylgw_parent['parent_id'] > 0) {
                            // 检查上级是否是养老院长或城市运营商
                            $parent_user = \app\common\model\User::where('id', $ylgw_parent['parent_id'])->field('is_sqdl,is_qydl')->find();
                            if($parent_user && ($parent_user['is_sqdl'] == 1 || $parent_user['is_qydl'] == 1)) {
                                $has_superior = true;
                            }
                        }

                        if(!$has_superior) {
                            // 没有上级，直接发放到账户余额
                            \app\common\model\User::money($ylgw_money, $ylgw_user['id'], '养老顾问佣金', 2, $order['orderId'], 'fenyong');
                            $residue -= $ylgw_money;
                        } else {
                            // 有上级，记录到养老顾问总佣金字段，通过对账系统提现
                            \app\common\model\User::where('id', $ylgw_user['id'])->setInc('ylgw_total_commission', $ylgw_money);
                            // 记录佣金日志
                            \app\common\model\MoneyLog::create([
                                'user_id' => $ylgw_user['id'],
                                'money' => $ylgw_money,
                                'before' => $ylgw_user['ylgw_total_commission'],
                                'after' => $ylgw_user['ylgw_total_commission'] + $ylgw_money,
                                'memo' => '养老顾问佣金-服务订单',
                                'type' => 'fenyong',
                                'createtime' => time()
                            ]);
                        }
                    }
                }
            }
        }

        //放发平台收益
        if($residue>0){
            \app\common\model\User::money($residue,1,'平台收益',2,$order['orderId'],'fenyong');
        }

        //给服务者发放佣金
        if($service_money > 0){
            $sk=Skill::get($order['skill_id']);
            \app\common\model\User::money($service_money,$sk['user_id'],'服务者获得佣金',2,$order['orderId'],'fenyong');
        }

        self::where(['id'=>$params['id']])->update(['user_confirm_time'=>time(),'shequ_money'=>$sq_money,'quyu_money'=>$qy_money,'service_money'=>$service_money,'user_confirm'=>1]);
        //发放分佣end

        // $userOpenid = UserInfo::getOpenid($order['user_id'],0);
        // Order::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'note'=>'有问题请联系客服','type'=>0,'templateAttr'=>'user_order_template','openid'=>$userOpenid]);
        return true;
    }



    public static function skillFinish($params)
    {
        $order = self::where(['status'=>['in',[2,5]],'id'=>$params['id']])->field('id,to_shop,user_id,skill_id,shop_id,goods_total_price,shop_id,payprice,travel_price,price,sumprice,premium_price,coupon_price,add_price')->find();
        if(!$order)
        {
            throw new Exception('当前订单状态异常，无法确认完成');
        }
        //更新订单，进行订单待结算
        self::where(['id'=>$params['id']])->update(['status'=>6,'finish_images'=>isset($params['finish_images'])?$params['finish_images']:'','finishtime'=>time()]);

        self::orderFinish($order);
        $userOpenid = UserInfo::getOpenid($order['user_id'],0);
        self::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'type'=>0,'note'=>'有问题请联系客服','templateAttr'=>'order_finish_template','openid'=>$userOpenid]);
        if($order['shop_id'])
        {
            $shopOpenid = UserInfo::getOpenid($order['shop_id'],2);
            self::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['user_id'],'to_shop'=>$order['to_shop'],'type'=>2,'templateAttr'=>'shop_finish_template','openid'=>$shopOpenid]);
        }
        return true;
    }

    public static function orderFinish($order)
    {
        OrderLog::create(['order_id'=>$order['id'],'user_id'=>$order['user_id'],'type'=>6,'content'=>'服务人员已完成服务']);
        $settleDay = $order['shop_id']?Shop::getSettleDay($order['shop_id']):ProjectConfig::getProjectConfig('settle_day');
        $settletime = $settleDay*86400+time();
        $totalPrice = bcadd(($order['goods_total_price']+$order['premium_price']),$order['add_price'],2);
        $settlePrice =$order['coupon_price'] > 0 ?bcsub($totalPrice,$order['coupon_price'],2): $totalPrice;
        $update = ['is_settle'=>1,'settletime'=>$settletime,'settle_price'=>$settlePrice];
        if($order['skill_id'])
        {
            $update['skill_percent'] = Skill::where('id',$order['skill_id'])->value('percent');
        }
        if($order['shop_id'])
        {
            $update['shop_percent'] = Shop::where('id',$order['shop_id'])->value('percent');
        }
        self::where(['id'=>$order['id']])->update($update);
        return true;
    }

    public static function checkOrder($order)
    {
        if(!$order)
        {
            throw new Exception('订单信息异常,无法核销');
        }
        self::where(['id'=>$order['id']])->update(['status'=>6,'finishtime'=>time()]);
        self::orderFinish($order);
        $shopOpenid = UserInfo::getOpenid($order['shop_id'],2);
        self::createOrderTemplateParams(['id'=>$order['id'],'user_id'=>$order['id'],'to_shop'=>$order['to_shop'],'type'=>2,'templateAttr'=>'shop_order_template','openid'=>$shopOpenid]);
        return true;
    }

    public function adddetail()
    {
        return $this->hasMany('AddOrderDetail','order_id');
    }

    public function detail()
    {
        return $this->hasMany('OrderDetail','order_id');
    }

}
