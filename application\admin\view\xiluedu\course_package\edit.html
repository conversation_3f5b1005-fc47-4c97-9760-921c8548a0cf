<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">课程名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input class="form-control" type="text" value="{$course.name}" readonly>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">套餐名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">套餐描述:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-description" class="form-control" name="row[description]" rows="3">{$row.description|htmlentities}</textarea>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">套餐数量:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-quantity" data-rule="required;integer;min(1)" class="form-control" name="row[quantity]" type="number" value="{$row.quantity}" min="1">
            <span class="help-block"><strong>注意：</strong>套餐数量表示购买此套餐可获得的养老顾问开通名额数量，价格是固定的，不按数量计算</span>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">原价:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-original_price" data-rule="required;number;min(0)" class="form-control" name="row[original_price]" type="number" step="0.01" min="0" value="{$row.original_price}">
            <span class="help-block">普通用户购买价格</span>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">城市运营商价格:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-qydl_price" data-rule="number;min(0)" class="form-control" name="row[qydl_price]" type="number" step="0.01" min="0" value="{$row.qydl_price}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">养老院长价格:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sqdl_price" data-rule="number;min(0)" class="form-control" name="row[sqdl_price]" type="number" step="0.01" min="0" value="{$row.sqdl_price}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">养老顾问价格:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ylgw_price" data-rule="number;min(0)" class="form-control" name="row[ylgw_price]" type="number" step="0.01" min="0" value="{$row.ylgw_price}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">适用人员:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="checkbox">
                <label><input name="row[user_types][]" type="checkbox" value="qydl" {in name="'qydl'" value="$row.user_types"}checked{/in}> 城市运营商</label>
            </div>
            <div class="checkbox">
                <label><input name="row[user_types][]" type="checkbox" value="sqdl" {in name="'sqdl'" value="$row.user_types"}checked{/in}> 养老院长</label>
            </div>
            <div class="checkbox">
                <label><input name="row[user_types][]" type="checkbox" value="ylgw" {in name="'ylgw'" value="$row.user_types"}checked{/in}> 养老顾问</label>
            </div>
            <div class="checkbox">
                <label><input name="row[user_types][]" type="checkbox" value="user" {in name="'user'" value="$row.user_types"}checked{/in}> 普通用户</label>
            </div>
            <span class="help-block">选择哪些用户类型可以购买此套餐</span>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">排序:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" class="form-control" name="row[sort]" type="number" value="{$row.sort}">
            <span class="help-block">数字越小越靠前</span>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">状态:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input name="row[status]" type="radio" value="1" {in name="1" value="$row.status"}checked{/in}> 启用</label>
            </div>
            <div class="radio">
                <label><input name="row[status]" type="radio" value="0" {in name="0" value="$row.status"}checked{/in}> 禁用</label>
            </div>
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
