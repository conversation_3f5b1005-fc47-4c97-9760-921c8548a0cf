<?php
namespace app\api\controller\service;

use app\common\controller\Api;
use app\common\model\User;
use app\api\model\service\Order as ServiceOrder;
use app\api\model\wanlshop\Order as WanlshopOrder;
use app\api\model\wanlshop\OrderGoods;
use app\common\model\xiluedu\OfflineOrder;
use app\common\model\xiluedu\CourseOrder;
use app\common\model\xiluedu\OfflineCourse;
use app\common\model\xiluedu\Course;
use app\api\model\service\Goods as ServiceGoods;
use app\api\model\wanlshop\Goods as WanlshopGoods;

/**
 * 分佣测试接口
 */
class Commission extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * 测试分佣计算
     * @ApiTitle (测试分佣计算)
     * @ApiRoute (/api/service.commission/test_calculate)
     * @ApiMethod (POST)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="order_no", type="string", required=true, description="订单号")
     * @ApiParams (name="user_id", type="int", required=true, description="用户ID")
     * @ApiParams (name="order_type", type="string", required=true, description="订单类型：service=服务订单，wanlshop=商城订单，offline_course=线下课程，online_course=线上课程")
     */
    public function test_calculate()
    {
        $order_no = $this->request->post('order_no');
        $user_id = $this->request->post('user_id');
        $order_type = $this->request->post('order_type');

        if (!$order_no) {
            $this->error('订单号不能为空');
        }
        if (!$user_id) {
            $this->error('用户ID不能为空');
        }
        if (!$order_type) {
            $this->error('订单类型不能为空');
        }

        // 验证用户是否存在
        $user = User::where('id', $user_id)->find();
        if (!$user) {
            $this->error('用户不存在');
        }

        $result = [];
        
        try {
            switch ($order_type) {
                case 'service':
                    $result = $this->calculateServiceCommission($order_no, $user_id);
                    break;
                case 'wanlshop':
                    $result = $this->calculateWanlshopCommission($order_no, $user_id);
                    break;
                case 'offline_course':
                    $result = $this->calculateOfflineCourseCommission($order_no, $user_id);
                    break;
                case 'online_course':
                    $result = $this->calculateOnlineCourseCommission($order_no, $user_id);
                    break;
                default:
                    $this->error('不支持的订单类型');
            }
        } catch (\Exception $e) {
            $this->error('计算失败：' . $e->getMessage());
        }

        $this->success('计算成功', $result);
    }

    /**
     * 计算服务订单分佣
     */
    private function calculateServiceCommission($order_no, $user_id)
    {
        // 查找订单
        $order = ServiceOrder::where('orderId', $order_no)->find();
        if (!$order) {
            throw new \Exception('服务订单不存在');
        }

        // 获取商品信息
        $goods = ServiceGoods::where('id', $order['goods_id'])->field('id,shequ_bili,quyu_bili,service_bili')->find();
        if (!$goods) {
            throw new \Exception('商品信息不存在');
        }

        // 计算分佣
        $service_money = 0;
        if ($goods['service_bili'] && $goods['service_bili'] > 0) {
            $service_money = truncateDecimal($order['sumprice'] * ($goods['service_bili'] / 100));
        } else {
            $service_money = truncateDecimal($order['sumprice'] * (config('site.fwz_bili') / 100));
        }

        $residue = $order['sumprice'] - $service_money - $order['coupon_price'];

        // 养老院长分成
        $sq_money = 0;
        if ($goods['shequ_bili'] && $goods['shequ_bili'] > 0) {
            $sq_money = truncateDecimal($residue * ($goods['shequ_bili'] / 100));
        } else {
            $sq_money = truncateDecimal($residue * (config('site.shequ_bili') / 100));
        }

        // 城市运营商分成
        $qy_money = 0;
        if ($goods['quyu_bili'] && $goods['quyu_bili'] > 0) {
            $qy_money = truncateDecimal($residue * ($goods['quyu_bili'] / 100));
        } else {
            $qy_money = truncateDecimal($residue * (config('site.quyu_bili') / 100));
        }

        // 养老顾问分成
        $ylgw_money = truncateDecimal($sq_money * (config('site.gjia_bili') / 100));

        // 判断用户身份和关系
        $user_relations = $this->getUserRelations($user_id);
        $user_commission = $this->calculateUserCommission($user_id, $sq_money, $qy_money, $ylgw_money);

        return [
            'order_info' => [
                'order_no' => $order_no,
                'order_id' => $order['id'],
                'total_price' => $order['sumprice'],
                'coupon_price' => $order['coupon_price'],
                'goods_name' => $goods['name'] ?? '未知商品'
            ],
            'commission_breakdown' => [
                'service_money' => $service_money,
                'residue' => $residue,
                'sq_money' => $sq_money,
                'qy_money' => $qy_money,
                'ylgw_money' => $ylgw_money
            ],
            'user_info' => $user_relations,
            'user_commission' => $user_commission
        ];
    }

    /**
     * 计算商城订单分佣
     */
    private function calculateWanlshopCommission($order_no, $user_id)
    {
        // 查找订单
        $order = WanlshopOrder::where('order_no', $order_no)->find();
        if (!$order) {
            throw new \Exception('商城订单不存在');
        }

        // 获取订单商品
        $order_goods = OrderGoods::where('order_id', $order['id'])->select();
        if (!$order_goods) {
            throw new \Exception('订单商品不存在');
        }

        $total_commission = [
            'sq_money' => 0,
            'qy_money' => 0,
            'ylgw_money' => 0
        ];

        $goods_details = [];

        foreach ($order_goods as $goods) {
            // 获取商品信息
            $wanlshop_goods = WanlshopGoods::where('id', $goods['goods_id'])->find();
            
            // 计算利润（养老顾问价格 - 养老院长优惠价格）
            $ylgw_price = $goods['price']; // 养老顾问支付的价格
            $sq_price = $wanlshop_goods['shequ_price'] ?? $goods['price']; // 养老院长优惠价格
            $lirun = $ylgw_price - $sq_price;

            if ($lirun > 0) {
                // 养老院长分成
                $sq_money = truncateDecimal($lirun * (config('site.goods_shequ_bili') / 100));
                // 城市运营商分成
                $qy_money = truncateDecimal($lirun * (config('site.goods_quyu_bili') / 100));
                // 养老顾问分成 = 利润 × 50%
                $ylgw_money = truncateDecimal($lirun * (config('site.gjia_bili') / 100));

                $total_commission['sq_money'] += $sq_money;
                $total_commission['qy_money'] += $qy_money;
                $total_commission['ylgw_money'] += $ylgw_money;

                $goods_details[] = [
                    'goods_name' => $goods['title'],
                    'ylgw_price' => $ylgw_price,
                    'sq_price' => $sq_price,
                    'profit' => $lirun,
                    'sq_money' => $sq_money,
                    'qy_money' => $qy_money,
                    'ylgw_money' => $ylgw_money
                ];
            }
        }

        // 判断用户身份和关系
        $user_relations = $this->getUserRelations($user_id);
        $user_commission = $this->calculateUserCommission($user_id, $total_commission['sq_money'], $total_commission['qy_money'], $total_commission['ylgw_money']);

        return [
            'order_info' => [
                'order_no' => $order_no,
                'order_id' => $order['id'],
                'total_price' => $order['order_price'],
                'goods_count' => count($order_goods)
            ],
            'goods_details' => $goods_details,
            'commission_breakdown' => $total_commission,
            'user_info' => $user_relations,
            'user_commission' => $user_commission
        ];
    }

    /**
     * 计算线下课程分佣
     */
    private function calculateOfflineCourseCommission($order_no, $user_id)
    {
        // 查找订单
        $order = OfflineOrder::where('order_no', $order_no)->find();
        if (!$order) {
            throw new \Exception('线下课程订单不存在');
        }

        // 获取课程信息
        $course = OfflineCourse::where('id', $order['course_id'])->field('id,name,shequ_bili,quyu_bili,service_bili')->find();
        if (!$course) {
            throw new \Exception('课程信息不存在');
        }

        // 计算分佣
        $service_money = 0;
        if ($course['service_bili']) {
            $service_money = truncateDecimal($order['pay_price'] * ($course['service_bili'] / 100));
        } else {
            $service_money = truncateDecimal($order['pay_price'] * (config('site.course_teacher_rate') / 100));
        }

        $residue = $order['pay_price'] - $service_money;

        // 对于课程10，特殊处理
        if ($order['course_id'] == 10) {
            $sq_money = truncateDecimal($order['pay_price'] * 0.5); // 3400元
            $ylgw_money = truncateDecimal($sq_money * 0.5); // 1700元
        } else {
            // 其他课程按原有逻辑
            if ($course['shequ_bili'] && $course['shequ_bili'] > 0) {
                $sq_money = truncateDecimal($residue * ($course['shequ_bili'] / 100));
            } else {
                $sq_money = truncateDecimal($residue * (config('site.course_shequ_rate') / 100));
            }
            $ylgw_money = truncateDecimal($sq_money * (config('site.gjia_bili') / 100));
        }

        // 城市运营商分成
        $qy_money = 0;
        if ($course['quyu_bili'] && $course['quyu_bili'] > 0) {
            $qy_money = truncateDecimal($residue * ($course['quyu_bili'] / 100));
        } else {
            $qy_money = truncateDecimal($residue * (config('site.course_quyu_rate') / 100));
        }

        // 判断用户身份和关系
        $user_relations = $this->getUserRelations($user_id);
        $user_commission = $this->calculateUserCommission($user_id, $sq_money, $qy_money, $ylgw_money);

        return [
            'order_info' => [
                'order_no' => $order_no,
                'order_id' => $order['id'],
                'course_id' => $order['course_id'],
                'course_name' => $course['name'],
                'pay_price' => $order['pay_price'],
                'is_course_10' => $order['course_id'] == 10
            ],
            'commission_breakdown' => [
                'service_money' => $service_money,
                'residue' => $residue,
                'sq_money' => $sq_money,
                'qy_money' => $qy_money,
                'ylgw_money' => $ylgw_money
            ],
            'user_info' => $user_relations,
            'user_commission' => $user_commission
        ];
    }

    /**
     * 计算线上课程分佣
     */
    private function calculateOnlineCourseCommission($order_no, $user_id)
    {
        // 查找订单
        $order = CourseOrder::where('order_no', $order_no)->find();
        if (!$order) {
            throw new \Exception('线上课程订单不存在');
        }

        // 获取课程信息
        $course = Course::where('id', $order['course_id'])->field('id,name,shequ_bili,quyu_bili,service_bili')->find();
        if (!$course) {
            throw new \Exception('课程信息不存在');
        }

        // 计算分佣逻辑与线下课程相同
        $service_money = 0;
        if ($course['service_bili'] != 0) {
            $service_money = truncateDecimal($order['total_price'] * ($course['service_bili'] / 100));
        } else {
            $service_money = truncateDecimal($order['total_price'] * (config('site.course_teacher_rate') / 100));
        }

        $residue = $order['total_price'] - $service_money - $order['favourable_price'];

        // 对于课程10，特殊处理
        if ($order['course_id'] == 10) {
            $sq_money = truncateDecimal($order['total_price'] * 0.5);
            $ylgw_money = truncateDecimal($sq_money * 0.5);
        } else {
            if ($course['shequ_bili'] && $course['shequ_bili'] > 0) {
                $sq_money = truncateDecimal($residue * ($course['shequ_bili'] / 100));
            } else {
                $sq_money = truncateDecimal($residue * (config('site.course_shequ_rate') / 100));
            }
            $ylgw_money = truncateDecimal($sq_money * (config('site.gjia_bili') / 100));
        }

        // 城市运营商分成
        $qy_money = 0;
        if ($course['quyu_bili'] && $course['quyu_bili'] > 0) {
            $qy_money = truncateDecimal($residue * ($course['quyu_bili'] / 100));
        } else {
            $qy_money = truncateDecimal($residue * (config('site.course_quyu_rate') / 100));
        }

        // 判断用户身份和关系
        $user_relations = $this->getUserRelations($user_id);
        $user_commission = $this->calculateUserCommission($user_id, $sq_money, $qy_money, $ylgw_money);

        return [
            'order_info' => [
                'order_no' => $order_no,
                'order_id' => $order['id'],
                'course_id' => $order['course_id'],
                'course_name' => $course['name'],
                'total_price' => $order['total_price'],
                'favourable_price' => $order['favourable_price'],
                'is_course_10' => $order['course_id'] == 10
            ],
            'commission_breakdown' => [
                'service_money' => $service_money,
                'residue' => $residue,
                'sq_money' => $sq_money,
                'qy_money' => $qy_money,
                'ylgw_money' => $ylgw_money
            ],
            'user_info' => $user_relations,
            'user_commission' => $user_commission
        ];
    }

    /**
     * 获取用户关系信息
     */
    private function getUserRelations($user_id)
    {
        $user = User::where('id', $user_id)->field('id,nickname,parent_id,is_sqdl,is_qydl,is_ylgw')->find();
        
        $sq_user = User::getOneSq($user_id);
        $qy_user = User::getOneQy($user_id);
        $ylgw_user = User::getOneYlgw($user_id);

        return [
            'user' => $user,
            'sq_user' => $sq_user,
            'qy_user' => $qy_user,
            'ylgw_user' => $ylgw_user
        ];
    }

    /**
     * 计算指定用户的佣金
     */
    private function calculateUserCommission($user_id, $sq_money, $qy_money, $ylgw_money)
    {
        $user = User::where('id', $user_id)->field('id,nickname,is_sqdl,is_qydl,is_ylgw')->find();
        
        $commission = 0;
        $commission_type = '';

        if ($user['is_qydl'] == 1) {
            // 城市运营商
            $commission = $qy_money;
            $commission_type = '城市运营商分成';
        } elseif ($user['is_sqdl'] == 1) {
            // 养老院长
            $commission = $sq_money;
            $commission_type = '养老院长分成';
        } elseif ($user['is_ylgw'] == 1) {
            // 养老顾问
            $commission = $ylgw_money;
            $commission_type = '养老顾问分成';
        } else {
            $commission_type = '普通用户（无分成）';
        }

        return [
            'user_id' => $user_id,
            'nickname' => $user['nickname'],
            'user_type' => $commission_type,
            'commission' => $commission
        ];
    }
}
