<?php
namespace app\api\controller\service;

use app\common\controller\Api;
use app\common\model\service\BmTc;
use app\common\model\User;
use fast\Random;
use think\Db;
use think\Exception;
use app\common\model\Qun as QunModel;

/**
 * 首页接口
 */
class Qun extends Api
{
    protected $noNeedLogin = [''];
    protected $noNeedRight = ['*'];


    /**
     * 设置企业群
     * <AUTHOR>
     * @date 2024/9/4  下午4:40
     * @notes
     */
    public function setQun()
    {
        $data = $this->request->post();
        if(isset($data['id']) && $data['id']){
            $qun = QunModel::get($data['id']);
            if(!$qun){
                $this->error('群不存在');
            }
            $data['updatetime'] = time();
            $res = QunModel::update($data);
        }else{
            $user =$this->auth->getUserinfo();
            $user_id2 =$user['qydl_staff']!=0 ? $user['qydl_staff'] :$this->auth->id;
            $user_area = Db::name('user_area')->where('user_id', $user_id2)->find();
            if(!$user_area){
                $this->error('您不是城市运营商');
            }
            $data['userarea_id'] = $user_area['id'];
            $data['createtime'] = time();
            $res = QunModel::create($data);
        }
        if($res){
            $this->success('操作成功');
        }else{
            $this->error('操作失败');
        }

    }

    /**
     * 获取企业群列表
     * <AUTHOR>
     * @date 2024/9/4  下午5:37
     * @notes
     */
    public function getQunList()
    {
        $user =$this->auth->getUserinfo();
        $user_id2 =$user['qydl_staff']!=0 ? $user['qydl_staff'] :$this->auth->id;
        $user_area = Db::name('user_area')->where('user_id', $user_id2)->find();
        if(!$user_area){
            $this->error('您不是城市运营商');
        }
        $list = QunModel::where('userarea_id', $user_area['id'])->select();
        $this->success('获取成功', $list);
    }

    /**
     * 删除群消息
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2024/9/4  下午5:38
     * @notes
     */
    public function deleteQun()
    {
        $id = $this->request->post('id');
        if(!$id){
            $this->error('参数错误');
        }
        $qun = QunModel::get($id);
        if(!$qun){
            $this->error('群不存在');
        }
        $res = QunModel::destroy($id);
        if($res){
            $this->success('删除成功');
        }else{
            $this->error('删除失败');
        }
    }

    public function getAreaQun()
    {
        $city=$this->request->param('city');
        $district=$this->request->param('district');
        $list = QunModel::where(['city'=>$city,'district'=>$district])->select();
        $this->success('获取成功', $list);
    }


}
