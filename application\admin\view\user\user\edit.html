<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id}">
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Group')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            {$groupList}-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label for="c-username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text" value="{$row.username|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('所属上级')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-parent_id" data-source="user/user/index?no_service=1"  data-format-item="<img class='img-sm img-center img-circle' src='{avatar}'> {nickname} - {mobile}" data-field="mobile" class="form-control selectpage" name="row[parent_id]" type="text" value="{$row.parent_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-password" data-rule="password" class="form-control" name="row[password]" type="password" value="" placeholder="{:__('Leave password blank if dont want to change')}" autocomplete="new-password" />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('养老院长')}:</label>
        <div class="col-xs-12 col-sm-4">

            <select  id="c-is_sqdl" data-rule="required" class="form-control selectpicker" name="row[is_sqdl]">
                {foreach name="sqdlList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.is_sqdl"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('城市运营商')}:</label>
        <div class="col-xs-12 col-sm-4">

            <select  id="c-is_qydl" data-rule="required" class="form-control selectpicker" name="row[is_qydl]">
                {foreach name="qydlList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.is_qydl"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('养老顾问')}:</label>
        <div class="col-xs-12 col-sm-4">

            <select  id="c-is_ylgw" data-rule="required" class="form-control selectpicker" name="row[is_ylgw]">
                {foreach name="ylgwList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.is_ylgw"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    {if condition="$canManageQuota"}
    <div class="form-group" id="ylgw-quota-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('养老顾问开通额度')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-ylgw_quota" data-rule="integer" class="form-control" name="row[ylgw_quota]" type="number" min="0" value="{$row.ylgw_quota|default=0}" placeholder="请输入开通额度">
            <span class="help-block">只有城市运营商和养老院长才能拥有此额度</span>
        </div>
    </div>
    {/if}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('区域')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-addr" data-rule="" class="form-control" data-toggle="city-picker" name="row[addr]" type="text" value="{$row.addr|htmlentities}"></div>
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label for="c-email" class="control-label col-xs-12 col-sm-2">{:__('Email')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-email" data-rule="email" class="form-control" name="row[email]" type="text" value="{$row.email|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-mobile" data-rule="mobile" class="form-control" name="row[mobile]" type="text" value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-avatar" class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="" class="form-control" size="50" name="row[avatar]" type="text" value="{$row.avatar}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label for="c-level" class="control-label col-xs-12 col-sm-2">{:__('Level')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-level" data-rule="required" class="form-control" name="row[level]" type="number" value="{$row.level}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[gender]', ['1'=>__('Male'), '0'=>__('Female')], $row['gender'])}
        </div>
    </div>
    <div class="form-group">
        <label for="c-birthday" class="control-label col-xs-12 col-sm-2">{:__('Birthday')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-birthday" data-rule="" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[birthday]" type="text" value="{$row.birthday}">
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label for="c-bio" class="control-label col-xs-12 col-sm-2">{:__('Bio')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-bio" data-rule="" class="form-control" name="row[bio]" type="text" value="{$row.bio|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label for="c-money" class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-money" data-rule="required" class="form-control" name="row[money]" type="number" value="{$row.money}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-card_no" class="control-label col-xs-12 col-sm-2">{:__('身份证号')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-card_no" data-rule="" class="form-control" name="row[card_no]" type="text" value="{$row.card_no|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-realname" class="control-label col-xs-12 col-sm-2">{:__('真实姓名')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-realname" data-rule="" class="form-control" name="row[realname]" type="text" value="{$row.realname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-ylgw_number_no" class="control-label col-xs-12 col-sm-2">{:__('编号')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-ylgw_number_no" data-rule="" class="form-control" name="row[ylgw_number_no]" type="text" value="{$row.ylgw_number_no|htmlentities}">
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label for="c-score" class="control-label col-xs-12 col-sm-2">{:__('Score')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-score" data-rule="required" class="form-control" name="row[score]" type="number" value="{$row.score}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label for="c-successions" class="control-label col-xs-12 col-sm-2">{:__('Successions')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-successions" data-rule="required" class="form-control" name="row[successions]" type="number" value="{$row.successions}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label for="c-maxsuccessions" class="control-label col-xs-12 col-sm-2">{:__('Maxsuccessions')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-maxsuccessions" data-rule="required" class="form-control" name="row[maxsuccessions]" type="number" value="{$row.maxsuccessions}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label for="c-prevtime" class="control-label col-xs-12 col-sm-2">{:__('Prevtime')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-prevtime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[prevtime]" type="text" value="{$row.prevtime|datetime}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label for="c-logintime" class="control-label col-xs-12 col-sm-2">{:__('Logintime')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-logintime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[logintime]" type="text" value="{$row.logintime|datetime}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label for="c-loginip" class="control-label col-xs-12 col-sm-2">{:__('Loginip')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-loginip" data-rule="required" class="form-control" name="row[loginip]" type="text" value="{$row.loginip}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label for="c-loginfailure" class="control-label col-xs-12 col-sm-2">{:__('Loginfailure')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-loginfailure" data-rule="required" class="form-control" name="row[loginfailure]" type="number" value="{$row.loginfailure}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label for="c-joinip" class="control-label col-xs-12 col-sm-2">{:__('Joinip')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-joinip" data-rule="required" class="form-control" name="row[joinip]" type="text" value="{$row.joinip}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label for="c-jointime" class="control-label col-xs-12 col-sm-2">{:__('Jointime')}:</label>-->
<!--        <div class="col-xs-12 col-sm-4">-->
<!--            <input id="c-jointime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[jointime]" type="text" value="{$row.jointime|datetime}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label for="c-ylgw_xy" class="control-label col-xs-12 col-sm-2">{:__('协议')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-ylgw_xy" data-rule="" class="form-control" size="50" name="row[ylgw_xy]" type="text" value="{$row.ylgw_xy}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-ylgw_xy" class="btn btn-danger faupload" data-input-id="c-ylgw_xy" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-ylgw_xy"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-ylgw_xy" class="btn btn-primary fachoose" data-input-id="c-ylgw_xy" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-ylgw_xy"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-ylgw_xy"></ul>
        </div>
    </div>
    <div class="form-group">
        <label for="c-avatar" class="control-label col-xs-12 col-sm-2">{:__('证书')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-ylgw_zs" data-rule="" class="form-control" size="50" name="row[ylgw_zs]" type="text" value="{$row.ylgw_zs}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-ylgw_zs" class="btn btn-danger faupload" data-input-id="c-ylgw_zs" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-ylgw_zs"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-ylgw_zs" class="btn btn-primary fachoose" data-input-id="c-ylgw_zs" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-ylgw_zs"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-ylgw_zs"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    // 监听身份选择变化，控制养老顾问开通额度字段的显示
    function toggleQuotaField() {
        var isSqdl = $('#c-is_sqdl').val(); // 养老院长
        var isQydl = $('#c-is_qydl').val(); // 城市运营商
        var quotaGroup = $('#ylgw-quota-group');

        if (quotaGroup.length > 0) {
            // 只有当用户是城市运营商(1)或养老院长(1)时才显示额度字段
            if (isSqdl == '1' || isQydl == '1') {
                quotaGroup.show();
            } else {
                quotaGroup.hide();
                // 隐藏时清空值
                $('#c-ylgw_quota').val('0');
            }
        }
    }

    // 页面加载时检查一次
    toggleQuotaField();

    // 监听身份选择变化
    $('#c-is_sqdl, #c-is_qydl').on('change', function() {
        toggleQuotaField();
    });
});
</script>
