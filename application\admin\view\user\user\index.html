<div class="panel panel-default panel-intro">
    {:build_heading()}
<!--    <div class="panel-heading">-->
<!--        {:build_heading(null,FALSE)}-->
<!--        <ul class="nav nav-tabs" data-field="is_shop">-->
<!--            <li class="{:$Think.get.is_shop === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>-->
<!--            {foreach name="shopTypeList" item="vo"}-->
<!--            <li class="{:$Think.get.is_shop === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>-->
<!--            {/foreach}-->
<!--        </ul>-->
<!--    </div>-->
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,edit,del')}
                        <div class="dropdown btn-group {:$auth->check('user/user/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('user/user/edit')}"
                           data-operate-del="{:$auth->check('user/user/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>


<script id="annualtpl" type="text/html">
    <div class="row">
        <div class="col-xs-12 col-sm-8">

            <select  id="c-level" data-rule="" class="form-control selectpicker" name="level">
                <option value="" >全部</option>
                <option value="1" >养老院长</option>
                <option value="2" >城市运营商</option>
                <option value="4" >养老顾问</option>
                <option value="3" >普通用户</option>
            </select>
            <input type="hidden" class="operate" data-name="level" value="=" />
        </div>
    </div>
</script>
