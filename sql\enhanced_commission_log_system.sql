-- 增强版分佣日志系统
-- 在现有基础上增加余额支付和详细计算过程记录

-- 1. 增强分佣日志表结构
ALTER TABLE `fa_commission_log` 
ADD COLUMN `payment_method` varchar(20) NOT NULL DEFAULT 'wechat' COMMENT '支付方式(wechat,alipay,balance,mixed)' AFTER `order_amount`,
ADD COLUMN `balance_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额支付金额' AFTER `payment_method`,
ADD COLUMN `online_payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '线上支付金额' AFTER `balance_amount`,
ADD COLUMN `calculation_details` json COMMENT '详细计算过程JSON' AFTER `calculation_rule`,
ADD COLUMN `before_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣前用户余额' AFTER `money_log_id`,
ADD COLUMN `after_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣后用户余额' AFTER `before_balance`,
ADD COLUMN `platform_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '平台利润' AFTER `commission_amount`,
ADD COLUMN `total_distributed` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总分佣金额' AFTER `platform_profit`;

-- 2. 创建分佣计算详情记录函数
DELIMITER $$
CREATE FUNCTION `calculate_commission_details`(
    order_amount DECIMAL(10,2),
    teacher_rate DECIMAL(5,2),
    sq_rate DECIMAL(5,2),
    qy_rate DECIMAL(5,2),
    ylgw_rate DECIMAL(5,2)
) RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result JSON;
    DECLARE teacher_money DECIMAL(10,2);
    DECLARE residue DECIMAL(10,2);
    DECLARE sq_money DECIMAL(10,2);
    DECLARE qy_money DECIMAL(10,2);
    DECLARE ylgw_money DECIMAL(10,2);
    DECLARE platform_money DECIMAL(10,2);
    
    -- 计算老师分佣
    SET teacher_money = TRUNCATE(order_amount * (teacher_rate / 100), 2);
    SET residue = order_amount - teacher_money;
    
    -- 计算养老院长分佣
    SET sq_money = TRUNCATE(residue * (sq_rate / 100), 2);
    
    -- 计算城市运营商分佣
    SET qy_money = TRUNCATE(residue * (qy_rate / 100), 2);
    
    -- 计算养老顾问分佣（基于养老院长分佣的50%）
    SET ylgw_money = TRUNCATE(sq_money * (ylgw_rate / 100), 2);
    
    -- 计算平台利润
    SET platform_money = residue - sq_money - qy_money;
    
    SET result = JSON_OBJECT(
        'order_amount', order_amount,
        'teacher_commission', JSON_OBJECT(
            'rate', teacher_rate,
            'amount', teacher_money,
            'calculation', CONCAT(order_amount, ' × ', teacher_rate, '% = ', teacher_money)
        ),
        'residue_after_teacher', residue,
        'sq_commission', JSON_OBJECT(
            'rate', sq_rate,
            'amount', sq_money,
            'calculation', CONCAT(residue, ' × ', sq_rate, '% = ', sq_money)
        ),
        'qy_commission', JSON_OBJECT(
            'rate', qy_rate,
            'amount', qy_money,
            'calculation', CONCAT(residue, ' × ', qy_rate, '% = ', qy_money)
        ),
        'ylgw_commission', JSON_OBJECT(
            'rate', ylgw_rate,
            'amount', ylgw_money,
            'calculation', CONCAT(sq_money, ' × ', ylgw_rate, '% = ', ylgw_money),
            'note', '基于养老院长分佣金额计算'
        ),
        'platform_profit', platform_money,
        'total_distributed', teacher_money + sq_money + qy_money + ylgw_money
    );
    
    RETURN result;
END$$
DELIMITER ;

-- 3. 创建余额支付记录表（如果不存在）
CREATE TABLE IF NOT EXISTS `fa_balance_payment_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_type` varchar(20) NOT NULL DEFAULT '' COMMENT '订单类型',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `balance_before` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付前余额',
  `balance_used` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '使用余额',
  `balance_after` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付后余额',
  `online_payment` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '线上支付金额',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `payment_method` varchar(20) NOT NULL DEFAULT '' COMMENT '线上支付方式',
  `memo` varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order` (`order_type`, `order_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额支付记录表';

-- 4. 创建分佣日志记录存储过程
DELIMITER $$
CREATE PROCEDURE `record_commission_log`(
    IN p_batch_no VARCHAR(32),
    IN p_step_no TINYINT,
    IN p_order_type VARCHAR(20),
    IN p_order_id INT,
    IN p_order_no VARCHAR(50),
    IN p_goods_id INT,
    IN p_goods_name VARCHAR(255),
    IN p_order_amount DECIMAL(10,2),
    IN p_payment_method VARCHAR(20),
    IN p_balance_amount DECIMAL(10,2),
    IN p_online_payment_amount DECIMAL(10,2),
    IN p_base_amount DECIMAL(10,2),
    IN p_commission_user_id INT,
    IN p_commission_user_role VARCHAR(50),
    IN p_buyer_user_id INT,
    IN p_buyer_user_role VARCHAR(50),
    IN p_commission_type VARCHAR(20),
    IN p_commission_rate DECIMAL(5,2),
    IN p_commission_amount DECIMAL(10,2),
    IN p_calculation_rule TEXT,
    IN p_calculation_details JSON,
    IN p_config_source VARCHAR(20),
    IN p_config_value VARCHAR(100),
    IN p_is_distributed TINYINT,
    IN p_distribution_type VARCHAR(10),
    IN p_distribution_status VARCHAR(10),
    IN p_distribution_memo VARCHAR(500),
    IN p_money_log_id INT,
    IN p_before_balance DECIMAL(10,2),
    IN p_after_balance DECIMAL(10,2),
    IN p_parent_user_id INT,
    IN p_parent_user_role VARCHAR(50),
    IN p_has_superior TINYINT,
    IN p_platform_profit DECIMAL(10,2),
    IN p_total_distributed DECIMAL(10,2)
)
BEGIN
    INSERT INTO `fa_commission_log` (
        `batch_no`, `step_no`, `order_type`, `order_id`, `order_no`, 
        `goods_id`, `goods_name`, `order_amount`, `payment_method`, 
        `balance_amount`, `online_payment_amount`, `base_amount`,
        `commission_user_id`, `commission_user_role`, `buyer_user_id`, `buyer_user_role`,
        `commission_type`, `commission_rate`, `commission_amount`, `platform_profit`, `total_distributed`,
        `calculation_rule`, `calculation_details`, `config_source`, `config_value`,
        `is_distributed`, `distribution_type`, `distribution_status`, `distribution_memo`,
        `money_log_id`, `before_balance`, `after_balance`,
        `parent_user_id`, `parent_user_role`, `has_superior`, `createtime`
    ) VALUES (
        p_batch_no, p_step_no, p_order_type, p_order_id, p_order_no,
        p_goods_id, p_goods_name, p_order_amount, p_payment_method,
        p_balance_amount, p_online_payment_amount, p_base_amount,
        p_commission_user_id, p_commission_user_role, p_buyer_user_id, p_buyer_user_role,
        p_commission_type, p_commission_rate, p_commission_amount, p_platform_profit, p_total_distributed,
        p_calculation_rule, p_calculation_details, p_config_source, p_config_value,
        p_is_distributed, p_distribution_type, p_distribution_status, p_distribution_memo,
        p_money_log_id, p_before_balance, p_after_balance,
        p_parent_user_id, p_parent_user_role, p_has_superior, UNIX_TIMESTAMP()
    );
END$$
DELIMITER ;

-- 5. 创建分佣统计查询视图
CREATE OR REPLACE VIEW `v_commission_detailed_summary` AS
SELECT 
    cl.order_type,
    cl.commission_user_id,
    u.nickname as commission_user_name,
    cl.commission_user_role,
    COUNT(*) as commission_count,
    SUM(cl.commission_amount) as total_commission,
    SUM(CASE WHEN cl.is_distributed = 1 THEN cl.commission_amount ELSE 0 END) as distributed_amount,
    SUM(CASE WHEN cl.distribution_type = 'online' THEN cl.commission_amount ELSE 0 END) as online_amount,
    SUM(CASE WHEN cl.distribution_type = 'offline' THEN cl.commission_amount ELSE 0 END) as offline_amount,
    SUM(cl.balance_amount) as total_balance_used,
    SUM(cl.online_payment_amount) as total_online_payment,
    AVG(cl.commission_rate) as avg_commission_rate,
    MIN(cl.createtime) as first_commission_time,
    MAX(cl.createtime) as last_commission_time
FROM fa_commission_log cl
LEFT JOIN fa_user u ON cl.commission_user_id = u.id
GROUP BY cl.order_type, cl.commission_user_id, cl.commission_user_role;

-- 6. 添加新的索引
ALTER TABLE `fa_commission_log` ADD INDEX `idx_payment_method` (`payment_method`);
ALTER TABLE `fa_commission_log` ADD INDEX `idx_balance_amount` (`balance_amount`);
ALTER TABLE `fa_commission_log` ADD INDEX `idx_distribution_complete` (`is_distributed`, `distribution_status`);
