define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'commission_log/index' + location.search,
                    add_url: 'commission_log/add',
                    edit_url: 'commission_log/edit',
                    del_url: 'commission_log/del',
                    multi_url: 'commission_log/multi',
                    import_url: 'commission_log/import',
                    table: 'commission_log',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false, sortable: true},
                        {field: 'batch_no', title: '批次号', operate: 'LIKE', sortable: true},
                        {field: 'step_no', title: '步骤', operate: false, width: 60},
                        {field: 'order_type', title: '订单类型', operate: '=', searchList: {
                            'course': '线上课程',
                            'offline_course': '线下课程',
                            'course_package': '课程套餐',
                            'wanlshop': '商城商品',
                            'service': '服务'
                        }, formatter: function(value, row, index) {
                            var typeMap = {
                                'course': '线上课程',
                                'offline_course': '线下课程',
                                'course_package': '课程套餐',
                                'wanlshop': '商城商品',
                                'service': '服务'
                            };
                            return typeMap[value] || value;
                        }},
                        {field: 'order_no', title: '订单号', operate: 'LIKE'},
                        {field: 'goods_name', title: '商品名称', operate: 'LIKE'},
                        {field: 'commission_user_id', title: '分佣用户ID', operate: '='},
                        {field: 'commission_user_role', title: '分佣角色', operate: '=', searchList: {
                            'platform': '平台',
                            'city_manager': '城市运营商',
                            'nursing_home_director': '养老院长',
                            'elderly_advisor': '养老顾问',
                            'teacher': '老师',
                            'service_provider': '服务者'
                        }, formatter: function(value, row, index) {
                            var roleMap = {
                                'platform': '平台',
                                'city_manager': '城市运营商',
                                'nursing_home_director': '养老院长',
                                'elderly_advisor': '养老顾问',
                                'teacher': '老师',
                                'service_provider': '服务者'
                            };
                            return roleMap[value] || value;
                        }},
                        {field: 'commission_rate', title: '分佣比例(%)', operate: 'BETWEEN', sortable: true},
                        {field: 'commission_amount', title: '分佣金额', operate: 'BETWEEN', sortable: true, formatter: function(value, row, index) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'is_distributed', title: '发放状态', operate: '=', searchList: {
                            '0': '未发放',
                            '1': '已发放'
                        }, formatter: function(value, row, index) {
                            return value == 1 ? '<span class="label label-success">已发放</span>' : '<span class="label label-warning">未发放</span>';
                        }},
                        {field: 'distribution_type', title: '发放方式', operate: '=', searchList: {
                            'online': '线上',
                            'offline': '线下'
                        }, formatter: function(value, row, index) {
                            return value == 'online' ? '<span class="label label-primary">线上</span>' : '<span class="label label-info">线下</span>';
                        }},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, sortable: true},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'detail',
                                    text: '详情',
                                    title: '查看详情',
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    icon: 'fa fa-list',
                                    url: 'commission_log/detail'
                                }
                            ],
                            formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        
        detail: function () {
            Controller.api.bindevent();
        },
        
        statistics: function () {
            // 统计页面的初始化
            Controller.api.bindevent();
            
            // 可以在这里添加图表初始化代码
            // 例如使用ECharts显示分佣统计图表
        },
        
        add: function () {
            Controller.api.bindevent();
        },
        
        edit: function () {
            Controller.api.bindevent();
        },
        
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    
    return Controller;
});
