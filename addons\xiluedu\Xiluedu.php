<?php

namespace addons\xiluedu;

use app\common\library\Menu;
use app\common\model\MoneyLog;
use app\common\model\User;
use app\common\model\xiluedu\Config;
use app\common\model\xiluedu\Course;
use app\common\model\xiluedu\CourseOrder;
use app\common\model\xiluedu\DivideMoney;
use app\common\model\xiluedu\OfflineCourse;
use app\common\model\xiluedu\OfflineOrder;
use app\common\model\xiluedu\UserMessage;
use app\common\model\xiluedu\UserMessageAccount;
use app\common\model\xiluedu\UserRelation;
use app\common\model\xiluedu\VipOrder;
use app\common\model\xiluedu\Withdraw;
use think\Addons;
use think\Db;
use think\db\Expression;
use think\Exception;
use think\Hook;
use think\Log;
use function fast\array_get;

/**
 * 插件
 */
class Xiluedu extends Addons
{

    /**
     * 插件安装方法
     * @return bool
     */
    public function install()
    {
        $menu = include ADDON_PATH . 'xiluedu' . DS . 'data' . DS . 'menu.php';
	    Menu::create($menu);
        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall()
    {
        Menu::delete("xiluedu");
        return true;
    }

    /**
     * 插件启用方法
     * @return bool
     */
    public function enable()
    {
        Menu::enable("xiluedu");
        return true;
    }

    /**
     * 插件禁用方法
     * @return bool
     */
    public function disable()
    {
        Menu::disable("xiluedu");
        return true;
    }


    /*******************分销绑定关系**************/
    public function xilueduBindUser(User $user,$params){
        $invitor_user_id = array_get($params,'invitor_user_id');
        if(!$invitor_user_id) return false;
        if($invitor_user_id == $user->id) return false;
        $user_relation = UserRelation::where('user_id',$user->id)->find();
        if($user_relation) return false;

        #.不能互绑定
        $puser = User::get($invitor_user_id);
        if(!$puser) return false;
        $puser_relation = UserRelation::where('user_id',$invitor_user_id)->find();
        if($puser_relation &&
            in_array($user->id,[$puser_relation['first_user_id'],$puser_relation['second_user_id']])){
            return false;
        }
        $user_relation = UserRelation::create([
            'user_id'       =>  $user->id,
            'first_user_id' =>  $invitor_user_id,
            'second_user_id'=>  $puser_relation ? $puser_relation->first_user_id :0,
        ]);
    }

    /**
     * 佣金计算
     * @param $order
     * @param $params
     */
    public function xilueduOrderDivide($order,$params){

        $distribution = Config::getMyConfig("distribution");
        if(!$distribution){
            return false;
        }
        if(!$order ||
            !$distribution ||
            array_get($distribution,'distribution_status') != '1'){
            return false;
        }
        $type = array_get($params,'type');
        if(!$type || !in_array($type,['course','offline','vip'])){
            return false;
        }
        $order_divide = \app\common\model\xiluedu\OrderDivide::get(['order_id'=>$order->id,'type'=>$type]);
        if($order_divide){
            return false;
        }
        if($type == 'course'){
            $divide_money = $order->pay_price;
        }else if($type == 'offline'){
            $divide_money = $order->pay_price;
        }else{
            $divide_money = $order->pay_price;
        }

        if($divide_money<=0.1){
            return false;
        }
        $user_relation = UserRelation::get(['user_id'=>$order->user_id]);
        if(!$user_relation){
            return false;
        }
        #使用模块比例
        $distribution_module = array_get($distribution,'distribution_module');
        #一级分销金额
        $first_rate =$first_money = 0;
        if($user_relation->first_user_id>0){
            if($distribution_module == 2){
                if($type == 'course'){
                    $divide_rate = Course::field("distribution_one_rate,distribution_two_rate")->where('id',$order->course_id)->find();
                }else if($type == 'offline'){
                    $divide_rate = OfflineCourse::field("distribution_one_rate,distribution_two_rate")->where('id',$order->course_id)->find();
                }else{
                    $divide_rate = OfflineCourse::field("distribution_one_rate,distribution_two_rate")->where('id',$order->vip_id)->find();
                }
                $first_rate = $divide_rate ? $divide_rate->distribution_one_rate : 0;
            }else{
                $first_rate = array_get($distribution,'distribution_one_rate') ?? 0;
            }

            $first_money = sprintf('%.2f',$first_rate*$divide_money / 100);
        }
        #二级分销金额
        $second_rate =$second_money = 0;
        if($user_relation->second_user_id>0){
            if($distribution_module == 2){
                $second_rate = isset($divide_rate) ? $divide_rate->distribution_two_rate : 0;
            }else{
                $second_rate = array_get($distribution,'distribution_two_rate') ?? 0;
            }
            $second_money = sprintf('%.2f',$second_rate*$divide_money / 100);
        }
        \app\common\model\xiluedu\OrderDivide::create([
            'type'              =>  $type,
            'distribution_module'=> $distribution_module,
            'user_id'           =>  $order->user_id,
            'order_id'          =>  $order->id,
            'order_divide_money'=>  $divide_money,
            'first_rate'        =>  $first_rate,
            'first_money'       =>  $first_money,
            'first_user_id'     =>  $user_relation->first_user_id,
            'second_rate'       =>  $second_rate,
            'second_money'      =>  $second_money,
            'second_user_id'    =>  $user_relation->second_user_id,
            'status'            =>  '1'
        ]);
    }

    /**
     * 佣金分配
     */
    public function xilueduDivide($order,$params){
        $type = array_get($params,'type');
        if(!$type){
            return false;
        }
        $divide = \app\common\model\xiluedu\OrderDivide::get(['order_id'=>$order->id,'type'=>$type]);
        if(!$divide || $divide->status != '2'){
            return false;
        }

        if($type == 'course'){
            $memo = '下级购买在线课程';
        }else if($type == 'offline'){
            $memo = '下级报名线下课程';
        }else{
            $memo = '下级购买会员';
        }
        try {
            #一级反佣
            if($divide->first_user_id && $divide->first_money > 0){
                $this->add_money_log($divide,$divide->first_user_id,$divide->first_money,$memo);
            }
            #二级反佣
            if($divide->second_user_id && $divide->second_money > 0){
                $this->add_money_log($divide,$divide->second_user_id,$divide->second_money,$memo);
            }
            #佣金记录表状态改为已完成分配
            $divide->status = '3';
            $divide->save();
        }catch (Exception $e){
            Db::rollback();
            return false;
        }
        //Db::commit();
    }

    /**
     * @param $divide
     * @param $user_id
     * @param $money
     * @param $memo
     * @return MoneyLog|false
     * @throws \think\exception\DbException
     */
    private function add_money_log($divide,$user_id,$money,$memo){
        if($money<=0.01) return false;
        if($divide->status !== 2) {
            Log::record("不可分佣状态",'error');
        }
        $user_account = UserMessageAccount::get(['user_id'=>$user_id]);
        if(!$user_account){
            $user_account = UserMessageAccount::addAccount(['user_id'=>$user_id]);
        }
        #1.佣金日志
        $result = MoneyLog::create([
            'user_id'   => $user_id,
            'before'    => $user_account->money,
            'money'     => $money,
            'after'     => $user_account->money + $money,
            'memo'      => $memo
        ]);
        #2.佣金来源关联
        DivideMoney::create([
            'type'          => $divide->type,
            'user_id'       => $user_id,
            'buy_user_id'   => $divide->user_id,
            'target_id'     => $divide->id,
            'order_id'      => $divide->order_id,
            'money_log_id'  => $result->id,
        ]);
        $user_account->money = new Expression("money+".$money);
        $user_account->total_money = new Expression("total_money+".$money);
        $user_account->save();
        #订阅消息
        //Hook::listen('commission_message',$result);
        return $result;
    }


    /**
     * 申请提现记录
     * @param Withdraw $withdraw
     */
    public function xilueduWithdrawLog(Withdraw $withdraw){
        $account = UserMessageAccount::get(['user_id'=>$withdraw->user_id]);
        if(!$account){
            throw new Exception("账号不存在");
        }
        $result = MoneyLog::create([
            'user_id'   => $withdraw->user_id,
            'before'    => $account->money,
            'money'     => '-'.$withdraw->money,
            'after'     => bcsub($account->money, $withdraw->money, 2),
            'memo'      => "用户提现"
        ]);
        #2.佣金来源关联
        DivideMoney::create([
            'type'          => 'withdraw',
            'user_id'       => $withdraw->user_id,
            'buy_user_id'   => 0,
            'target_id'     => $withdraw->id,
            'order_id'      => 0,
            'money_log_id'  => $result->id,
        ]);
        $account->money = new Expression("money-" . $withdraw->money);
        $account->withdraw_money = new Expression("withdraw_money+".$withdraw->money);
        $account->save();
        return true;
    }

    /**
     * 申请提现拒绝
     * @param \app\admin\model\xiluedu\Withdraw $withdraw
     */
    public function xilueduWithdrawRefuse(\app\admin\model\xiluedu\Withdraw $withdraw){
        $account = UserMessageAccount::get(['user_id'=>$withdraw->user_id]);
        if(!$account){
            throw new Exception("账号不存在");
        }
        $result = MoneyLog::create([
            'user_id'   => $withdraw->user_id,
            'before'    => $account->money,
            'money'     => $withdraw->money,
            'after'     => bcadd($account->money, $withdraw->money, 2),
            'memo'      => "提现失败"
        ]);
        #2.佣金来源关联
        DivideMoney::create([
            'type'          => 'refuse',
            'user_id'       => $withdraw->user_id,
            'buy_user_id'   => 0,
            'target_id'     => $withdraw->id,
            'order_id'      => 0,
            'money_log_id'  => $result->id,
        ]);
        $account->money = new Expression("money+" . $withdraw->money);
        $account->withdraw_money = new Expression("withdraw_money-".$withdraw->money);
        $account->save();
        return true;

    }



    /**
     * 线上课程购买成功
     */
    public function xilueduCourseBuy(CourseOrder &$courseOrder) {
        if(!$courseOrder || $courseOrder->pay_status == 1) {
            //不存在或未支付
            return;
        }

        // 如果购买的是课程16，开通养老顾问身份
        if($courseOrder->course_id == 16) {
            $user = \app\common\model\User::get($courseOrder->user_id);
            if($user && $user->is_ylgw == 0) {
                // 检查是否是通过新的购买方式（根据订单号前缀和平台判断）
                $order_trade_no = $courseOrder->order_trade_no ?: '';
                $platform = $courseOrder->platform ?: '';

                // 新的购买方式特征：
                // 1. Course16控制器：订单号以C16开头
                // 2. Package控制器：platform为quota，订单号以QO开头
                // 3. YlgwQuota控制器：订单号以Q开头
                $is_new_purchase = (
                    strpos($order_trade_no, 'C16') === 0 ||  // Course16控制器
                    strpos($order_trade_no, 'QO') === 0 ||   // Package控制器
                    ($platform === 'quota' && strpos($order_trade_no, 'Q') === 0) // YlgwQuota控制器
                );

                \think\Log::info("课程16钩子处理 - 订单ID: {$courseOrder->id}, 订单号: {$order_trade_no}, 平台: {$platform}, 是否新购买方式: " . ($is_new_purchase ? 'true' : 'false'));

                if(!$is_new_purchase) {
                    // 旧的购买方式（传统Pay控制器），需要执行分佣
                    if($courseOrder->pay_price > 0) {
                        \think\Log::info("执行传统分佣逻辑 - 订单ID: {$courseOrder->id}, 金额: {$courseOrder->pay_price}");
                        // 使用新的简化分佣逻辑
                        $this->executeCourse16Commission($courseOrder);
                    }
                } else {
                    \think\Log::info("新的购买方式，分佣已在对应控制器中处理 - 订单ID: {$courseOrder->id}");
                }

                // 开通养老顾问身份前，调整上级关系
                $original_parent_id = $user->parent_id;
                $final_parent_id = $this->adjustParentForYlgw($user);

                if ($original_parent_id != $final_parent_id) {
                    $user->parent_id = $final_parent_id;
                    \think\Log::info("调整养老顾问上级关系 - 用户ID: {$user->id}, 原上级: {$original_parent_id}, 新上级: {$final_parent_id}");
                }

                // 开通养老顾问身份
                $user->is_ylgw = 1;
                $user->save();
                \think\Log::info("开通养老顾问身份成功 - 用户ID: {$user->id}, 最终上级: {$final_parent_id}");
            }
        }

        // 如果购买的是课程10，开通养老院长身份
        if($courseOrder->course_id == 10) {
            $user = \app\common\model\User::get($courseOrder->user_id);
            if($user && $user->is_sqdl == 0) {
                // 在更新用户身份和上级关系之前，先计算养老顾问佣金
                // 因为佣金计算需要基于原有的上级关系
                if($courseOrder->pay_price > 0) {
                    \app\common\model\xiluedu\CourseOrder::shareProfit($courseOrder->id, 1);
                }

                // 升级为养老院长
                $user->is_sqdl = 1;
                $user->is_ylgw = 0;
                // 用户升级为养老院长后，需要重新分配上级
                // 优先查找用户地址对应的城市运营商，如果没有则设为平台直属(0)
                if($user->addr) {
                    $quyu = \app\common\model\UserArea::where('addr', $user->addr)->find();
                    if($quyu && $quyu['user_id']) {
                        // 如果找到城市运营商，设置为上级
                        $user->parent_id = $quyu['user_id'];
                    } else {
                        // 如果没有找到城市运营商，设为平台直属
                        $user->parent_id = 0;
                    }
                } else {
                    // 如果用户没有地址信息，设为平台直属
                    $user->parent_id = 0;
                }

                $user->save();
            }
        }

        $model = UserMessage::create([
            'user_id' => $courseOrder->user_id,
            'type' => UserMessage::TYPE_COURSE_ORDER,
            'title' => '您已成功购买在线视频！',
            'content' => sprintf('您已成功购买了%s,请前往学习吧~',$courseOrder->course->name),
            'extra' => ['order_id'=>$courseOrder->id,'course_id'=>$courseOrder->course_id]
        ]);
        UserMessageAccount::where('user_id',$courseOrder->user_id)->setInc('user_message',1);
    }

    /**
     * 线下课程购买成功
     */
    public function xilueduOfflineBuy(OfflineOrder &$courseOrder) {
        if(!$courseOrder || $courseOrder->pay_status == 0) {
            //不存在或未支付
            return;
        }
        $model = UserMessage::create([
            'user_id' => $courseOrder->user_id,
            'type' => UserMessage::TYPE_OFFLINE_ORDER,
            'title' => '您已成功预约线下课程！',
            'content' => sprintf('您已预约了%s,请及时参与哦~',$courseOrder->course_name),
            'extra' => ['order_id'=>$courseOrder->id,'course_id'=>$courseOrder->course_id]
        ]);
        UserMessageAccount::where('user_id',$courseOrder->user_id)->setInc('user_message',1);
    }

    /**
     * 线下Vip购买成功
     */
    public function xilueduVipBuy(VipOrder &$vipOrder) {
        if(!$vipOrder || $vipOrder->pay_status == 0) {
            //不存在或未支付
            return;
        }
        $model = UserMessage::create([
            'user_id' => $vipOrder->user_id,
            'type' => UserMessage::TYPE_VIP_ORDER,
            'title' => '您已成功购买vip！',
            'content' => sprintf('您已成功开通%s会员,请前往学习吧~',$vipOrder->vip_name),
            'extra' => ['order_id'=>$vipOrder->id,'vip_id'=>$vipOrder->vip_id]
        ]);
        UserMessageAccount::where('user_id',$vipOrder->user_id)->setInc('user_message',1);
    }

    /**
     * 调整养老顾问的上级关系
     * 如果用户已有上级且上级是养老顾问，则向上查找直到找到养老院长或城市运营商
     *
     * @param \app\common\model\User $user 用户对象
     * @return int 最终上级ID
     */
    private function adjustParentForYlgw($user)
    {
        // 如果用户没有上级，保持不变
        if (!$user->parent_id || $user->parent_id == 0) {
            return $user->parent_id;
        }

        // 向上查找最高级别的上级
        return $this->findTopLevelParent($user->parent_id);
    }

    /**
     * 向上查找最高级别的上级（养老院长或城市运营商）
     *
     * @param int $user_id 起始用户ID
     * @return int 最终上级ID
     */
    private function findTopLevelParent($user_id)
    {
        $max_depth = 10; // 防止无限循环
        $current_depth = 0;
        $current_user_id = $user_id;

        while ($current_depth < $max_depth) {
            $user = \app\common\model\User::where('id', $current_user_id)
                ->field('id,parent_id,is_ylgw,is_sqdl,is_qydl,nickname')
                ->find();

            if (!$user) {
                // 用户不存在，返回平台
                \think\Log::warning("查找上级时用户不存在 - 用户ID: {$current_user_id}，返回平台");
                return 0;
            }

            // 如果当前用户是养老院长或城市运营商，返回这个用户
            if ($user['is_sqdl'] == 1 || $user['is_qydl'] == 1) {
                $role = $user['is_sqdl'] == 1 ? '养老院长' : '城市运营商';
                \think\Log::info("找到最终上级 - 用户ID: {$user['id']}, 角色: {$role}, 昵称: {$user['nickname']}");
                return $user['id'];
            }

            // 如果当前用户是养老顾问或普通用户，继续向上查找
            if ($user['is_ylgw'] == 1) {
                \think\Log::info("继续向上查找 - 当前用户ID: {$user['id']} (养老顾问), 上级ID: {$user['parent_id']}");
            } else {
                \think\Log::info("继续向上查找 - 当前用户ID: {$user['id']} (普通用户), 上级ID: {$user['parent_id']}");
            }

            // 检查是否有上级
            if (!$user['parent_id'] || $user['parent_id'] == 0) {
                // 没有上级，返回平台
                \think\Log::info("用户没有上级，返回平台 - 用户ID: {$user['id']}");
                return 0;
            }

            // 继续向上查找
            $current_user_id = $user['parent_id'];
            $current_depth++;
        }

        // 超过最大深度，返回平台
        \think\Log::warning("查找上级超过最大深度，返回平台 - 起始用户ID: {$user_id}");
        return 0;
    }

    /**
     * 执行课程16的新分佣逻辑（钩子中使用，避免双重分佣）
     */
    private function executeCourse16Commission($courseOrder)
    {
        \think\Log::info("课程16钩子分佣开始 - 订单ID: {$courseOrder->id}, 用户ID: {$courseOrder->user_id}, 金额: {$courseOrder->pay_price}");

        $user = \app\common\model\User::where('id', $courseOrder->user_id)->field('id,parent_id,is_sqdl,is_qydl')->find();

        // 基础分佣金额（固定金额，不再使用配置比例）
        $ylgw_base = 91.25;  // 养老顾问基础分佣 (25%)
        $sqdl_base = 182.5;  // 养老院长基础分佣 (50%)
        $qydl_base = 36.5;   // 城市运营商基础分佣 (10%)

        \think\Log::info("钩子新分佣逻辑 - 基础金额: 养老顾问={$ylgw_base}, 养老院长={$sqdl_base}, 城市运营商={$qydl_base}");

        // 使用统一的分佣逻辑
        \app\api\controller\xiluedu\Course16::processCourse16Commission($user, '课程16', $courseOrder);

        \think\Log::info("课程16钩子分佣完成 - 订单ID: {$courseOrder->id}");
    }





    /**
     * 应用初始化
     */
    public function appInit()
    {
        //先判断是否已经通过其它方式引入了此类
        if(!class_exists("\PHPQRCode\QRcode")){
            \think\Loader::addNamespace('PHPQRCode', ADDON_PATH . 'xiluedu' . DS . 'library' . DS . 'PHPQRCode' . DS.'lib'.DS.'PHPQRCode'.DS);
        }
    }

}
