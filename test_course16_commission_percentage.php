<?php
/**
 * 测试课程16按比例分佣逻辑
 * 验证城市运营商直接推荐获得60%分佣
 */

echo "=== 课程16按比例分佣测试 ===\n\n";

// 模拟不同的订单金额
$test_amounts = [365, 500, 1000];

foreach ($test_amounts as $order_amount) {
    echo "## 订单金额: {$order_amount}元\n\n";
    
    // 计算各种分佣比例
    $ylgw_rate = 25;  // 养老顾问基础分佣比例 25%
    $sqdl_rate = 50;  // 养老院长基础分佣比例 50%
    $qydl_rate = 10;  // 城市运营商基础分佣比例 10%
    $qydl_direct_rate = 60; // 城市运营商直接推荐分佣比例 60%
    
    $ylgw_base = $order_amount * ($ylgw_rate / 100);
    $sqdl_base = $order_amount * ($sqdl_rate / 100);
    $qydl_base = $order_amount * ($qydl_rate / 100);
    $qydl_direct = $order_amount * ($qydl_direct_rate / 100);
    
    echo "### 分佣金额计算\n";
    echo "- 养老顾问基础分佣 (25%): {$ylgw_base}元\n";
    echo "- 养老院长基础分佣 (50%): {$sqdl_base}元\n";
    echo "- 城市运营商基础分佣 (10%): {$qydl_base}元\n";
    echo "- 城市运营商直接推荐分佣 (60%): {$qydl_direct}元\n\n";
    
    echo "### 7种情况分佣结果\n\n";
    
    // 情况1：城市运营商A -> 养老顾问B -> 用户C成为养老顾问
    echo "**情况1**: 城市运营商A -> 养老顾问B -> 用户C成为养老顾问\n";
    echo "- B获得ylgw_total_commission: {$ylgw_base}元 (25%)\n";
    echo "- A获得money: {$qydl_direct}元 (60%) ✅ **修复重点**\n";
    echo "- 总分佣: " . ($ylgw_base + $qydl_direct) . "元\n";
    echo "- 平台剩余: " . ($order_amount - $ylgw_base - $qydl_direct) . "元\n\n";
    
    // 情况2：城市运营商A -> 养老院长B -> 养老顾问C -> 用户D成为养老顾问
    echo "**情况2**: 城市运营商A -> 养老院长B -> 养老顾问C -> 用户D成为养老顾问\n";
    echo "- C获得ylgw_total_commission: {$ylgw_base}元 (25%)\n";
    echo "- B获得money: {$sqdl_base}元 (50%)\n";
    echo "- A获得money: {$qydl_base}元 (10%)\n";
    echo "- 总分佣: " . ($ylgw_base + $sqdl_base + $qydl_base) . "元\n";
    echo "- 平台剩余: " . ($order_amount - $ylgw_base - $sqdl_base - $qydl_base) . "元\n\n";
    
    // 情况3：养老院长A -> 养老顾问B -> 用户C成为养老顾问
    echo "**情况3**: 养老院长A -> 养老顾问B -> 用户C成为养老顾问\n";
    echo "- B获得ylgw_total_commission: {$ylgw_base}元 (25%)\n";
    echo "- A获得money: {$sqdl_base}元 (50%)\n";
    echo "- 总分佣: " . ($ylgw_base + $sqdl_base) . "元\n";
    echo "- 平台剩余: " . ($order_amount - $ylgw_base - $sqdl_base) . "元\n\n";
    
    // 情况4：养老院长A -> 用户B成为养老顾问
    echo "**情况4**: 养老院长A -> 用户B成为养老顾问\n";
    echo "- A获得money: {$sqdl_base}元 (50%)\n";
    echo "- 总分佣: {$sqdl_base}元\n";
    echo "- 平台剩余: " . ($order_amount - $sqdl_base) . "元\n\n";
    
    // 情况5：养老顾问A(上级是0) -> 用户B成为养老顾问
    echo "**情况5**: 养老顾问A(上级是0) -> 用户B成为养老顾问\n";
    echo "- A获得money: {$ylgw_base}元 (25%)\n";
    echo "- A的ylgw_total_commission: 0元\n";
    echo "- 总分佣: {$ylgw_base}元\n";
    echo "- 平台剩余: " . ($order_amount - $ylgw_base) . "元\n\n";
    
    // 情况6：普通用户A -> 用户B成为养老顾问
    echo "**情况6**: 普通用户A -> 用户B成为养老顾问\n";
    echo "- 无分佣\n";
    echo "- 总分佣: 0元\n";
    echo "- 平台剩余: {$order_amount}元\n\n";
    
    // 情况7：城市运营商A -> 养老院长B -> 用户C成为养老顾问
    echo "**情况7**: 城市运营商A -> 养老院长B -> 用户C成为养老顾问\n";
    echo "- B获得money: {$sqdl_base}元 (50%)\n";
    echo "- A获得money: {$qydl_base}元 (10%)\n";
    echo "- 总分佣: " . ($sqdl_base + $qydl_base) . "元\n";
    echo "- 平台剩余: " . ($order_amount - $sqdl_base - $qydl_base) . "元\n\n";
    
    echo "---\n\n";
}

echo "## 修复总结\n\n";
echo "### 问题\n";
echo "- 城市运营商A直接推荐用户B成为养老顾问时，只获得10%分佣而不是60%\n\n";

echo "### 修复\n";
echo "1. **动态计算分佣金额**：根据订单金额按比例计算，不再写死金额\n";
echo "2. **区分城市运营商分佣场景**：\n";
echo "   - 直接推荐：60%分佣\n";
echo "   - 作为上级的上级：10%分佣\n";
echo "3. **更新方法参数**：传递正确的分佣金额给各个处理方法\n\n";

echo "### 代码变更\n";
echo "```php\n";
echo "// 按比例计算分佣金额\n";
echo "\$qydl_direct_rate = 60; // 城市运营商直接推荐分佣比例 60%\n";
echo "\$qydl_direct = truncateDecimal(\$order_amount * (\$qydl_direct_rate / 100));\n";
echo "\n";
echo "// 城市运营商直接推荐的情况\n";
echo "self::handleQydlParent(\$parent, \$qydl_direct, \$package_name, \$order);\n";
echo "```\n\n";

echo "### 验证要点\n";
echo "1. **情况1验证**：城市运营商直接推荐应获得60%分佣\n";
echo "2. **情况2验证**：城市运营商作为上级的上级应获得10%分佣\n";
echo "3. **动态计算**：不同订单金额应按比例正确计算\n";
echo "4. **日志检查**：确认分佣金额计算日志正确\n";
