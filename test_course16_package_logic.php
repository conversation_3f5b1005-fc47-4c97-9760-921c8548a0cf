<?php
/**
 * 测试课程16套餐价格逻辑
 */

// 模拟套餐数据
$packages = [
    [
        'id' => 1,
        'name' => '单个购买',
        'quantity' => 1,
        'original_price' => 365.00,
        'qydl_price' => 365.00,
        'sqdl_price' => 365.00,
        'ylgw_price' => 365.00,
    ],
    [
        'id' => 2,
        'name' => '套餐购买(10个)',
        'quantity' => 10,
        'original_price' => 3650.00,
        'qydl_price' => 1000.00,
        'sqdl_price' => 1300.00,
        'ylgw_price' => 3650.00,
    ],
    [
        'id' => 3,
        'name' => '套餐购买(50个)',
        'quantity' => 50,
        'original_price' => 3650.00,
        'qydl_price' => 1000.00,
        'sqdl_price' => 1300.00,
        'ylgw_price' => 3650.00,
    ]
];

// 模拟用户类型
$userTypes = [
    'user' => '普通用户',
    'qydl' => '城市运营商',
    'sqdl' => '养老院长',
    'ylgw' => '养老顾问'
];

// 价格计算函数
function getPriceByUserType($package, $userType) {
    switch ($userType) {
        case 'qydl':
            return $package['qydl_price'];
        case 'sqdl':
            return $package['sqdl_price'];
        case 'ylgw':
            return $package['ylgw_price'];
        default:
            return $package['original_price'];
    }
}

// 总价计算函数（新逻辑：价格固定，不按数量计算）
function getTotalPrice($package, $userType) {
    return getPriceByUserType($package, $userType);
}

echo "=== 课程16套餐价格计算测试 ===\n\n";

foreach ($packages as $package) {
    echo "套餐：{$package['name']}\n";
    echo "名额数量：{$package['quantity']}个\n";
    echo "价格明细：\n";
    
    foreach ($userTypes as $type => $typeName) {
        $price = getPriceByUserType($package, $type);
        $totalPrice = getTotalPrice($package, $type);
        
        echo "  - {$typeName}：¥{$price} (总价：¥{$totalPrice})\n";
    }
    
    echo "说明：无论选择{$package['quantity']}个名额，价格都是固定的\n";
    echo str_repeat("-", 50) . "\n\n";
}

echo "=== 关键逻辑说明 ===\n";
echo "1. 套餐数量(quantity)表示购买后可获得的养老顾问开通名额数量\n";
echo "2. 套餐价格是固定的，不按数量计算\n";
echo "3. 例如：10个名额的套餐，城市运营商价格固定为¥1000，不是¥100×10\n";
echo "4. 50个名额的套餐，价格仍然是¥1000，只是获得的名额更多\n";
echo "5. 购买后，用户的ylgw_quota字段会增加对应的名额数量\n";
