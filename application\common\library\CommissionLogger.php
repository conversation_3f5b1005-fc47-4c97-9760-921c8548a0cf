<?php

namespace app\common\library;

use app\common\model\CommissionLog;
use app\common\model\User;

/**
 * 分佣日志记录助手类
 */
class CommissionLogger
{
    /**
     * 记录课程分佣日志
     * @param array $order 订单信息
     * @param array $commissions 分佣信息
     * @param string $courseType 课程类型 (course/offline_course/course_package)
     * @return bool
     */
    public static function logCourseCommission($order, $commissions, $courseType = 'course')
    {
        $buyer = User::where('id', $order['user_id'])->field('id,is_sqdl,is_qydl,is_ylgw')->find();
        $buyerRole = self::getUserRole($buyer);
        
        $params = [
            'order_type' => $courseType,
            'order_id' => $order['id'],
            'order_no' => $order['order_no'] ?? $order['orderId'] ?? '',
            'goods_id' => $order['course_id'] ?? 0,
            'goods_name' => $order['course_name'] ?? '课程',
            'order_amount' => $order['total_price'] ?? $order['pay_price'] ?? 0,
            'buyer_user_id' => $order['user_id'],
            'buyer_user_role' => $buyerRole,
            'commissions' => $commissions
        ];
        
        return CommissionLog::recordFullCommissionProcess($params);
    }
    
    /**
     * 记录商城分佣日志
     * @param array $order 订单信息
     * @param array $commissions 分佣信息
     * @return bool
     */
    public static function logWanlshopCommission($order, $commissions)
    {
        $buyer = User::where('id', $order['user_id'])->field('id,is_sqdl,is_qydl,is_ylgw')->find();
        $buyerRole = self::getUserRole($buyer);
        
        $params = [
            'order_type' => 'wanlshop',
            'order_id' => $order['id'],
            'order_no' => $order['order_no'] ?? '',
            'goods_id' => 0, // 商城可能有多个商品
            'goods_name' => '商城商品',
            'order_amount' => $order['total_price'] ?? 0,
            'buyer_user_id' => $order['user_id'],
            'buyer_user_role' => $buyerRole,
            'commissions' => $commissions
        ];
        
        return CommissionLog::recordFullCommissionProcess($params);
    }
    
    /**
     * 记录服务分佣日志
     * @param array $order 订单信息
     * @param array $commissions 分佣信息
     * @return bool
     */
    public static function logServiceCommission($order, $commissions)
    {
        $buyer = User::where('id', $order['user_id'])->field('id,is_sqdl,is_qydl,is_ylgw')->find();
        $buyerRole = self::getUserRole($buyer);
        
        $params = [
            'order_type' => 'service',
            'order_id' => $order['id'],
            'order_no' => $order['orderId'] ?? '',
            'goods_id' => $order['goods_id'] ?? 0,
            'goods_name' => $order['goods_name'] ?? '服务',
            'order_amount' => $order['sumprice'] ?? 0,
            'buyer_user_id' => $order['user_id'],
            'buyer_user_role' => $buyerRole,
            'commissions' => $commissions
        ];
        
        return CommissionLog::recordFullCommissionProcess($params);
    }
    
    /**
     * 创建分佣记录
     * @param int $userId 用户ID
     * @param string $userRole 用户角色
     * @param string $commissionType 分佣类型
     * @param float $rate 分佣比例
     * @param float $amount 分佣金额
     * @param float $baseAmount 计算基数
     * @param string $rule 计算规则
     * @param array $options 其他选项
     * @return array
     */
    public static function createCommissionRecord($userId, $userRole, $commissionType, $rate, $amount, $baseAmount, $rule, $options = [])
    {
        $user = User::where('id', $userId)->field('id,parent_id,is_sqdl,is_qydl,is_ylgw')->find();
        $hasSuperior = self::checkHasSuperior($user);
        
        // 确定发放方式
        $distributionType = 'online';
        $isDistributed = 0;
        $distributionStatus = 'pending';
        $distributionMemo = '';
        
        if ($userRole === 'elderly_advisor' && $hasSuperior) {
            $distributionType = 'offline';
            $distributionMemo = '有上级的养老顾问，通过线下对账分佣';
        } else {
            $isDistributed = 1;
            $distributionStatus = 'success';
            $distributionMemo = '直接发放到账户余额';
        }
        
        return [
            'user_id' => $userId,
            'user_role' => $userRole,
            'commission_type' => $commissionType,
            'rate' => $rate,
            'amount' => $amount,
            'base_amount' => $baseAmount,
            'calculation_rule' => $rule,
            'config_source' => $options['config_source'] ?? 'global',
            'config_value' => $options['config_value'] ?? '',
            'is_distributed' => $isDistributed,
            'distribution_type' => $distributionType,
            'distribution_status' => $distributionStatus,
            'distribution_memo' => $distributionMemo,
            'money_log_id' => $options['money_log_id'] ?? 0,
            'parent_user_id' => $user['parent_id'] ?? 0,
            'parent_user_role' => self::getParentRole($user),
            'has_superior' => $hasSuperior ? 1 : 0
        ];
    }
    
    /**
     * 获取用户角色
     * @param array $user 用户信息
     * @return string
     */
    public static function getUserRole($user)
    {
        if (!$user) return 'regular_user';
        
        if ($user['is_qydl'] == 1) return 'city_manager';
        if ($user['is_sqdl'] == 1) return 'nursing_home_director';
        if ($user['is_ylgw'] == 1) return 'elderly_advisor';
        
        return 'regular_user';
    }
    
    /**
     * 检查用户是否有上级
     * @param array $user 用户信息
     * @return bool
     */
    public static function checkHasSuperior($user)
    {
        if (!$user || !$user['parent_id'] || $user['parent_id'] <= 0) {
            return false;
        }
        
        $parent = User::where('id', $user['parent_id'])->field('is_sqdl,is_qydl')->find();
        if (!$parent) {
            return false;
        }
        
        // 检查上级是否是养老院长或城市运营商
        return ($parent['is_sqdl'] == 1 || $parent['is_qydl'] == 1);
    }
    
    /**
     * 获取上级角色
     * @param array $user 用户信息
     * @return string
     */
    public static function getParentRole($user)
    {
        if (!$user || !$user['parent_id'] || $user['parent_id'] <= 0) {
            return 'platform';
        }
        
        $parent = User::where('id', $user['parent_id'])->field('is_sqdl,is_qydl,is_ylgw')->find();
        if (!$parent) {
            return 'platform';
        }
        
        return self::getUserRole($parent);
    }
    
    /**
     * 格式化分佣规则说明
     * @param string $type 分佣类型
     * @param float $rate 比例
     * @param float $baseAmount 基数
     * @param string $configSource 配置来源
     * @return string
     */
    public static function formatCalculationRule($type, $rate, $baseAmount, $configSource = 'global')
    {
        $typeMap = [
            'direct' => '直接分佣',
            'indirect' => '间接分佣', 
            'recommendation' => '推荐分佣',
            'teacher' => '老师分佣',
            'service' => '服务者分佣'
        ];
        
        $typeName = $typeMap[$type] ?? $type;
        $configText = $configSource === 'global' ? '全局配置' : '商品配置';
        
        return sprintf('%s：基于%.2f元按%s%.2f%%计算', $typeName, $baseAmount, $configText, $rate);
    }
}
