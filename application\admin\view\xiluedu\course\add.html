<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-type" data-rule="required" class="form-control" name="row[type]">
                {foreach name="typeList" item="vo"}
                    <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Category_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-category_id" data-rule="required" data-source="xiluedu/category/selectpage?isTree=1" class="form-control selectpage" data-params='{"custom[is_service]":"0"}' name="row[category_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tag_ids')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tag_ids" data-rule="" data-source="xiluedu/tags/selectpage" data-multiple="true" data-params='{"custom[type]":"1"}' class="form-control selectpage" name="row[tag_ids]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Teacher_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-teacher_id" data-rule="" data-source="xiluedu/teacher/index" class="form-control selectpage" name="row[teacher_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Thumb_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-thumb_image" data-rule="required" class="form-control" size="50" name="row[thumb_image]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-thumb_image" class="btn btn-danger faupload" data-input-id="c-thumb_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-thumb_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-thumb_image" class="btn btn-primary fachoose" data-input-id="c-thumb_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-thumb_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-thumb_image"></ul>
        </div>
    </div>

<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Images')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input id="c-images" data-rule="" class="form-control" size="50" name="row[images]" type="text" value="">-->
<!--                <div class="input-group-addon no-border no-padding">-->
<!--                    <span><button type="button" id="faupload-images" class="btn btn-danger faupload" data-input-id="c-images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>-->
<!--                    <span><button type="button" id="fachoose-images" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>-->
<!--                </div>-->
<!--                <span class="msg-box n-right" for="c-images"></span>-->
<!--            </div>-->
<!--            <ul class="row list-inline faupload-preview" id="p-images"></ul>-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group hide selected3">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" data-rule="" class="form-control editor" rows="5" name="row[content]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Description')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-description" data-rule="required" rows="8" class="form-control" name="row[description]"></textarea>
        </div>
    </div>
    <div class="form-group selected1 selected2">
        <label class="control-label col-xs-12 col-sm-2">{:__('Introduce')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-introduce" data-rule="" rows="8" class="form-control editor" name="row[introduce]"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Salesprice')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-salesprice" data-rule="required" class="form-control" step="0.01" name="row[salesprice]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Market_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-market_price" data-rule="required" class="form-control" step="0.01" name="row[market_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_charge')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                {foreach name="isChargeList" item="vo"}
                <label for="row[is_charge]-{$key}"><input id="row[is_charge]-{$key}" name="row[is_charge]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_hot')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                {foreach name="isHotList" item="vo"}
                <label for="row[is_hot]-{$key}"><input id="row[is_hot]-{$key}" name="row[is_hot]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_new')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                {foreach name="isNewList" item="vo"}
                <label for="row[is_new]-{$key}"><input id="row[is_new]-{$key}" name="row[is_new]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Basic_num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-basic_num" data-rule="required" class="form-control" name="row[basic_num]" type="number" value="0">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">视频时长（分钟）:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_time" data-rule="required" class="form-control" name="row[total_time]" type="number" value="0">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('养老院长分成比例（%）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-shequ_bili" data-rule="" class="form-control" name="row[shequ_bili]" type="number" value=""  placeholder="请输入养老院长分成比例">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('城市运营商分成比例（%）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-quyu_bili" data-rule="" class="form-control" name="row[quyu_bili]" type="number" value=""  placeholder="请输入城市运营商分成比例">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('老师分成比例（%）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_bili" data-rule="" class="form-control" name="row[service_bili]" type="number" value=""  placeholder="请输入服务者分成比例">
        </div>
    </div>

<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('一级返佣比例')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input aria-describedby="c-distribution_one_rate" data-rule="integer,range(0~100)" class="form-control" name="row[distribution_one_rate]" type="text" value="0">-->
<!--                <span class="input-group-addon" id="c-distribution_one_rate">%</span>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('二级返佣比例')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input aria-describedby="c-distribution_two_rate"  data-rule="integer,range(0~100)" class="form-control" name="row[distribution_two_rate]" type="text" value="0">-->
<!--                <span class="input-group-addon" id="c-distribution_two_rate">%</span>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">客户端:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
                {foreach name="fromList" item="vo"}
                <label for="row[is_service]-{$key}"><input id="row[is_service]-{$key}" name="row[is_service]" type="radio" value="{$key}" {in name="key" value="0"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>

        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" data-rule="required" class="form-control" name="row[weigh]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Poster_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-poster_image" data-rule="" class="form-control" size="50" name="row[poster_image]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-poster_image" class="btn btn-danger faupload" data-input-id="c-poster_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-poster_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-poster_image" class="btn btn-primary fachoose" data-input-id="c-poster_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-poster_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-poster_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('海报视频')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-poster_video" data-rule="" class="form-control" size="50" name="row[poster_video]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-poster_video" class="btn btn-danger faupload" data-input-id="c-poster_video" data-mimetype="mp4" data-multiple="false" data-preview-id="p-poster_video"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-poster_video" class="btn btn-primary fachoose" data-input-id="c-poster_video" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-poster_video"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-poster_video"></ul>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
