<?php

namespace app\api\controller;

use app\api\model\service\MoneyLog;
use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\User as UserModel;
use app\common\model\UserArea;
use DateInterval;
use DatePeriod;
use DateTime;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;
use EasyWeChat\Factory;
/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third','getUserMenu', 'deleteUserByMobile', 'cleanDirtyData', 'checkDirtyData'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }

    }
    public function qrcode()
    {
        $user =$this->auth->getUserinfo();
        $user_id2 =$user['qydl_staff']!=0 ? $user['qydl_staff'] :$this->auth->id;
        $user = UserModel::get($user_id2);
        if(empty($user['qrcode'])){
            $config = [
                'app_id' =>  'wx850a3717257c5723', // 小程序ID
                'secret' => 'ed1a2b6130f5209e8e69ddcfcbc358f1', // 密码
                // 下面为可选项
                // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
                'response_type' => 'array',
                'log' => [
                    'level' => 'debug',
                    'file' => ROOT_PATH.'/public/uploads/20241018/273cd77a96b4205bc3b76637e719d7db.png/uploads/20241018/273cd77a96b4205bc3b76637e719d7db.png',
                ],
            ];
            $app = Factory::miniProgram($config);

            $response  = $app->app_code->getUnlimit($this->auth->invit_code, [
                'page'  => 'pages/index/index',
                'width' => 600,
            ]);
            if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
                $filename = $response->save(ROOT_PATH.'public/uploads/drivingcode');
                \app\common\model\User::where('id',$this->auth->id)->update(['qrcode'=>'/uploads/drivingcode/'.$filename]);
            }
            $url = '/uploads/drivingcode/'.$filename;
        } else {
            $url = $user['qrcode'];
        }
        $this->success('ok',cdnurl($url,'https://service.jiaqingfu.com.cn'));
    }

    /**
     * 获取分享信息
     * <AUTHOR>
     * @date 2024/9/23  下午4:24
     * @notes
     */
    public function shareInfo()
    {
        //获取奖励优惠券
        $coupon = \app\common\model\service\Coupon::where('is_check',1)->where('type',5)
            ->where('endtime','>',time())->order('id desc')->find();
        //已邀请好友
        $shareUserIds = model('app\common\model\User')->where('parent_id',$this->auth->id)->column('id');
        //已使用优惠券数量
        $couponNum = Db::name('service_user_coupon')->where('user_id','in',$shareUserIds)->where('type',5)->where('state',1)->count();
        //帮好友节省金额
        $couponMoney = Db::name('service_user_coupon')->where('user_id','in',$shareUserIds)->where('type',5)->where('state',1)->sum('reduce');

        $info = [
            'share_img'=>config('site.share_img'),
            'share_user_img'=>config('site.share_user_img'),
            'coupon' => $coupon,
            'share_user_num' => count($shareUserIds),
            'coupon_num' => $couponNum,
            'coupon_money' => $couponMoney,
        ];
        $this->success('ok',$info);
    }

    /**
     * 清空足迹
     * <AUTHOR>
     * @date 2024/9/23  上午11:09
     * @notes
     */
    public function delFooterView()
    {
        $type = $this->request->post('type');
        if($type == 1){//服务
            model('app\common\model\service\FooterView')->where('user_id',$this->auth->id)->delete();
        }elseif ($type == 2){//商城
            model('app\api\model\wanlshop\Record')->where('user_id',$this->auth->id)->delete();
        }elseif ($type == 3){//课程
            model('app\common\model\xiluedu\FooterView')->where('user_id',$this->auth->id)->delete();
        }
        $this->success('ok');
    }

    /**
     * 数据统计
     * <AUTHOR>
     * @date 2024/9/9  下午5:58
     * @notes
     */
    public function dataInfo()
    {
        if($this->auth->is_sqdl != 1 && $this->auth->is_qydl != 1 && $this->auth->is_ylgw != 1){
            $this->error(__('暂无权限'));
        }
        $dl_num = 0;
        $zt_num = 0;
        if($this->auth->is_qydl == 1){
            $child_user = UserModel::getUserChilds($this->auth->id,true);
            $dl_num = count($child_user);
            $zt_num = model('app\common\model\User')->where('parent_id','=',$this->auth->id)->count();
        }
//        $time = $this->request->post('time','week');
        $start_time = $this->request->param('start_time');
        $end_time = $this->request->param('end_time');

        $date = [];
//        $monthFrist = date('Ym01',strtotime($start_time));  //月初时间
//        $i= (int) date('md',strtotime($start_time))-1;
//        dump(date('md',strtotime($end_time.' 23:59:59')));exit;
//        for ($i;$i<date('md',strtotime($end_time.' 23:59:59'));$i++){
//            $nowGross = $monthFrist + $i;
//            $date[] = date('Y-m-d', strtotime($nowGross));
//        }

        $startDate = new DateTime($start_time); // 格式为 YYYY-MM-DD
        $endDate = new DateTime($end_time); // 格式为 YYYY-MM-DD

// 创建一个 DateInterval 对象，表示每天增加一天
        $interval = new DateInterval('P1D'); // P1D 表示 period of 1 day

// 创建一个 DatePeriod 对象，它将生成一个日期序列
        $period = new DatePeriod($startDate, $interval, $endDate->add($interval)); // 注意这里加了$interval是为了包含结束日期

// 遍历 DatePeriod 对象，并打印每一天的日期
        foreach ($period as $day) {
            $date[]= $day->format("Y-m-d"); // 输出格式为 YYYY-MM-DD
        }

        $userList = [];//用户数量
        $orderList = [];//订单数量
        $moneyList = [];//订单金额
        $fenyongList = [];//分佣金额
        if($this->auth->is_qydl == 1){
            $user_ids = model('app\common\model\User')->getUserChilds($this->auth->id,true);
        }else{
            if($this->auth->is_ylgw == 1){
                //获取养老顾问的所有下级
                $user_ids = model('app\common\model\User')->getAllNonAgentDescendantsYlgw($this->auth->id);
            }else{
                $user_ids = model('app\common\model\User')->getUserChilds($this->auth->id,false);
            }
        }
        foreach ($date as $k=>$v){
            $where = [];
            $start_time = strtotime($v);
            $end_time = $start_time + 86400;
            $where['createtime'] = ['between',[$start_time,$end_time]];
            // $list[] = Videolog::where($where)->where('video_id','in',$videoids)->count();
            $userList[] = \app\common\model\User::where($where)->where('parent_id',$this->auth->id)->where('status','normal')->count();

            $service_order_count = model('app\api\model\service\Order')->where('user_id','in',$user_ids)->where('status','in','2,3,4,5')->where($where)->count();
            $shop_order_ids = model('app\api\model\wanlshop\Order')->where('user_id','in',$user_ids)->where('state','in','2,3,4,6')->where($where)->column('id');
            // if($k == 6){
            //     dump(model('app\api\model\wanlshop\Order')->getLastSql());
            //     exit;
            // }
            $orderList[] = $service_order_count+count($shop_order_ids);//下级订单数量

            $service_order_money = model('app\api\model\service\Order')->where('user_id','in',$user_ids)->where('status','not in','0,-1')->where($where)->sum('payprice');
            $shop_order_money = model('app\api\model\wanlshop\OrderGoods')->where('order_id','in',$shop_order_ids)->sum('actual_payment');
            $moneyList[] = $service_order_money + $shop_order_money;

            $fenyongList[] = MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->where($where)->sum('money');
        }
        $series = [
            'user_list'=>[
                'date'=>$date,
                'series'=>[
                    'type'=>'line',
                    'data'=>$userList
                ],
            ],
            'order_list'=>[
                'date'=>$date,
                'series'=>[
                    'type'=>'line',
                    'data'=>$orderList
                ],
            ],
            'money_list'=>[
                'date'=>$date,
                'series'=>[
                    'type'=>'line',
                    'data'=>$moneyList
                ],
            ],
            'fenyong_list'=>[
                'date'=>$date,
                'series'=>[
                    'type'=>'line',
                    'data'=>$fenyongList
                ],

            ],
        ];
        $this->success('ok',['zt_num'=>$zt_num,'dl_num'=>$dl_num,'series'=>$series]);
    }



    /**
     * 招募技师
     * <AUTHOR>
     * @date 2024/9/3  下午4:06
     * @notes
     */
    public function recruitJishi()
    {
        $city = $this->request->post('city');
        $sex = $this->request->post('sex');
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        $ret = Sms::check($mobile, $captcha, 'recruit');
        if (!$ret) {
            $this->error(__('Captcha is incorrect'));
        }
        if(!$city || !$sex || !$mobile || !$captcha){
            $this->error(__('参数错误'));
        }
        $data = [
            'city' => $city,
            'sex' => $sex,
            'mobile' => $mobile,
            'createtime' => time(),
            'name' => $this->request->post('name'),
        ];
        $ret = \app\common\model\RecruitJishi::create($data);
        if($ret){
            $this->success('提交成功');
        }else{
            $this->error('提交失败');
        }
    }

    /**
     * 获取个人用户菜单
     * <AUTHOR>
     * @date 2024/9/3  下午3:37
     * @notes
     */
    public function getUserMenu()
    {
        $where = [];
        // if($this->auth->isLogin()){
        //     if($this->auth->user_type == 0){//普通用户
        //         $where['type'] = 0;
        //     }elseif ($this->auth->user_type == 1){
        //         $where['type'] = ['in','0,1'];
        //     }elseif ($this->auth->user_type == 2){
        //         $where['type'] = ['in','0,1,2'];
        //     }
        // }else{
        //     $where['type'] = 0;
        // }
        $list = \app\common\model\UserMenu::where($where)->where('status',1)->order('weigh desc')->select();
        $this->success('ok',$list);
    }

    /**
     * 邀请记录
     * <AUTHOR>
     * @date 2024/9/3  下午2:58
     * @notes
     */
    public function shareList()
    {
        $user =$this->auth->getUserinfo();
        $user_id2 =$user['qydl_staff']!=0 ? $user['qydl_staff'] :$this->auth->id;
        $list = \app\common\model\User::where('parent_id', $user_id2)->field('id,nickname,mobile,avatar,createtime')->paginate();
        $this->success('', $list);
    }


    public function getSubUserList()
    {
        $sk= $this->request->param('sk');
        $r=UserArea::where('user_id', $this->auth->id)->find();
        $where=[];
        $where2['parent_id'] = $this->auth->id;
        if(!empty($r)) {
            $pids = \app\common\model\User::where(['is_sqdl' => 1])->where($where2)->column('id');
            $userIds = [];
            foreach ($pids as $pid) {
                $userIds += \app\common\model\User::getAllNonAgentDescendants($pid);
            }
            $userIds += \app\common\model\User::where($where2)->column('id');
            if(!empty($sk)) $where['nickname'] = ['mobile', "%$sk%"];
            $list = \app\admin\model\User::whereIn('id', $userIds)->where($where)->field('id,nickname,mobile,avatar,createtime')
                ->paginate();
        }else{
            if($this->auth->is_ylgw == 1){
                $userIds = \app\common\model\User::getAllNonAgentDescendantsYlgw($this->auth->id);
            }else{
                $userIds = \app\common\model\User::getAllNonAgentDescendants($this->auth->id);
            }
            $userIds += \app\common\model\User::where($where2)->column('id');
            if(!empty($sk)) $where['nickname'] = ['mobile', "%$sk%"];
            $list = \app\admin\model\User::whereIn('id', $userIds)->where($where)->field('id,nickname,mobile,avatar,createtime')
                ->paginate();
        }
        $this->success('ok', $list);
    }


    /**
     * 会员中心
     */
    public function index()
    {
        $this->success('', ['welcome' => $this->auth->nickname]);
    }

    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @param string $account  账号
     * @param string $password 密码
     */
    public function login()
    {
        $account = $this->request->post('account');
        $password = $this->request->post('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @param string $mobile  手机号
     * @param string $captcha 验证码
     */
    public function mobilelogin()
    {
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        $is_service = $this->request->post('is_service',1);
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, ['is_service'=>$is_service]);
        }
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @ApiMethod (POST)
     * @param string $username 用户名
     * @param string $password 密码
     * @param string $email    邮箱
     * @param string $mobile   手机号
     * @param string $code     验证码
     */
    public function register()
    {
        $username = $this->request->post('username');
        $password = $this->request->post('password');
        $email = $this->request->post('email');
        $mobile = $this->request->post('mobile');
        $code = $this->request->post('code');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        if ($email && !Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        $ret = Sms::check($mobile, $code, 'register');
        if (!$ret) {
            $this->error(__('Captcha is incorrect'));
        }
        $ret = $this->auth->register($username, $password, $email, $mobile, []);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @ApiMethod (POST)
     * @param string $avatar   头像地址
     * @param string $username 用户名
     * @param string $nickname 昵称
     * @param string $bio      个人简介
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $username = $this->request->post('username');
        $nickname = $this->request->post('nickname');
        $bio = $this->request->post('bio');
        $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
        if ($username) {
            $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Username already exists'));
            }
            $user->username = $username;
        }
        if ($nickname) {
            $exists = \app\common\model\User::where('nickname', $nickname)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Nickname already exists'));
            }
            $user->nickname = $nickname;
        }
        $user->bio = $bio;
        $user->avatar = $avatar;
        $user->save();
        $this->success();
    }

    /**
     * 修改邮箱
     *
     * @ApiMethod (POST)
     * @param string $email   邮箱
     * @param string $captcha 验证码
     */
    public function changeemail()
    {
        $user = $this->auth->getUser();
        $email = $this->request->post('email');
        $captcha = $this->request->post('captcha');
        if (!$email || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if (\app\common\model\User::where('email', $email)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Email already exists'));
        }
        $result = Ems::check($email, $captcha, 'changeemail');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->email = 1;
        $user->verification = $verification;
        $user->email = $email;
        $user->save();

        Ems::flush($email, 'changeemail');
        $this->success();
    }

    /**
     * 修改手机号
     *
     * @ApiMethod (POST)
     * @param string $mobile  手机号
     * @param string $captcha 验证码
     */
    public function changemobile()
    {
        $user = $this->auth->getUser();
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        $result = Sms::check($mobile, $captcha, 'changemobile');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->mobile = 1;
        $user->verification = $verification;
        $user->mobile = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 第三方登录
     *
     * @ApiMethod (POST)
     * @param string $platform 平台名称
     * @param string $code     Code码
     */
    public function third()
    {
        $url = url('user/index');
        $platform = $this->request->post("platform");
        $code = $this->request->post("code");
        $config = get_addon_config('third');
        if (!$config || !isset($config[$platform])) {
            $this->error(__('Invalid parameters'));
        }
        $app = new \addons\third\library\Application($config);
        //通过code换access_token和绑定会员
        $result = $app->{$platform}->getUserInfo(['code' => $code]);
        if ($result) {
            $loginret = \addons\third\library\Service::connect($platform, $result);
            if ($loginret) {
                $data = [
                    'userinfo'  => $this->auth->getUserinfo(),
                    'thirdinfo' => $result
                ];
                $this->success(__('Logged in successful'), $data);
            }
        }
        $this->error(__('Operation failed'), $url);
    }

    /**
     * 重置密码
     *
     * @ApiMethod (POST)
     * @param string $mobile      手机号
     * @param string $newpassword 新密码
     * @param string $captcha     验证码
     */
    public function resetpwd()
    {
        $type = $this->request->post("type", "mobile");
        $mobile = $this->request->post("mobile");
        $email = $this->request->post("email");
        $newpassword = $this->request->post("newpassword");
        $captcha = $this->request->post("captcha");
        $is_service = $this->request->post("is_service",0);
        if (!$newpassword || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        //验证Token
        if (!Validate::make()->check(['newpassword' => $newpassword], ['newpassword' => 'require|regex:\S{6,30}'])) {
            $this->error(__('Password must be 6 to 30 characters'));
        }
        if ($type == 'mobile') {
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
           
            $user = \app\common\model\User::where(['mobile' => $mobile,'is_service'=>$is_service] )->find();
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
        } else {
            if (!Validate::is($email, "email")) {
                $this->error(__('Email is incorrect'));
            }
            $user = \app\common\model\User::getByEmail($email);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Ems::check($email, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Ems::flush($email, 'resetpwd');
        }
        //模拟一次登录
        $this->auth->direct($user->id);
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }

    public function destroy()
    {
        $user = $this->auth->getUser();
        if ($user) {
            // 修改手机号和邮箱，避免唯一键冲突
            $suffix = '_deleted_' . time();
            $user->mobile = $user->mobile . $suffix;
            if ($user->email) {
                $user->email = $user->email . $suffix;
            }
            $user->save();
            
            // 执行软删除
            $user->delete();
        }
        $this->auth->logout();
        $this->success('注销成功');
    }

    /**
     * 根据手机号删除用户所有信息
     * @ApiMethod (POST)
     * @param string $mobile 手机号
     */
    public function deleteUserByMobile()
    {
        $mobile = $this->request->post('mobile');

        if (!$mobile) {
            $this->error(__('手机号不能为空'));
        }

        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('手机号格式不正确'));
        }

        // 查找用户
        $user = Db::name('user')->where('mobile', $mobile)->value('id');
        if (!$user) {
            $this->error(__('用户不存在'));
        }

        $userId = $user;

        // 开启事务
        Db::startTrans();
        try {
            // 删除订单相关信息
            try {
                   $result = Db::table('fa_service_user_info')->where('mobile', $mobile)->delete();
                $this->deleteOrderRelatedData($userId);
            } catch (\Exception $e) {
                throw new \Exception('删除订单相关数据失败: ' . $e->getMessage(), 1001);
            }

            // 删除日志相关信息
            try {
                $this->deleteLogRelatedData($userId);
            } catch (\Exception $e) {
                throw new \Exception('删除日志相关数据失败: ' . $e->getMessage(), 1002);
            }

            // 删除其他相关信息
            try {
                $this->deleteOtherRelatedData($userId);
            } catch (\Exception $e) {
                throw new \Exception('删除其他相关数据失败: ' . $e->getMessage(), 1003);
            }

            // 最后删除用户主表记录
            $result = Db::name('user')->where('id', $userId)->delete();
         
            if (!$result) {
                throw new \Exception('删除用户主表记录失败', 1004);
            }

            // 提交事务
            Db::commit();

            $this->success('用户信息删除成功');

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            //显示详细报错信息
            $errorMsg = '删除失败：';
            if ($e->getMessage()) {
                $errorMsg .= $e->getMessage();
            }
            $errorMsg .= ' (文件: ' . $e->getFile() . ', 行号: ' . $e->getLine() . ')';
            $this->error($errorMsg);
        }
    }

    /**
     * 删除订单相关数据
     * @param int $userId 用户ID
     */
    private function deleteOrderRelatedData($userId)
    {
        // 服务订单相关
        $serviceOrderIds = Db::name('service_order')->where('user_id', $userId)->column('id');
        if (!empty($serviceOrderIds)) {
            Db::name('service_order')->where('user_id', $userId)->delete();
            Db::name('service_order_address')->where('user_id', $userId)->delete();
            Db::name('service_order_detail')->where('user_id', $userId)->delete();
            Db::name('service_order_log')->where('user_id', $userId)->delete();
            Db::name('service_comment')->where('user_id', $userId)->delete();
            Db::name('service_complaint')->where('user_id', $userId)->delete();
            Db::name('service_refund_order')->where('user_id', $userId)->delete();
            Db::name('service_premium_order')->where('user_id', $userId)->delete();
            Db::name('service_add_order')->where('user_id', $userId)->delete();
            Db::name('service_add_order_detail')->where('user_id', $userId)->delete();
        }

        // 商城订单相关
        $wanlshopOrderIds = Db::name('wanlshop_order')->where('user_id', $userId)->column('id');
        if (!empty($wanlshopOrderIds)) {
            Db::name('wanlshop_order')->where('user_id', $userId)->delete();
            Db::name('wanlshop_order_address')->where('user_id', $userId)->delete();
            Db::name('wanlshop_order_goods')->where('order_id', 'in', $wanlshopOrderIds)->delete();
            Db::name('wanlshop_goods_comment')->where('user_id', $userId)->delete();
            Db::name('wanlshop_refund')->where('user_id', $userId)->delete();
            Db::name('wanlshop_pay')->where('user_id', $userId)->delete();
        }

        // 拼团订单相关
        $groupsOrderIds = Db::name('wanlshop_groups_order')->where('user_id', $userId)->column('id');
        if (!empty($groupsOrderIds)) {
            Db::name('wanlshop_groups_order')->where('user_id', $userId)->delete();
            Db::name('wanlshop_groups_order_address')->where('user_id', $userId)->delete();
            Db::name('wanlshop_groups_order_goods')->where('user_id', $userId)->delete();
            Db::name('wanlshop_groups_team')->where('user_id', $userId)->delete();
        }

        // 课程订单相关
        Db::name('xiluedu_course_order')->where('user_id', $userId)->delete();
        Db::name('xiluedu_offline_order')->where('user_id', $userId)->delete();
        Db::name('xiluedu_vip_order')->where('user_id', $userId)->delete();
        Db::name('xiluedu_order_divide')->where('user_id', $userId)->delete();

        // 其他订单
        Db::name('recharge_order')->where('user_id', $userId)->delete();
        Db::name('service_bm_order')->where('user_id', $userId)->delete();
        Db::name('service_agent_order')->where('area_user_id', $userId)->delete();
        
    }

    /**
     * 删除日志相关数据
     * @param int $userId 用户ID
     */
    private function deleteLogRelatedData($userId)
    {
        // 用户相关日志
        Db::name('user_money_log')->where('user_id', $userId)->delete();
        Db::name('user_score_log')->where('user_id', $userId)->delete();

        // 服务相关日志
        Db::name('service_user_money_log')->where('user_id', $userId)->delete();
        Db::name('service_shop_money_log')->where('user_id', $userId)->delete();
        Db::name('service_bm_log')->where('user_id', $userId)->delete();
        Db::name('service_rebate')->where('user_id', $userId)->delete();
        Db::name('service_rebate')->where('from_user_id', $userId)->delete();

        // 课程相关日志
        Db::name('commission_log')->where('commission_user_id', $userId)->delete();
        Db::name('commission_log')->where('buyer_user_id', $userId)->delete();
    }

    /**
     * 删除其他相关数据
     * @param int $userId 用户ID
     */
    private function deleteOtherRelatedData($userId)
    {
        // 用户token
        Db::name('user_token')->where('user_id', $userId)->delete();

        // 地址信息
        Db::name('service_address')->where('user_id', $userId)->delete();
        Db::name('wanlshop_address')->where('user_id', $userId)->delete();

        // 购物车
        Db::name('wanlshop_cart')->where('user_id', $userId)->delete();

        // 申请信息
        Db::name('service_apply_skill')->where('user_id', $userId)->delete();
        Db::name('service_apply_shop')->where('user_id', $userId)->delete();

        // 附件信息
        Db::name('attachment')->where('user_id', $userId)->delete();

        // 投诉举报
        Db::name('wanlshop_complaint')->where('user_id', $userId)->delete();
        Db::name('wanlshop_complaint')->where('complaint_user_id', $userId)->delete();

        // 聊天记录
        Db::name('wanlshop_chat')->where('form_uid', $userId)->delete();
        Db::name('wanlshop_chat')->where('to_id', $userId)->delete();

        // 优惠券
        Db::name('wanlshop_coupon_receive')->where('user_id', $userId)->delete();
        Db::name('service_user_coupon')->where('user_id', $userId)->delete();

        // 收藏和足迹
        $this->safeDeleteTable('wanlshop_collection', 'user_id', $userId);
        Db::name('service_footer_view')->where('user_id', $userId)->delete();
        Db::name('wanlshop_record')->where('user_id', $userId)->delete();
        Db::name('xiluedu_footer_view')->where('user_id', $userId)->delete();

        // 消息相关
        $this->safeDeleteTable('wanlshop_message', 'user_id', $userId);
        $this->safeDeleteTable('wanlshop_message_read', 'user_id', $userId);

        // 提现记录
        $this->safeDeleteTable('wanlshop_withdraw', 'user_id', $userId);
        Db::name('xiluedu_withdraw')->where('user_id', $userId)->delete();

        // 保姆相关
        Db::name('service_bm')->where('user_id', $userId)->delete();

        // 招募记录
        Db::name('recruit_jishi')->where('user_id', $userId)->delete();

        // 大客户信息
        Db::name('big_customer')->where('user_id', $userId)->delete();
        Db::name('big_customer_group')->where('user_id', $userId)->delete();

        // 区域代理配置
        Db::name('user_area')->where('user_id', $userId)->delete();
        Db::name('user_area_price')->where('user_id', $userId)->delete();

        // 分享商品订单
        Db::name('share_goods_order')->where('user_id', $userId)->delete();

        // 发票信息
        Db::name('invoice')->where('user_id', $userId)->delete();

        // 保险记录
        Db::name('service_insurance')->where('user_id', $userId)->delete();

        // 申诉记录
        Db::name('service_shensu')->where('user_id', $userId)->delete();

        // 使用记录
        Db::name('service_use_log')->where('user_id', $userId)->delete();

        // 固定日志
        Db::name('fixed_log')->where('user_id', $userId)->delete();
        Db::name('fixed_log')->where('skill_user_id', $userId)->delete();

        // 充值相关
        Db::name('service_recharge')->where('user_id', $userId)->delete();
        Db::name('service_plus_pay')->where('user_id', $userId)->delete();
        Db::name('service_skill_ensure_pay')->where('user_id', $userId)->delete();
        Db::name('service_shop_ensure_pay')->where('user_id', $userId)->delete();

        // 课程相关
        Db::name('xiluedu_user_course')->where('user_id', $userId)->delete();
        Db::name('xiluedu_user_vip')->where('user_id', $userId)->delete();
    }

    /**
     * 安全删除表数据（如果表不存在则跳过）
     * @param string $tableName 表名
     * @param string $fieldName 字段名
     * @param mixed $value 字段值
     */
    private function safeDeleteTable($tableName, $fieldName, $value)
    {
        try {
            Db::name($tableName)->where($fieldName, $value)->delete();
        } catch (\Exception $e) {
            // 表不存在或其他错误，跳过
            // 可以记录日志：Log::error("删除表 {$tableName} 失败: " . $e->getMessage());
        }
    }

    /**
     * 清理脏数据 - 删除其他表中user表不存在的user_id记录
     * @ApiMethod (POST)
     * @param int $batch_size 批次大小，默认1000
     * @param string $confirm 确认参数，必须为'YES_CLEAN_DIRTY_DATA'
     */
    public function cleanDirtyData()
    {
        $batchSize = $this->request->post('batch_size', 1000);
        $confirm = $this->request->post('confirm');

        if ($confirm !== 'YES_CLEAN_DIRTY_DATA') {
            $this->error('请确认清理操作，传入confirm参数：YES_CLEAN_DIRTY_DATA');
        }

        if ($batchSize < 100 || $batchSize > 5000) {
            $this->error('批次大小必须在100-5000之间');
        }

        // 开启事务
        Db::startTrans();
        try {
            $cleanResult = $this->performDirtyDataClean($batchSize);

            // 提交事务
            Db::commit();

            $this->success('脏数据清理完成', $cleanResult);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error('清理失败：' . $e->getMessage());
        }
    }

    /**
     * 执行脏数据清理
     * @param int $batchSize 批次大小
     * @return array 清理结果
     */
    private function performDirtyDataClean($batchSize)
    {
        $cleanResult = [
            'total_cleaned' => 0,
            'tables_cleaned' => [],
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => '',
        ];

        // 获取所有存在的用户ID
        $existingUserIds = Db::name('user')->column('id');
        $existingUserIdsStr = implode(',', $existingUserIds);

        // 定义需要清理的表和字段映射
        $tablesToClean = $this->getDirtyDataTableConfig();

        foreach ($tablesToClean as $table => $config) {
            $cleanedCount = $this->cleanTableDirtyData($table, $config, $existingUserIdsStr, $batchSize);

            if ($cleanedCount > 0) {
                $cleanResult['tables_cleaned'][$table] = [
                    'description' => $config['description'],
                    'cleaned_count' => $cleanedCount,
                    'user_id_field' => $config['user_id_field']
                ];
                $cleanResult['total_cleaned'] += $cleanedCount;
            }
        }

        $cleanResult['end_time'] = date('Y-m-d H:i:s');

        return $cleanResult;
    }

    /**
     * 获取需要清理的表配置
     * @return array
     */
    private function getDirtyDataTableConfig()
    {
        return [
            // 订单相关表
            'service_order' => [
                'user_id_field' => 'user_id',
                'description' => '服务订单'
            ],
            'service_order_address' => [
                'user_id_field' => 'user_id',
                'description' => '服务订单地址'
            ],
            'service_order_detail' => [
                'user_id_field' => 'user_id',
                'description' => '服务订单详情'
            ],
            'service_order_log' => [
                'user_id_field' => 'user_id',
                'description' => '服务订单日志'
            ],
            'wanlshop_order' => [
                'user_id_field' => 'user_id',
                'description' => '商城订单'
            ],
            'wanlshop_order_address' => [
                'user_id_field' => 'user_id',
                'description' => '商城订单地址'
            ],
            'wanlshop_groups_order' => [
                'user_id_field' => 'user_id',
                'description' => '拼团订单'
            ],
            'wanlshop_groups_order_address' => [
                'user_id_field' => 'user_id',
                'description' => '拼团订单地址'
            ],
            'wanlshop_groups_order_goods' => [
                'user_id_field' => 'user_id',
                'description' => '拼团订单商品'
            ],
            'xiluedu_course_order' => [
                'user_id_field' => 'user_id',
                'description' => '课程订单'
            ],
            'xiluedu_offline_order' => [
                'user_id_field' => 'user_id',
                'description' => '线下课程订单'
            ],
            'xiluedu_vip_order' => [
                'user_id_field' => 'user_id',
                'description' => 'VIP订单'
            ],
            'service_add_order' => [
                'user_id_field' => 'user_id',
                'description' => '附加项目订单'
            ],
            'service_add_order_detail' => [
                'user_id_field' => 'user_id',
                'description' => '附加项目订单详情'
            ],
            'service_premium_order' => [
                'user_id_field' => 'user_id',
                'description' => '补差价订单'
            ],
            'service_refund_order' => [
                'user_id_field' => 'user_id',
                'description' => '退款订单'
            ],
            'recharge_order' => [
                'user_id_field' => 'user_id',
                'description' => '充值订单'
            ],
            'service_bm_order' => [
                'user_id_field' => 'user_id',
                'description' => '保姆订单'
            ],

            // 日志相关表
            'user_money_log' => [
                'user_id_field' => 'user_id',
                'description' => '用户余额日志'
            ],
            'user_score_log' => [
                'user_id_field' => 'user_id',
                'description' => '用户积分日志'
            ],
            'service_user_money_log' => [
                'user_id_field' => 'user_id',
                'description' => '服务者余额日志'
            ],
            'service_shop_money_log' => [
                'user_id_field' => 'user_id',
                'description' => '商户余额日志'
            ],
            'service_bm_log' => [
                'user_id_field' => 'user_id',
                'description' => '保姆次数日志'
            ],
            'service_rebate' => [
                'user_id_field' => 'user_id',
                'description' => '返佣记录'
            ],

            // 地址相关表
            'service_address' => [
                'user_id_field' => 'user_id',
                'description' => '服务地址'
            ],
            'wanlshop_address' => [
                'user_id_field' => 'user_id',
                'description' => '商城地址'
            ],

            // 购物车和收藏
            'wanlshop_cart' => [
                'user_id_field' => 'user_id',
                'description' => '购物车'
            ],
            'wanlshop_collection' => [
                'user_id_field' => 'user_id',
                'description' => '收藏记录'
            ],

            // 评论相关表
            'service_comment' => [
                'user_id_field' => 'user_id',
                'description' => '服务评论'
            ],
            'wanlshop_goods_comment' => [
                'user_id_field' => 'user_id',
                'description' => '商品评论'
            ],

            // 投诉相关表
            'service_complaint' => [
                'user_id_field' => 'user_id',
                'description' => '服务投诉'
            ],
            'wanlshop_complaint' => [
                'user_id_field' => 'user_id',
                'description' => '商城投诉'
            ],

            // Token和认证
            'user_token' => [
                'user_id_field' => 'user_id',
                'description' => '用户Token'
            ],

            // 附件
            'attachment' => [
                'user_id_field' => 'user_id',
                'description' => '附件'
            ],

            // 优惠券
            'wanlshop_coupon_user' => [
                'user_id_field' => 'user_id',
                'description' => '商城优惠券'
            ],
            'service_user_coupon' => [
                'user_id_field' => 'user_id',
                'description' => '服务优惠券'
            ],

            // 聊天和消息
            'wanlshop_chat' => [
                'user_id_field' => 'form_uid',
                'description' => '聊天记录(发送者)'
            ],
            'wanlshop_message' => [
                'user_id_field' => 'user_id',
                'description' => '消息'
            ],
            'wanlshop_message_read' => [
                'user_id_field' => 'user_id',
                'description' => '消息阅读记录'
            ],

            // 浏览记录
            'wanlshop_record' => [
                'user_id_field' => 'user_id',
                'description' => '商城浏览记录'
            ],
            'service_footer_view' => [
                'user_id_field' => 'user_id',
                'description' => '服务浏览记录'
            ],
            'xiluedu_footer_view' => [
                'user_id_field' => 'user_id',
                'description' => '课程浏览记录'
            ],

            // 申请相关表
            'service_apply_skill' => [
                'user_id_field' => 'user_id',
                'description' => '服务者申请'
            ],
            'service_apply_shop' => [
                'user_id_field' => 'user_id',
                'description' => '商户申请'
            ],

            // 提现记录
            'wanlshop_withdraw' => [
                'user_id_field' => 'user_id',
                'description' => '商城提现'
            ],
            'xiluedu_withdraw' => [
                'user_id_field' => 'user_id',
                'description' => '课程提现'
            ],

            // 其他业务表
            'service_bm' => [
                'user_id_field' => 'user_id',
                'description' => '保姆信息'
            ],
            'recruit_jishi' => [
                'user_id_field' => 'user_id',
                'description' => '招募技师'
            ],
            'big_customer' => [
                'user_id_field' => 'user_id',
                'description' => '大客户信息'
            ],
            'big_customer_group' => [
                'user_id_field' => 'user_id',
                'description' => '大客户分组'
            ],
            'user_area' => [
                'user_id_field' => 'user_id',
                'description' => '区域代理配置'
            ],
            'user_area_price' => [
                'user_id_field' => 'user_id',
                'description' => '区域调价记录'
            ],
            'share_goods_order' => [
                'user_id_field' => 'user_id',
                'description' => '分享商品订单'
            ],
            'invoice' => [
                'user_id_field' => 'user_id',
                'description' => '发票'
            ],
            'service_insurance' => [
                'user_id_field' => 'user_id',
                'description' => '保险记录'
            ],
            'service_shensu' => [
                'user_id_field' => 'user_id',
                'description' => '申诉记录'
            ],
            'service_use_log' => [
                'user_id_field' => 'user_id',
                'description' => '使用记录'
            ],
            'fixed_log' => [
                'user_id_field' => 'user_id',
                'description' => '固定日志'
            ],
            'service_recharge' => [
                'user_id_field' => 'user_id',
                'description' => '服务充值'
            ],
            'service_plus_pay' => [
                'user_id_field' => 'user_id',
                'description' => '会员支付'
            ],
            'service_skill_ensure_pay' => [
                'user_id_field' => 'user_id',
                'description' => '服务者保证金'
            ],
            'service_shop_ensure_pay' => [
                'user_id_field' => 'user_id',
                'description' => '商户保证金'
            ],
            'xiluedu_course_user' => [
                'user_id_field' => 'user_id',
                'description' => '课程用户'
            ],
            'xiluedu_offline_apply' => [
                'user_id_field' => 'user_id',
                'description' => '线下课程申请'
            ],
            'xiluedu_vip_user' => [
                'user_id_field' => 'user_id',
                'description' => 'VIP用户'
            ],
            'xiluedu_commission_log' => [
                'user_id_field' => 'user_id',
                'description' => '课程佣金日志'
            ],
            'xiluedu_order_divide' => [
                'user_id_field' => 'user_id',
                'description' => '课程订单分佣'
            ],
        ];
    }

    /**
     * 清理单个表的脏数据
     * @param string $table 表名
     * @param array $config 表配置
     * @param string $existingUserIdsStr 存在的用户ID字符串
     * @param int $batchSize 批次大小
     * @return int 清理的记录数
     */
    private function cleanTableDirtyData($table, $config, $existingUserIdsStr, $batchSize)
    {
        $userIdField = $config['user_id_field'];
        $totalCleaned = 0;

        // 检查表是否存在
        $tableExists = Db::query("SHOW TABLES LIKE 'fa_{$table}'");
        if (empty($tableExists)) {
            return 0;
        }

        // 检查字段是否存在
        $fieldExists = Db::query("SHOW COLUMNS FROM `fa_{$table}` LIKE '{$userIdField}'");
        if (empty($fieldExists)) {
            return 0;
        }

        do {
            // 查找脏数据ID
            $sql = "SELECT id FROM `fa_{$table}`
                    WHERE `{$userIdField}` IS NOT NULL
                    AND `{$userIdField}` != 0
                    AND `{$userIdField}` NOT IN ({$existingUserIdsStr})
                    LIMIT {$batchSize}";

            $dirtyIds = Db::query($sql);

            if (!empty($dirtyIds)) {
                $idsToDelete = array_column($dirtyIds, 'id');
                $deletedCount = Db::name($table)->where('id', 'in', $idsToDelete)->delete();
                $totalCleaned += $deletedCount;

                // 如果删除的记录数少于批次大小，说明已经清理完毕
                if (count($dirtyIds) < $batchSize) {
                    break;
                }
            }
        } while (!empty($dirtyIds));

        return $totalCleaned;
    }

    /**
     * 检查脏数据统计 - 不执行删除，只统计
     * @ApiMethod (POST)
     */
    public function checkDirtyData()
    {
        // 获取所有存在的用户ID
        $existingUserIds = Db::name('user')->column('id');
        $existingUserIdsStr = implode(',', $existingUserIds);

        // 定义需要检查的表和字段映射
        $tablesToCheck = $this->getDirtyDataTableConfig();

        $checkResult = [
            'total_dirty_records' => 0,
            'tables_with_dirty_data' => [],
            'check_time' => date('Y-m-d H:i:s'),
            'total_user_count' => count($existingUserIds),
        ];

        foreach ($tablesToCheck as $table => $config) {
            $dirtyCount = $this->countTableDirtyData($table, $config, $existingUserIdsStr);

            if ($dirtyCount > 0) {
                $checkResult['tables_with_dirty_data'][$table] = [
                    'description' => $config['description'],
                    'dirty_count' => $dirtyCount,
                    'user_id_field' => $config['user_id_field']
                ];
                $checkResult['total_dirty_records'] += $dirtyCount;
            }
        }

        $this->success('脏数据检查完成', $checkResult);
    }

    /**
     * 统计单个表的脏数据数量
     * @param string $table 表名
     * @param array $config 表配置
     * @param string $existingUserIdsStr 存在的用户ID字符串
     * @return int 脏数据数量
     */
    private function countTableDirtyData($table, $config, $existingUserIdsStr)
    {
        $userIdField = $config['user_id_field'];

        // 检查表是否存在
        $tableExists = Db::query("SHOW TABLES LIKE 'fa_{$table}'");
        if (empty($tableExists)) {
            return 0;
        }

        // 检查字段是否存在
        $fieldExists = Db::query("SHOW COLUMNS FROM `fa_{$table}` LIKE '{$userIdField}'");
        if (empty($fieldExists)) {
            return 0;
        }

        try {
            $sql = "SELECT COUNT(*) as count FROM `fa_{$table}`
                    WHERE `{$userIdField}` IS NOT NULL
                    AND `{$userIdField}` != 0
                    AND `{$userIdField}` NOT IN ({$existingUserIdsStr})";

            $result = Db::query($sql);
            return $result[0]['count'] ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
