<?php

return [
    'User_id'          => '用户',
    'Order_no'         => '订单号',
    'Course_id'        => '课程id',
    'Pay_type'         => '支付方式',
    'Pay_type 0'       => '无',
    'Pay_type 1'       => '微信',
    'Pay_type 2'       => '余额',
    'Total_price'      => '总价格',
    'Pay_price'        => '支付价格',
    'Pay_score'        => '支付积分',
    'Favourable_price' => '优惠金额',
    'Wxconfig'         => '微信支付配置',
    'Pay_status'       => '支付状态',
    'Pay_status 1'     => '待支付',
    'Pay_status 2'     => '已支付',
    'Paytime'          => '支付时间',
    'Createtime'       => '创建时间',
    'Updatetime'       => '更新时间',
    'Notify'           => '进入回调',
    'Notify 0'         => '未进入',
    'Notify 1'         => '进入',
    'Is_send'          => '是否发货',
    'Is_send 0'        => '否',
    'Is_send 1'        => '是',
    'Send'             => '发货'
];
