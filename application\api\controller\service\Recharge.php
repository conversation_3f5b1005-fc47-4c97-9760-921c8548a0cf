<?php
namespace app\api\controller\service;

use app\api\model\service\Goods;
use app\api\model\service\MoneyLog;
use app\api\model\service\OrderLog;
use app\common\controller\Api;
use app\common\model\User as UserModel;
use fast\Random;
use think\Db;
use app\common\model\Recharge as RechargeModel;
use think\Exception;


/**
 * 首页接口
 */
class Recharge extends Api
{
    protected $noNeedLogin = [''];
    protected $noNeedRight = ['*'];


    /**
     * 获取充值配置
     * <AUTHOR>
     * @date 2024/8/23  下午4:18
     * @notes
     */
    public function rechargeInfo()
    {
        $list = RechargeModel::where(['status'=>'normal'])->select();
        foreach ($list as &$v){
            $shop_coupon = model('app\api\model\wanlshop\Coupon')->where('id','in',$v['wanlshop_coupon_ids'])->field('id,rangetype as type,price')->select();
            foreach ($shop_coupon as &$v1){
                if($v1['type'] == 'all'){
                    $v1['desc'] = '全场通用';
                }else{
                    $v1['desc'] = '指定商品';
                }
            }
            $service_coupon = model('app\api\model\service\Coupon')->where('id','in',$v['service_coupon_ids'])->field('id,type,reduce as  price')->select();
            foreach ($service_coupon as &$v2){
                if($v2['type'] == '0'){
                    $v2['desc'] = '全场通用';
                }else{
                    $v2['desc'] = '指定服务';
                }
            }
            $v['coupon'] = array_merge($shop_coupon,$service_coupon);
        }
        $this->success('信息返回成功',$list);
    }

    /**
     * 用户充值余额
     * @return void
     */
    public function recharge()
    {
        $id = input('id/d',0);
        $recharge = RechargeModel::get($id);
        if(!$recharge){
            $this->error('充值配置不存在');
        }
        $uid = $this->auth->id;
        $money = model('app\api\model\service\User')->where('id',$uid)->value('money');
            $price = $recharge->money;
            $price <=0 && $this->error('充值金额错误');
            $orderId = 'Recharge'.Random::alnum(5).'-'.$uid.'-'.time();
            $paytype = input('paytype/d',0);
            $re = '';
            Db::startTrans();
            try{
                $recharge = new \app\api\model\service\Recharge(['user_id'=>$uid,'service_coupon_ids'=>$recharge->service_coupon_ids,'wanlshop_coupon_ids'=>$recharge->wanlshop_coupon_ids,'price'=>$price,'orderId'=>$orderId,'paytype'=>$paytype]);
                $recharge->allowField(true)->save();
                $re = \addons\service\library\Pay::payOrder(['amount'=>$price,'orderid'=>$orderId,'title'=>'充值余额'],$paytype,$uid,0);
                Db::commit();

            } catch (Exception $e) {
                Db::rollback();
                $this->error('支付拉起失败',$e->getMessage());
            }
            $this->success('支付信息返回成功',$re);

        $this->success('信息返回成功',$money);
    }


    /**
     * 充值记录列表
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function list()
    {
        $uid = $this->auth->id;
        $page = input('page/d',1);
        $list = model('app\api\model\service\Recharge')
            ->where(['user_id'=>$uid,'state'=>1])
            ->field('id,price,orderId,paytype,createtime')
            ->order('id desc')
            ->page($page)
            ->limit(10)
            ->select();
        $this->success('信息返回成功',$list);
    }

    /**
     * 余额日志
     * @return string
     */
    public function moneylog()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $where = [];
            $type = $this->request->post('type');
            if($type == 1){//收入
                $where['money'] = ['>',0];
            }else if($type == 2){//支出
                $where['money'] = ['<',0];
            }
            $month = $this->request->post('month');
            if($month){
                $where['createtime'] = ['between',[strtotime($month.'-01'),strtotime($month.'-31')]];
            }

            $list = model('app\common\model\MoneyLog')
                ->where('user_id', $this->auth->id)
                ->order('createtime desc')
                ->where($where)
                ->paginate()->each(function ($item){
                    $item['type'] = $item['money'] < 0 ? '支出' : '收入';
                    $item['createtime'] = date('Y-m-d H:i:s',$item['createtime']);
                    return $item;
                });
            $this->success('ok',$list);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 结算明细
     * <AUTHOR>
     * @date 2024/9/21  下午2:10
     * @notes
     */
    public function jiesuan()
    {
        $month = $this->request->param('month',date('Y-m'));
        $start_time = $this->request->param('start_time');
        $end_time = $this->request->param('end_time');
        $type = $this->request->param('type');

        $wjs_money = 0;
        $yjs_money = 0;

        $list = [];
        $where = [];
        $where['createtime'] = ['between',[strtotime($month.'-01'),strtotime($month.'-31')]];
        if($start_time && $end_time){
            $where['createtime'] = ['between',[strtotime($start_time." 00:00:00"),strtotime($end_time. ' 23:59:59')]];
        }
        /**
         * 查询城市运营商待结算信息end
         */
        //获取此城市运营商代理区域
        $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
        $sq_list = [];
        $qy_wjs_money = 0;
        $service_money = 0;
        $list = [];
        if($district){
            $qy_list = model('app\api\model\service\Order')->alias('o')
                ->join('service_order_address d','d.order_id=o.id','left')
                ->where('o.user_id',$this->auth->id)
//                ->where('o.status','between',[2,6])
                ->where('o.status','>',1)
                ->where('o.user_confirm',0)
                ->where('d.district',$district)
                ->where('o.createtime','between',$where['createtime'][1])
                ->field('o.id,o.goods_id,o.payprice,o.createtime,o.quyu_money,o.shequ_money,o.service_money')->order('createtime desc')->select();
            foreach ($qy_list as &$row){
                $goods = Goods::where('id',$row['goods_id'])->field('id,name,shequ_bili,quyu_bili,service_bili')->find();
                $lirun = $row['payprice'] - $goods['service_bili'];
                $qy_money = $row['quyu_money'];

                $money=MoneyLog::getProfit($row['id']);
                $qy_wjs_money += $money['qy'];
                $service_money += $money['service'];
                $order = [];
                $order['id'] = $row['id'];
                $order['name'] = $goods['name'];
                $order['money'] = $money['qy'];
                $order['createtime'] = date('Y-m-d H:i:s',$row['createtime']);
                $list[] = $order;
            }
        }
        // $child_user = model('app\common\model\User')->getUserChilds($this->auth->id,true);
        $child_user = UserModel::getAllNonAgentDescendantsQy($this->auth->id);;
        //商城未结算订单
        $shop_list = model('app\api\model\wanlshop\OrderGoods')->alias('goods')
            ->join('wanlshop_order order','order.id=goods.order_id','left')
            ->field('order.id,goods.title,goods.goods_id,goods.actual_payment,goods.cost_price,order.createtime,goods.quyu_d_bili')
            ->where('order.createtime','between',$where['createtime'][1])
            ->where('user_id','in',$child_user)
            ->where('state','in','2,3')
            ->order('id desc')
            ->select();
        $shop_money = 0;
        foreach ($shop_list as &$row){
            $goods = model('app\api\model\wanlshop\Goods')::where('id',$row['goods_id'])->field('id,title,shequ_bili,quyu_bili')->find();
            // $lirun = $row['actual_payment'] - $row['cost_price'];
            // $qy_money = 0;
            // if($lirun > 0){
            //     if($goods['quyu_bili']){
            //         $qy_money = truncateDecimal($lirun * ($goods['quyu_bili'] / 100));
            //     }else{
            //         $qy_money = truncateDecimal($lirun * (config('site.quyu_bili') / 100));
            //     }
            // }
            $qy_money = $row['quyu_d_bili'];
            $qy_wjs_money += $qy_money;
            $shop_money += $qy_money;
            $order = [];
            $order['name'] = $goods['title'] ?? "";
            $order['money'] = $qy_money;
            $order['createtime'] = date('Y-m-d H:i:s',$row['createtime']);
            $list[] = $order;
        }

        $yjs_money = MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->where($where)->sum('money');
        if($type != 1){//已结算
            $list = MoneyLog::where('user_id',$this->auth->id)->field('memo as name,money,createtime')->where('type','fenyong')->where($where)->order('id desc')->select();
            foreach ($list as &$row){
                $row['createtime'] = date('Y-m-d H:i:s',$row['createtime']);
            }
        }
        $shop_money=$shop_money+MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->where('service_type',0)->sum('money');
        $service_money=$service_money+MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->where('service_type',2)->sum('money');
        $res = [
            'wjs_money'=>round($qy_wjs_money,2),
            'yjs_money'=>round($yjs_money,2),
            'shop_money'=>round($shop_money,2),
            'service_money'=>round($service_money,2),
            'list'=>$list
        ];
        $this->success('ok',$res);
    }

    /**
     * 获取养老院长结算明细
     * <AUTHOR>
     * @date 2024/10/24  上午11:48
     * @notes
     */
    public function getSqJiesuan()
    {
        $month = $this->request->param('month',date('Y-m'));
        $start_time = $this->request->param('start_time');
        $end_time = $this->request->param('end_time');
        $type = $this->request->param('type');
        $user_type = $this->request->param('user_type');

        $wjs_money = 0;
        $yjs_money = 0;

        $list = [];
        $where = [];
        $where['createtime'] = ['between',[strtotime($month.'-01'),strtotime($month.'-31')]];
        if($start_time && $end_time){
            $where['createtime'] = ['between',[strtotime($start_time),strtotime($end_time . ' 23:59:59')]];
        }

        //查询养老院长下级
        $child_user = UserModel::getAllNonAgentDescendants($this->auth->id);

        $sq_list = model('app\api\model\service\Order')
            ->where($where)
            ->where('user_id','in',$child_user)
            ->where('status','in','2,3,4,5')
            ->where('user_confirm',0)
            ->field('goods_id,payprice')
            ->order('createtime','desc')
            ->select();
        $sq_wjs_money = 0;
        $service_money = 0;
        $list = [];
        foreach ($sq_list as &$row){
            $goods = Goods::where('id',$row['goods_id'])->field('id,name,shequ_bili,quyu_bili,service_bili')->find();
            $lirun = $row['payprice'] - $goods['service_bili'];
            $sq_money = 0;
            if($lirun > 0){
                if($goods['shequ_bili']){
                    $sq_money = truncateDecimal($lirun * ($goods['shequ_bili'] / 100));
                }else{
                    $sq_money = truncateDecimal($lirun * (config('site.shequ_bili') / 100));
                }
            }
            $service_money += $sq_money;
            $sq_wjs_money += $sq_money;
            $order = [];
            $order['name'] = $goods['name'];
            $order['money'] = $sq_money;
            $order['createtime'] = date('Y-m-d H:i:s',$row['createtime']);
            $list[] = $order;
        }
        //商城未结算订单
        $shop_list = model('app\api\model\wanlshop\OrderGoods')->alias('goods')
            ->join('wanlshop_order order','order.id=goods.order_id','left')
            ->field('order.id,goods.title,goods.goods_id,goods.actual_payment,goods.cost_price,order.createtime,goods.shequ_d_bili,goods.quyu_d_bili')
            ->where('order.createtime','between',[strtotime($month.'-01'),strtotime($month.'-31')])
            ->where('user_id','in',$child_user)
            ->where('state','in','2,3')
            ->order('id desc')
            ->select();
        $shop_money = 0;
        foreach ($shop_list as &$row){
            $goods = model('app\api\model\wanlshop\Goods')::where('id',$row['goods_id'])->field('id,title,shequ_bili,quyu_bili')->find();
            $lirun = $row['actual_payment'] - $row['cost_price'];
            // $sq_money = 0;
            // if($lirun > 0){
            //     if($goods['shequ_bili']){
            //         $sq_money = truncateDecimal($lirun * ($goods['shequ_bili'] / 100));
            //     }else{
            //         $sq_money = truncateDecimal($lirun * (config('site.shequ_bili') / 100));
            //     }
            // }
//            if($this->auth->is_sqdl == 1 ) {
                $sq_money = $row['shequ_d_bili'];
                $shop_money += $sq_money;
                $sq_wjs_money += $sq_money;
//            }
//            if($this->auth->is_qydl == 1 ) {
//                $sq_money = $row['quyu_d_bili'];
//                $shop_money += $sq_money;
//                $sq_wjs_money += $sq_money;
//            }

            $order = [];
            $order['name'] = $goods['title'];
            $order['money'] = $sq_money;
            $order['createtime'] = date('Y-m-d H:i:s',$row['createtime']);
            $list[] = $order;
        }

        $yjs_money = MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->sum('money');
        if($type != 1){//已结算
            $list = MoneyLog::where('user_id',$this->auth->id)->field('memo as name,money,createtime')->where('type','fenyong')->order('createtime desc')->where($where)->select();
            foreach ($list as &$row){
                $row['createtime'] = date('Y-m-d H:i:s',$row['createtime']);
            }
        }

        $shop_money+=MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->where('service_type',0)->sum('money');
        $service_money+=MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->where('service_type',2)->sum('money');
        $res = [
            'wjs_money'=>round($sq_wjs_money,2),
            'yjs_money'=>round($yjs_money,2),
            'shop_money'=>round($shop_money,2),
            'service_money'=>round($service_money,2),
            'list'=>$list
        ];
        $this->success('ok',$res);
    }


    public function serviceWallet()
    {
        $uid=$this->auth->id;
        $user=\app\common\model\User::get($uid);
        $service= \app\api\model\service\Skill::where('user_id',$uid)->find();
        $djs_money=MoneyLog::serviceDjs($service->id);
        $data=[
            'money'=>$user['money'],
            'ensure_price'=>$service['ensure_price'],
            'djs_money'=>sprintf("%.2f", $djs_money),
            'total' => sprintf("%.2f",$user['money']+$service['ensure_price'] +$djs_money)
        ];
        $this->success('ok',$data);

    }

    public function serviceMoneyDayList(){
        $uid=$this->auth->id;
//        $user=\app\common\model\User::get($uid);
        $start_time = $this->request->param('start_time');
        $end_time = $this->request->param('end_time');
        $service= \app\api\model\service\Skill::where('user_id',$uid)->find();

        $orders=\app\api\model\service\Order::where('skill_id',$service->id)
            ->field("DATE(FROM_UNIXTIME(createtime)) as date,sum(service_money) as money,count(id) as order_num")
            ->where('')
            ->where('user_confirm','=',1)
            ->order('id','desc')
            ->group('date')
            ->select();

        $this->success('ok',$orders);

    }

    public function serviceMoneyDayDetail(){
        $uid=$this->auth->id;
        $day=$this->request->param('day',date('Y-m-d'));
        $where['createtime'] = ['between',[strtotime($day),strtotime($day. ' 23:59:59')]];
        $service= \app\api\model\service\Skill::where('user_id',$uid)->find();
        $orders=\app\api\model\service\Order::where('skill_id',$service->id)
            ->where($where)
            ->order('id','desc')
            ->select();
        foreach ($orders as &$row){
            if($row['user_confirm']==0){
                $row['service_money']=MoneyLog::getProfit($row['id'])['service'];
            }
            $row['goods_name']=Goods::where('id',$row['goods_id'])->value('name');
        }
        $result=[];
        $yjs_money=$djs_money=$night_money=0;
        foreach ($orders as &$row){
            if($row['user_confirm']==1) {
                $yjs_money += $row['service_money'];
            }else{
                $m=MoneyLog::getProfit($row['id'])['service'];
                $djs_money += $m;
                $row['service_money'] = sprintf("%.2f", $m);
            }
            $night_money+=$row['night_money'];
        }
        $result['yjs_money']=sprintf("%.2f", $yjs_money);
        $result['djs_money']=sprintf("%.2f", $djs_money);
        $result['night_money']=$night_money;
        $result['order_list']=$orders;
        $this->success('ok',$result);

    }


    public function serviceMoneyList(){
        $uid=$this->auth->id;
        $user=\app\common\model\User::get($uid);
        $year=$this->request->param('year',date('Y'));
        $month=$this->request->param('month',date('m'));
        $start_date=date("Y-m-d", strtotime("{$year}-{$month}-01"));
        $end_date=date("Y-m-d", strtotime("{$year}-{$month}-" . date("t", mktime(0, 0, 0, $month, 1, $year))));

        $service= \app\api\model\service\Skill::where('user_id',$uid)->find();
        $page = $this->request->param('page',1);

        $orders=\app\api\model\service\Order::where('skill_id',$service->id)
            ->where('status','>',1)
            ->where( 'createtime','between',[strtotime($start_date),strtotime($end_date. ' 23:59:59')])
            ->order('id','desc')
            ->page($page,10)->select();

        foreach ($orders as &$row){
            if($row['user_confirm']==0){
                $row['service_money']=MoneyLog::getProfit($row['id'])['service'];
            }
            $row['goods_name']=Goods::where('id',$row['goods_id'])->value('name');

        }
        $this->success('ok',$orders);
    }


    public function serviceMoneyLog()
    {
        $type=$this->request->param('type');
        $year=$this->request->param('year');
        $month=$this->request->param('month');
        $start_date=date("Y-m-d", strtotime("{$year}-{$month}-01"));
        $end_date=date("Y-m-d", strtotime("{$year}-{$month}-" . date("t", mktime(0, 0, 0, $month, 1, $year))));
        $list=MoneyLog::getServiceList($this->auth->id,$start_date,$end_date,$type);
        $this->success('ok',$list);
    }

    public function serviceMoneyCount()
    {
        $year=$this->request->param('year');
        $month=$this->request->param('month');
        $start_date=date("Y-m-d", strtotime("{$year}-{$month}-01"));
        $end_date=date("Y-m-d", strtotime("{$year}-{$month}-" . date("t", mktime(0, 0, 0, $month, 1, $year))));
        $list=MoneyLog::getServiceCount($this->auth->id,$start_date,$end_date);
        $this->success('ok',$list);
    }

    /**
     * 服务者我的钱包
     * <AUTHOR>
     * @date 2024/9/21  下午2:10
     * @notes
     */
    public function money()
    {
        $month = $this->request->param('month',date('Y-m'));
        $start_time = $this->request->param('start_time');
        $end_time = $this->request->param('end_time');
        $type = $this->request->param('type');

        $wjs_money = 0;
        $yjs_money = 0;
        $list = [];

        $where = [];
        $where['createtime'] = ['between',[strtotime($month.'-01'),strtotime($month.'-31')]];
        if($start_time && $end_time){
            $where['createtime'] = ['between',[strtotime($start_time),strtotime($end_time. ' 23:59:59')]];
        }

        /**
         * 查询城市运营商待结算信息end
         */
        //获取此城市运营商代理区域
        $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
        $sq_list = [];
        $qy_wjs_money = 0;
        $service_money = 0;
        $list = [];
        if($district){
            $qy_list = model('app\api\model\service\Order')->alias('o')
                ->join('service_order_address d','d.order_id=o.id','left')
                ->where('o.user_id',$this->auth->id)
//                ->where('o.status','between',[2,6])
                ->where('o.status','>',1)
                ->where('o.user_confirm',0)
                ->where('d.district',$district)
                ->field('o.id,o.goods_id,o.payprice,o.createtime,o.quyu_money,o.shequ_money,o.service_money')->select();
            foreach ($qy_list as &$row){
                $goods = Goods::where('id',$row['goods_id'])->field('id,name,shequ_bili,quyu_bili,service_bili')->find();
                $lirun = $row['payprice'] - $goods['service_bili'];
                $qy_money = $row['quyu_money'];

                $money=$this->getProfit($row['id']);
                $qy_wjs_money += $money['qy'];
                $service_money += $money['service'];
                $order = [];
                $order['id'] = $row['id'];
                $order['name'] = $goods['name'];
                $order['money'] = $money['qy'];
                $order['createtime'] = date('Y-m-d H:i:s',$row['createtime']);
                $list[] = $order;
            }
        }
        // $child_user = model('app\common\model\User')->getUserChilds($this->auth->id,true);
        $child_user = UserModel::getAllNonAgentDescendantsQy($this->auth->id);;
        //商城未结算订单
        $shop_list = model('app\api\model\wanlshop\OrderGoods')->alias('goods')
            ->join('wanlshop_order order','order.id=goods.order_id','left')
            ->field('order.id,goods.title,goods.goods_id,goods.actual_payment,goods.cost_price,order.createtime,goods.quyu_d_bili')
            ->where('order.createtime','between',[strtotime($month.'-01'),strtotime($month.'-31')])
            ->where('user_id','in',$child_user)
            ->where('state','in','2,3')
            ->select();
        $shop_money = 0;
        foreach ($shop_list as &$row){
            $goods = model('app\api\model\wanlshop\Goods')::where('id',$row['goods_id'])->field('id,title,shequ_bili,quyu_bili')->find();
            $qy_money = $row['quyu_d_bili'];
//            $qy_wjs_money += $qy_money;
            $shop_money += $qy_money;
            $order = [];
            $order['name'] = $goods['title'];
            $order['money'] = $qy_money;
            $order['createtime'] = date('Y-m-d H:i:s',$row['createtime']);
            $list[] = $order;
        }

        $yjs_money = MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->where($where)->sum('money');
        if($type != 1){//已结算
            $list = MoneyLog::where('user_id',$this->auth->id)->field('memo as name,money,createtime')->where('type','fenyong')->where($where)->select();
            foreach ($list as &$row){
                $row['createtime'] = date('Y-m-d H:i:s',$row['createtime']);
            }
        }
        $shop_money=$shop_money+MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->where('service_type',0)->sum('money');
        $service_money=$service_money+MoneyLog::where('user_id',$this->auth->id)->where('type','fenyong')->where('service_type',2)->sum('money');
        $res = [
            'wjs_money'=>round($qy_wjs_money,2),
            'yjs_money'=>round($yjs_money,2),
            'shop_money'=>round($shop_money,2),
            'service_money'=>round($service_money,2),
            'list'=>$list
        ];
        $this->success('ok',$res);
    }
    /**
     * 服务质量分记录
     * <AUTHOR>
     * @date 2024/9/27  下午6:34
     * @notes
     */
    public function serviceScoreLog()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $where = [];
            $type = $this->request->post('type');
            if($type) $where['type']=$type;
//            if($type == 1){//收入
//                $where['score'] = ['>',0];
//            }else if($type == 2){//支出
//                $where['score'] = ['<',0];
//            }
            $month = $this->request->post('month');
//            if($month){
//                $where['createtime'] = ['between',[strtotime($month.'-01'),strtotime($month.'-31')]];
//            }
            $list = model('app\common\model\service\ScoreLog')
                ->where('user_id', $this->auth->id)
                ->order('createtime desc')
                ->where($where)
                ->paginate()->each(function ($item){
//                    $item['type'] = $item['money'] < 0 ? '支出' : '收入';
                    $item['createtime'] = date('Y-m-d H:i:s',$item['createtime']);
                    return $item;
                });
            $this->success('ok',$list);
        }
        $this->error(__('非法请求'));
    }




}
