<?php
/**
 * 测试养老顾问上级调整逻辑
 */

// 模拟用户数据
$users = [
    1 => ['id' => 1, 'nickname' => '平台', 'parent_id' => 0, 'is_ylgw' => 0, 'is_sqdl' => 0, 'is_qydl' => 0],
    2001 => ['id' => 2001, 'nickname' => '城市运营商A', 'parent_id' => 0, 'is_ylgw' => 0, 'is_sqdl' => 0, 'is_qydl' => 1],
    2002 => ['id' => 2002, 'nickname' => '养老院长B', 'parent_id' => 2001, 'is_ylgw' => 0, 'is_sqdl' => 1, 'is_qydl' => 0],
    2003 => ['id' => 2003, 'nickname' => '养老顾问C', 'parent_id' => 2002, 'is_ylgw' => 1, 'is_sqdl' => 0, 'is_qydl' => 0],
    2004 => ['id' => 2004, 'nickname' => '养老顾问D', 'parent_id' => 2003, 'is_ylgw' => 1, 'is_sqdl' => 0, 'is_qydl' => 0],
    2005 => ['id' => 2005, 'nickname' => '普通用户E', 'parent_id' => 2004, 'is_ylgw' => 0, 'is_sqdl' => 0, 'is_qydl' => 0],
    2006 => ['id' => 2006, 'nickname' => '普通用户F', 'parent_id' => 2001, 'is_ylgw' => 0, 'is_sqdl' => 0, 'is_qydl' => 0],
    2007 => ['id' => 2007, 'nickname' => '普通用户G', 'parent_id' => 0, 'is_ylgw' => 0, 'is_sqdl' => 0, 'is_qydl' => 0],
];

// 模拟查找最高级别上级的函数
function findTopLevelParent($user_id, $users) {
    $max_depth = 10;
    $current_depth = 0;
    $current_user_id = $user_id;
    $path = [];
    
    while ($current_depth < $max_depth) {
        if (!isset($users[$current_user_id])) {
            echo "用户不存在 - 用户ID: {$current_user_id}\n";
            return $user_id;
        }
        
        $user = $users[$current_user_id];
        $path[] = $user['nickname'] . "({$user['id']})";
        
        // 如果当前用户是养老院长或城市运营商，返回这个用户
        if ($user['is_sqdl'] == 1 || $user['is_qydl'] == 1) {
            $role = $user['is_sqdl'] == 1 ? '养老院长' : '城市运营商';
            echo "找到最终上级 - 用户ID: {$user['id']}, 角色: {$role}, 昵称: {$user['nickname']}\n";
            echo "查找路径: " . implode(' -> ', $path) . "\n";
            return $user['id'];
        }
        
        // 如果当前用户是养老顾问，继续向上查找
        if ($user['is_ylgw'] == 1) {
            if (!$user['parent_id'] || $user['parent_id'] == 0) {
                echo "养老顾问没有上级，返回平台 - 用户ID: {$user['id']}\n";
                echo "查找路径: " . implode(' -> ', $path) . " -> 平台\n";
                return 0;
            }
            
            echo "继续向上查找 - 当前用户ID: {$user['id']} (养老顾问), 上级ID: {$user['parent_id']}\n";
            $current_user_id = $user['parent_id'];
            $current_depth++;
            continue;
        }
        
        // 如果是普通用户，返回这个用户
        echo "遇到普通用户，保持原有上级 - 用户ID: {$user['id']}\n";
        echo "查找路径: " . implode(' -> ', $path) . "\n";
        return $user['id'];
    }
    
    echo "查找超过最大深度，返回原始上级 - 起始用户ID: {$user_id}\n";
    return $user_id;
}

// 测试场景
$test_cases = [
    [
        'name' => '场景1：普通用户E要开通养老顾问，当前上级是养老顾问D',
        'user_id' => 2005,
        'description' => '用户E -> 养老顾问D -> 养老顾问C -> 养老院长B -> 城市运营商A',
        'expected' => '应该调整为养老院长B(2002)'
    ],
    [
        'name' => '场景2：普通用户F要开通养老顾问，当前上级是城市运营商A',
        'user_id' => 2006,
        'description' => '用户F -> 城市运营商A',
        'expected' => '保持城市运营商A(2001)'
    ],
    [
        'name' => '场景3：普通用户G要开通养老顾问，没有上级',
        'user_id' => 2007,
        'description' => '用户G -> 无上级',
        'expected' => '保持无上级(0)'
    ],
    [
        'name' => '场景4：养老顾问C的上级调整',
        'user_id' => 2003,
        'description' => '养老顾问C -> 养老院长B -> 城市运营商A',
        'expected' => '保持养老院长B(2002)'
    ]
];

echo "=== 养老顾问上级调整逻辑测试 ===\n\n";

echo "用户关系图:\n";
echo "平台(1)\n";
echo "└── 城市运营商A(2001)\n";
echo "    ├── 养老院长B(2002)\n";
echo "    │   └── 养老顾问C(2003)\n";
echo "    │       └── 养老顾问D(2004)\n";
echo "    │           └── 普通用户E(2005) [要开通养老顾问]\n";
echo "    └── 普通用户F(2006) [要开通养老顾问]\n";
echo "普通用户G(2007) [要开通养老顾问，无上级]\n\n";

foreach ($test_cases as $index => $case) {
    echo "=== {$case['name']} ===\n";
    echo "描述: {$case['description']}\n";
    echo "预期结果: {$case['expected']}\n";
    echo "实际执行:\n";
    
    $user = $users[$case['user_id']];
    echo "用户: {$user['nickname']}({$user['id']}), 当前上级: {$user['parent_id']}\n";
    
    if (!$user['parent_id'] || $user['parent_id'] == 0) {
        echo "用户没有上级，保持不变: 0\n";
        $final_parent = 0;
    } else {
        echo "开始查找最终上级...\n";
        $final_parent = findTopLevelParent($user['parent_id'], $users);
    }
    
    echo "最终上级: {$final_parent}\n";
    
    if ($final_parent != $user['parent_id']) {
        $old_parent_name = $user['parent_id'] ? $users[$user['parent_id']]['nickname'] : '无';
        $new_parent_name = $final_parent ? $users[$final_parent]['nickname'] : '平台';
        echo "需要调整: {$old_parent_name}({$user['parent_id']}) -> {$new_parent_name}({$final_parent})\n";
    } else {
        echo "无需调整，保持原有上级\n";
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

echo "=== 总结 ===\n";
echo "1. 如果用户的上级是养老顾问，会向上查找直到找到养老院长或城市运营商\n";
echo "2. 如果用户的上级已经是养老院长或城市运营商，保持不变\n";
echo "3. 如果用户没有上级，保持不变\n";
echo "4. 如果查找过程中遇到普通用户，保持该用户为上级\n";
echo "5. 如果养老顾问没有上级，则上级设为平台(0)\n";

?>
