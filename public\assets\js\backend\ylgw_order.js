define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'ylgw_order/index' + location.search,
                    table: 'xiluedu_course_order',
                }
            });
    
            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'order_no', title: '订单号', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'user.nickname', title: '用户昵称', operate: 'LIKE'},
                        {field: 'user.mobile', title: '手机号', operate: 'LIKE'},
                        {
                            field: 'platform', 
                            title: '开通方式', 
                            searchList: {
                                "quota": "额度开通",
                                "admin": "管理员开通", 
                                "wxmin": "自主购买(小程序)"
                            },
                            formatter: function(value, row, index) {
                                var platformMap = {
                                    'quota': '<span class="label label-success">额度开通</span>',
                                    'admin': '<span class="label label-info">管理员开通</span>',
                                    'wxmin': '<span class="label label-primary">自主购买(小程序)</span>'
                                };
                                return platformMap[value] || value;
                            }
                        },
                        {
                            field: 'opener_info', 
                            title: '开通者', 
                            operate: 'LIKE',
                            formatter: Table.api.formatter.content
                        },
                        {field: 'total_price', title: '订单金额', operate:'BETWEEN', sortable: true},
                        {field: 'pay_price', title: '支付金额', operate:'BETWEEN', sortable: true},
                        {
                            field: 'pay_type', 
                            title: '支付方式',
                            searchList: {"0":"无","1":"微信","2":"余额","3":"额度支付"},
                            formatter: function(value, row, index) {
                                var payTypeMap = {
                                    '0': '无',
                                    '1': '微信',
                                    '2': '余额',
                                    '3': '额度支付'
                                };
                                return payTypeMap[value] || value;
                            }
                        },
                        {
                            field: 'pay_status', 
                            title: '支付状态',
                            searchList: {"1":"待支付","2":"已支付"},
                            formatter: function(value, row, index) {
                                if (value == '2') {
                                    return '<span class="label label-success">已支付</span>';
                                } else {
                                    return '<span class="label label-danger">待支付</span>';
                                }
                            }
                        },
                        {field: 'paytime', title: '支付时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'createtime', title: '创建时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, sortable: true}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定tab切换事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var value = $(this).data('value');
                var options = table.bootstrapTable('getOptions');
                options.pageNumber = 1;
                options.queryParams = function (params) {
                    var filter = params.filter ? JSON.parse(params.filter) : {};
                    var op = params.op ? JSON.parse(params.op) : {};
                    
                    if (value) {
                        filter.platform = value;
                        op.platform = value.indexOf(',') > -1 ? 'in' : '=';
                    } else {
                        delete filter.platform;
                        delete op.platform;
                    }
                    
                    params.filter = JSON.stringify(filter);
                    params.op = JSON.stringify(op);
                    
                    return params;
                };
                table.bootstrapTable('refresh', {});
                return false;
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
