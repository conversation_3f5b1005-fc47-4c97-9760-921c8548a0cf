<?php
/**
 * 课程16套餐分佣修复验证测试
 * 验证价格差分佣逻辑是否正确
 */

echo "=== 课程16套餐分佣修复验证 ===\n\n";

// 模拟套餐数据
$packages = [
    [
        'id' => 1,
        'name' => '单个购买',
        'quantity' => 1,
        'original_price' => 365.00,
        'qydl_price' => 146.00,    // 城市运营商价格
        'sqdl_price' => 182.50,    // 养老院长价格
        'ylgw_price' => 365.00,    // 养老顾问价格
    ],
    [
        'id' => 2,
        'name' => '套餐购买(10个)',
        'quantity' => 10,
        'original_price' => 3650.00,
        'qydl_price' => 1300.00,   // 城市运营商价格
        'sqdl_price' => 1600.00,   // 养老院长价格
        'ylgw_price' => 3650.00,   // 养老顾问价格
    ],
    [
        'id' => 3,
        'name' => '套餐购买(20个)',
        'quantity' => 20,
        'original_price' => 7300.00,
        'qydl_price' => 2600.00,   // 城市运营商价格
        'sqdl_price' => 3200.00,   // 养老院长价格
        'ylgw_price' => 7300.00,   // 养老顾问价格
    ]
];

// 测试场景
$test_scenarios = [
    [
        'title' => '养老院长购买单个养老顾问资格',
        'package_id' => 1,
        'buyer_type' => 'sqdl',
        'parent_type' => 'qydl',
        'expected_commission' => 182.50 - 146.00, // 36.5
        'description' => '养老院长支付182.5，上级城市运营商应分账36.5（182.5-146=36.5）'
    ],
    [
        'title' => '养老院长购买10个养老顾问资格',
        'package_id' => 2,
        'buyer_type' => 'sqdl',
        'parent_type' => 'qydl',
        'expected_commission' => 1600.00 - 1300.00, // 300
        'description' => '养老院长支付1600，上级城市运营商应分账300（1600-1300=300）'
    ],
    [
        'title' => '养老院长购买20个养老顾问资格',
        'package_id' => 3,
        'buyer_type' => 'sqdl',
        'parent_type' => 'qydl',
        'expected_commission' => 3200.00 - 2600.00, // 600
        'description' => '养老院长支付3200，上级城市运营商应分账600（3200-2600=600）'
    ],
    [
        'title' => '普通用户购买单个，上级是城市运营商',
        'package_id' => 1,
        'buyer_type' => 'user',
        'parent_type' => 'qydl',
        'expected_commission' => 365.00 - 146.00, // 219
        'description' => '普通用户支付365，上级城市运营商应分账219（365-146=219）'
    ],
    [
        'title' => '普通用户购买单个，上级是养老院长',
        'package_id' => 1,
        'buyer_type' => 'user',
        'parent_type' => 'sqdl',
        'expected_commission' => 365.00 - 182.50, // 182.5
        'description' => '普通用户支付365，上级养老院长应分账182.5（365-182.5=182.5）'
    ]
];

// 价格差分佣计算函数
function calculatePriceDifferenceCommission($package, $buyer_type, $parent_type) {
    $buyer_price = getPriceByUserType($package, $buyer_type);
    $parent_price = getPriceByUserType($package, $parent_type);
    return $buyer_price - $parent_price;
}

// 根据用户类型获取价格
function getPriceByUserType($package, $userType) {
    switch ($userType) {
        case 'qydl':
            return $package['qydl_price'];
        case 'sqdl':
            return $package['sqdl_price'];
        case 'ylgw':
            return $package['ylgw_price'];
        default:
            return $package['original_price'];
    }
}

// 用户类型名称映射
$userTypeNames = [
    'user' => '普通用户',
    'qydl' => '城市运营商',
    'sqdl' => '养老院长',
    'ylgw' => '养老顾问'
];

echo "## 修复前后对比\n\n";
echo "### 修复前（错误逻辑）\n";
echo "- 使用固定比例计算分佣（如10%）\n";
echo "- 养老院长购买单个：182.5 × 10% = 18.25元（错误）\n";
echo "- 养老院长购买10个：1600 × 10% = 160元（错误）\n\n";

echo "### 修复后（正确逻辑）\n";
echo "- 使用价格差计算分佣\n";
echo "- 分佣金额 = 购买者支付价格 - 上级用户类型对应价格\n\n";

echo "## 测试结果\n\n";

foreach ($test_scenarios as $index => $scenario) {
    $package = $packages[$scenario['package_id'] - 1];
    $calculated_commission = calculatePriceDifferenceCommission($package, $scenario['buyer_type'], $scenario['parent_type']);
    
    echo "### 测试" . ($index + 1) . ": {$scenario['title']}\n";
    echo "**套餐信息：** {$package['name']}\n";
    echo "**购买者：** {$userTypeNames[$scenario['buyer_type']]}\n";
    echo "**上级：** {$userTypeNames[$scenario['parent_type']]}\n";
    echo "**购买价格：** ¥" . getPriceByUserType($package, $scenario['buyer_type']) . "\n";
    echo "**上级价格：** ¥" . getPriceByUserType($package, $scenario['parent_type']) . "\n";
    echo "**计算分佣：** ¥{$calculated_commission}\n";
    echo "**期望分佣：** ¥{$scenario['expected_commission']}\n";
    
    if (abs($calculated_commission - $scenario['expected_commission']) < 0.01) {
        echo "**结果：** ✅ 通过\n";
    } else {
        echo "**结果：** ❌ 失败\n";
    }
    
    echo "**说明：** {$scenario['description']}\n";
    echo str_repeat("-", 60) . "\n\n";
}

echo "## 修复要点总结\n\n";
echo "1. **新增价格差计算逻辑**\n";
echo "   - 获取套餐信息\n";
echo "   - 根据上级用户类型获取对应价格\n";
echo "   - 计算：购买价格 - 上级价格 = 分佣金额\n\n";

echo "2. **新增处理方法**\n";
echo "   - handleYlgwParentNew() - 养老顾问上级处理\n";
echo "   - handleSqdlParentNew() - 养老院长上级处理\n";
echo "   - handleQydlParentNew() - 城市运营商上级处理\n\n";

echo "3. **兼容性保证**\n";
echo "   - 保留旧方法用于兼容\n";
echo "   - 新套餐系统使用价格差逻辑\n";
echo "   - 旧系统继续使用比例逻辑\n\n";

echo "4. **分佣原则**\n";
echo "   - 只分给直接上级，不再多级分佣\n";
echo "   - 确保分佣金额为正数\n";
echo "   - 详细的日志记录便于调试\n\n";

echo "=== 测试完成 ===\n";
