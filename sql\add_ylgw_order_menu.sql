-- 添加开通养老顾问记录菜单到会员管理下面
-- 执行前请先备份数据库

-- 1. 查看当前会员管理菜单结构
SELECT '=== 当前会员管理菜单结构 ===' as info;
SELECT id, pid, name, title, icon, ismenu, weigh, status 
FROM fa_auth_rule 
WHERE name LIKE '%user%' AND (title LIKE '%会员%' OR title LIKE '%用户%')
ORDER BY pid, weigh DESC;

-- 2. 获取会员管理的父菜单ID
SET @user_parent_id = (
    SELECT id FROM fa_auth_rule 
    WHERE name = 'user' AND title = '会员管理' AND pid = 0 
    LIMIT 1
);

-- 如果没有找到会员管理菜单，尝试查找其他可能的父菜单
SET @user_parent_id = IFNULL(@user_parent_id, (
    SELECT id FROM fa_auth_rule 
    WHERE (name LIKE '%user%' OR title LIKE '%会员%') AND pid = 0 
    LIMIT 1
));

-- 显示找到的父菜单ID
SELECT CONCAT('找到的会员管理父菜单ID: ', IFNULL(@user_parent_id, '未找到')) as parent_info;

-- 3. 添加开通养老顾问记录菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', @user_parent_id, 'ylgw_order', '开通养老顾问记录', 'fa fa-user-plus', '', '查看养老顾问开通记录', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 85, 'normal');

-- 获取刚插入的菜单ID
SET @ylgw_order_menu_id = LAST_INSERT_ID();

-- 4. 添加开通养老顾问记录的子菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
-- 查看列表
('file', @ylgw_order_menu_id, 'ylgw_order/index', '查看', 'fa fa-list', '', '查看开通养老顾问记录列表', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal'),
-- 批量操作
('file', @ylgw_order_menu_id, 'ylgw_order/multi', '批量操作', 'fa fa-cogs', '', '批量操作开通记录', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 95, 'normal');

-- 5. 获取所有新添加的菜单ID
SET @new_menu_ids = (
    SELECT GROUP_CONCAT(id) 
    FROM fa_auth_rule 
    WHERE name LIKE 'ylgw_order%' 
    ORDER BY id
);

-- 6. 为超级管理员组（ID=1）添加权限
UPDATE `fa_auth_group` 
SET `rules` = CASE 
    WHEN `rules` IS NULL OR `rules` = '' THEN @new_menu_ids
    ELSE CONCAT(`rules`, ',', @new_menu_ids)
END
WHERE `id` = 1;

-- 7. 清理可能的重复逗号
UPDATE `fa_auth_group` 
SET `rules` = TRIM(BOTH ',' FROM REPLACE(REPLACE(REPLACE(`rules`, ',,', ','), ',,', ','), ',,', ','))
WHERE `id` = 1;

-- 8. 查看添加结果
SELECT '=== 新添加的开通养老顾问记录菜单 ===' as info;
SELECT 
    r.id,
    r.pid,
    r.name,
    r.title,
    r.icon,
    r.ismenu,
    r.weigh,
    r.status,
    CASE 
        WHEN r.pid = @ylgw_order_menu_id THEN '子菜单'
        WHEN r.name = 'ylgw_order' THEN '主菜单'
        ELSE '其他'
    END as menu_type
FROM `fa_auth_rule` r 
WHERE r.name LIKE 'ylgw_order%' 
ORDER BY r.pid, r.weigh DESC;

-- 9. 验证超级管理员权限
SELECT '=== 超级管理员权限验证 ===' as info;
SELECT 
    g.id as group_id,
    g.name as group_name,
    CASE 
        WHEN FIND_IN_SET(@ylgw_order_menu_id, g.rules) > 0 THEN '已配置开通养老顾问记录权限'
        ELSE '未配置开通养老顾问记录权限'
    END as permission_status
FROM `fa_auth_group` g 
WHERE g.id = 1;

-- 10. 显示完整的会员管理菜单结构
SELECT '=== 完整的会员管理菜单结构 ===' as info;
SELECT 
    CASE 
        WHEN r.pid = 0 THEN CONCAT('├── ', r.title, ' (', r.name, ')')
        WHEN r.pid = @user_parent_id THEN CONCAT('│   ├── ', r.title, ' (', r.name, ')')
        ELSE CONCAT('│   │   ├── ', r.title, ' (', r.name, ')')
    END as menu_structure,
    CASE 
        WHEN r.ismenu = 1 THEN '显示在菜单'
        ELSE '不显示在菜单'
    END as menu_display
FROM `fa_auth_rule` r 
WHERE (r.pid = @user_parent_id OR r.id = @user_parent_id OR r.pid IN (
    SELECT id FROM fa_auth_rule WHERE pid = @user_parent_id
))
ORDER BY r.pid, r.weigh DESC;
