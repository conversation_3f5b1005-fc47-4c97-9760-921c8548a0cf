<?php
namespace app\api\controller\wanlshop;

use addons\wanlshop\library\WanlSdk\Common;
use app\admin\model\Invoice;
use app\admin\model\ShareGoodsOrder;
use app\api\model\service\Skill;
use app\common\controller\Api;
use think\Cache;
use think\Db;
use think\Exception;
use app\api\model\wanlshop\Goods;
use app\api\model\wanlshop\OrderGoods;

/**
 * WanlShop订单接口
 */
class Order extends Api
{
    protected $noNeedLogin = [];
	protected $noNeedRight = ['*'];
    
	/**
     * 获取订单列表
     *
     * @ApiSummary  (WanlShop 订单接口获取订单列表)
     * @ApiMethod   (GET)
	 * 2020年5月12日23:25:40
	 *
	 * @param string $state 状态
	 */
    public function getOrderList()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
    	$state = $this->request->request('state');
        $is_sqdl = $this->request->request('is_sqdl');
        $is_service = $this->request->request('is_service',0);
        $keyword = $this->request->request('keyword');
        if ($state && $state != 0) {
        	$where['order.state'] = $state;
        }
        $where['order.status'] = 'normal';
        // $where['address.district'] = '二七区';
        if($is_sqdl == 1){
            //获取此城市运营商代理区域
            $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
            if(!$district){
                $this->error('您还不是城市运营商');
            }
            $where['address.district'] = $district;
        }else{
            $where['order.user_id'] = $this->auth->id;
        }
        if($keyword){
            $where['goods.title'] = ['like','%'.$keyword.'%'];
        }
        $where['order.is_service'] = $is_service;
		// 列表	
		$list = model('app\api\model\wanlshop\Order')->alias('order')
            ->join('wanlshop_order_address address','address.order_id = order.id','left')
            ->join('wanlshop_order_goods goods','goods.order_id = order.id','left')
			->where($where)
			->field('order.id,order.order_no,order.shop_id,order.state,order.createtime')
			->order('order.updatetime desc')
			->paginate()
			->each(function($order, $key) use($state){
				$goods = model('app\api\model\wanlshop\OrderGoods')
					->where(['order_id'=> $order->id])
					->field('id,title,goods_id,image,difference,price,market_price,number,refund_status')
					->select();
				$order['goods'] = $goods;
				// 获取支付 1.1.2升级
				$order['pay'] = model('app\api\model\wanlshop\Pay')
					->where(['order_id' => $order->id, 'type' => 'goods'])
					->field('number, price, order_price, freight_price, discount_price, actual_payment')
					->find();
				$order['shop'] = $order->shop?$order->shop->visible(['shopname']):[];
                $order['pay_deadline']=$order['createtime']+1800;
                $order['invoice'] = Invoice::where(['order_no' => $order->order_no])->find();
                $order['refund_info']= \app\api\model\wanlshop\Refund::where(['order_id' => $order->id])->find();
				return $order;
			});
		$list?($this->success('ok',$list)):($this->error(__('网络繁忙')));
    }
    
    /**
	 * 获取购买过的商品
	 *
	 * @ApiSummary  (WanlShop 订单接口获取订单列表)
	 * @ApiMethod   (GET)
	 * 2020年5月12日23:25:40
	 *
	 * @param string $state 状态
	 */
	public function getBuyList()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    $order_ids = [];
	    foreach (model('app\api\model\wanlshop\Order')->where([ 'user_id' => ['eq', $this->auth->id], 'state' => ['in', '2,3,4,6'], 'status' => ['eq', 'normal']])->select() as $order) {
			  $order_ids[] = $order['id'];  
		}
	    $goods = model('app\api\model\wanlshop\OrderGoods')
			->where('order_id', 'in', $order_ids)
			->select();
		// 列表	
		$list = model('app\api\model\wanlshop\Goods')
			->where('id', 'in', array_keys(array_flip(array_column($goods, 'goods_id'))))
			->field('id, image, title, price')
			->order('updatetime desc')
			->paginate();
		$this->success('ok', $list);
	}
    
    
	/**
	 * 查询用户店铺订单记录 
	 *
	 * @ApiSummary  (查询用户店铺订单记录 1.0.2升级)
	 * @ApiMethod   (POST)
	 *
	 * @param string $shop_id 店铺ID
	 */
	public function getOrderListToShop()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if($this->request->isPost()){
			$shop_id = $this->request->post('shop_id');
			$shop_id ? '':($this->error(__('Invalid parameters')));
			$list = model('app\api\model\wanlshop\Order')
				->where(['shop_id' => $shop_id, 'user_id' => $this->auth->id, 'status' => 'normal'])
				->field('id,shop_id,order_no,state')
				->order('updatetime desc')
				->select();
			// 订单状态:1=待支付,2=待发货,3=待收货,4=待评论,5=售后订单(已弃用),6=已完成,7=已取消
			foreach ($list as $row) {
			    $row['goods'] = model('app\api\model\wanlshop\OrderGoods')
			    	->where(['order_id'=> $row->id])
			    	->field('id,title,image,difference,price,market_price,number,refund_status')
			    	->select();
			}
			$this->success(__('发送成功'), $list);
		}
		$this->error(__('非法请求'));
	}
	
    /**
     * 获取订单详情
     *
     * @ApiSummary  (WanlShop 订单接口获取订单详情)
     * @ApiMethod   (GET)
	 * 
	 * @param string $id 订单ID
	 */
    public function getOrderInfo()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		$id = $this->request->request('id');
		$id ? $id : ($this->error(__('非法请求')));
		$order = model('app\api\model\wanlshop\Order')
			->where(['id' => $id, 'user_id' => $this->auth->id])
			->field('id,shop_id,order_no,isaddress,express_no,express_name,freight_type,remarks,state,createtime,paymenttime,delivertime,taketime,dealtime')
			->find();
		$order ? $order : ($this->error(__('网络异常')));
		// 输出配置
		$config = get_addon_config('wanlshop');
		$order['config'] = $config['order'];
		switch ($order['state']) {
			case 1:
				$express = [
					'context' => '付款后，即可将商品发出',
					'status' => '尚未付款',
					'time' => date('Y-m-d H:i:s', $order['createtime'])
				];
				break;
			case 2:
				$express = [
					'context' => '商家正在处理订单',
					'status' => '已付款',
					'time' => date('Y-m-d H:i:s', $order['paymenttime'])
				];
				break;
			default: // 获取物流
				$eData = model('app\api\model\wanlshop\KuaidiSub')
					->where(['express_no' => $order['express_no']])
					->find();
				// 兼容PHP7.4	1.1.5升级
				if(!empty($eData['data'])){
				    $ybData = json_decode($eData['data'], true);
					$express = $ybData[0];
				}else{
					$express = [
						'status' => '已发货',
						'context' => '包裹正在等待快递小哥揽收~',
						'time' => date('Y-m-d H:i:s', $order['delivertime'])
					];
				}
		}
		// 获取物流
		$order['logistics'] = $express;
		// 获取地址
		$order['address'] = model('app\api\model\wanlshop\OrderAddress')
			->where(['order_id' => $id, 'user_id' => $this->auth->id])
			->order('isaddress desc')
			->field('id,name,mobile,address,address_name')
			->find();
		// 获取店铺
		$order['shop'] = $order->shop?$order->shop->visible(['shopname']):[];
		// 获取订单商品
		$order['goods'] = model('app\api\model\wanlshop\OrderGoods')
			->where(['order_id'=> $id])
			->field('id,goods_id,title,image,difference,price,market_price,actual_payment,discount_price,freight_price,number,refund_id,refund_status')
			->select();
		foreach($order['goods'] as $v){
			$goods_info = \app\api\model\wanlshop\Refund::where(['order_id' => $id,'goods_ids'=>$v->id])->find();
			if($goods_info){
				$v['refund_state']=$goods_info['state']??'';
			}else{
				$v['refund_state']='';
			}
		}

		// 获取支付 1.1.2升级
		$order['pay'] = model('app\api\model\wanlshop\Pay')
			->where(['order_id' => $order->id, 'type' => 'goods'])
			->field('id, pay_no, number, price, pay_type,order_price, freight_price, discount_price, actual_payment')
			->find();
        $order['pay_deadline']=$order['createtime']+1800;
        $order['refund_info'] = \app\api\model\wanlshop\Refund::where(['order_id' => $id])->find();
		$this->success('ok',$order);
    }
	
	/**
	 * 确认收货
	 *
	 * @ApiSummary  (WanlShop 订单接口确认收货)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	public function confirmOrder()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $id = $this->request->post('id');
			$id ? $id : ($this->error(__('非法请求')));
			// 判断权限
			$order = model('app\api\model\wanlshop\Order')
				->where(['id' => $id, 'state'=> 3, 'user_id' => $this->auth->id])
				->find();
			if(!$order){
				$this->error(__('订单异常'));
			}
			Db::startTrans();
			try {
			    // 获取支付 1.1.2升级
//			    $pay = model('app\api\model\wanlshop\Pay')->get(['order_id' => $id, 'type' => 'goods']);
			    // 平台转款给商家
			    // controller('addons\wanlshop\library\WanlPay\WanlPay')->money(+$pay['price'], $order['shop']['user_id'], '买家确认收货', 'pay', $order['order_no']);
			    // 查询是否有退款
			    $refund = model('app\api\model\wanlshop\Refund')
			    	->where(['order_id' => $id, 'state' => 4, 'order_type' => 'goods'])
			    	->select();
			    // 退款存在
			    if($refund){
			    	foreach($refund as $value){
			    		controller('addons\wanlshop\library\WanlPay\WanlPay')->money(-$value['price'], $order['shop']['user_id'], '该订单存在的退款', 'pay', $order['order_no']);
			    	}
			    }
                $order_list = OrderGoods::where('order_id',$id)->field('id,goods_id,title,goods_sku_id,order_id,shop_id,cost_price,freight_price,number,actual_payment,shequ_d_bili,quyu_d_bili,ylgw_d_bili')->select();
                foreach ($order_list as $v){
                    $goods = model('app\api\model\wanlshop\Goods')
                        ->where(['id' => $v['goods_id'], 'status' => 'normal'])
                        ->find();
                    //如果不是城市运营商自己买则发放奖励
                    if($this->auth->is_qydl != 1){
                        //获取此用户所属养老院长
                        $sq_user = \app\common\model\User::getOneSq($this->auth->id);
                        //获取此用户所属城市运营商
                        $qy_user = \app\common\model\User::getOneQy($this->auth->id);
                        //获取此用户所属养老顾问
                        $ylgw_user = \app\common\model\User::getOneYlgw($this->auth->id);

                        $sq_money = $v['shequ_d_bili'];
                        $qy_money = $v['quyu_d_bili'];
                        $ylgw_money = $v['ylgw_d_bili'];
                        //发放养老院长奖励
                        if($sq_user && $sq_money > 0){
                            OrderGoods::where('id',$v['id'])->update(['shequ_bili'=>$sq_money,'shequ_d_bili'=>0]);
                            \app\common\model\User::money($sq_money,$sq_user['id'],'下级购买商品《'.$v['title'].'》',0,$id,'fenyong');
                        }
                        //发放城市运营商奖励
                        if($qy_user && $qy_money > 0){
                            OrderGoods::where('id',$v['id'])->update(['quyu_bili'=>$qy_money,'quyu_d_bili'=>0]);
                            \app\common\model\User::money($qy_money,$qy_user['id'],'下级购买商品《'.$v['title'].'》',0,$id,'fenyong');
                        }
                        //发放养老顾问奖励
                        if($ylgw_user && $ylgw_money > 0){
                            // 检查是否是推荐分佣（购买用户的直接上级就是养老顾问）
                            $purchase_user = \app\common\model\User::where('id', $this->auth->id)->field('id,parent_id')->find();
                            $is_referral_commission = ($purchase_user['parent_id'] == $ylgw_user['id']);

                            OrderGoods::where('id',$v['id'])->update(['ylgw_bili'=>$ylgw_money,'ylgw_d_bili'=>0]);

                            // 无论是推荐分佣还是普通分佣，都要检查养老顾问是否有上级
                            $ylgw_parent = \app\common\model\User::where('id', $ylgw_user['id'])->field('parent_id,is_sqdl,is_qydl')->find();
                            $has_superior = false;

                            if($ylgw_parent && $ylgw_parent['parent_id'] > 0) {
                                // 检查上级是否是养老院长或城市运营商
                                $parent_user = \app\common\model\User::where('id', $ylgw_parent['parent_id'])->field('is_sqdl,is_qydl')->find();
                                if($parent_user && ($parent_user['is_sqdl'] == 1 || $parent_user['is_qydl'] == 1)) {
                                    $has_superior = true;
                                }
                            }

                            if($is_referral_commission) {
                                if (!$has_superior) {
                                    // 推荐分佣且无上级：直接发放到账户余额
                                    \app\common\model\User::money($ylgw_money,$ylgw_user['id'],'养老顾问推荐分成-商品《'.$v['title'].'》',0,$id,'fenyong');
                                } else {
                                    // 推荐分佣但有上级：按线下分佣处理
                                    $parent_user_full = \app\common\model\User::where('id', $ylgw_user['parent_id'])->find();
                                    if ($parent_user_full) {
                                        // 1. 佣金实际分给上级账户
                                        \app\common\model\User::money($ylgw_money, $parent_user_full['id'], '下级养老顾问推荐分佣-商品《'.$v['title'].'》(来自用户' . $this->auth->id . ')', 0, $id, 'fenyong');

                                        // 2. 记录养老顾问应得金额
                                        $current_commission = $ylgw_user['ylgw_total_commission'] ?: 0;
                                        \app\common\model\User::where('id', $ylgw_user['id'])->setInc('ylgw_total_commission', $ylgw_money);

                                        // 3. 记录养老顾问的应得分佣详情
                                        \app\common\model\MoneyLog::create([
                                            'user_id' => $ylgw_user['id'],
                                            'money' => $ylgw_money,
                                            'before' => $current_commission,
                                            'after' => $current_commission + $ylgw_money,
                                            'memo' => '应得推荐分佣-商品《'.$v['title'].'》(已分给上级' . $parent_user_full['id'] . ',待线下分账)',
                                            'type' => 'pending_commission',
                                            'createtime' => time()
                                        ]);
                                    }
                                }
                            } else {
                                // 普通分佣：使用前面已经检查的上级状态
                                if(!$has_superior) {
                                    // 普通分佣且无上级：直接发放到账户余额
                                    \app\common\model\User::money($ylgw_money,$ylgw_user['id'],'下级购买商品《'.$v['title'].'》',0,$id,'fenyong');
                                } else {
                                    // 普通分佣且有上级：按线下分佣处理
                                    $parent_user_full = \app\common\model\User::where('id', $ylgw_user['parent_id'])->find();
                                    if ($parent_user_full) {
                                        // 1. 佣金实际分给上级账户
                                        \app\common\model\User::money($ylgw_money, $parent_user_full['id'], '下级养老顾问分佣-商品《'.$v['title'].'》(来自用户' . $this->auth->id . ')', 0, $id, 'fenyong');

                                        // 2. 记录养老顾问应得金额
                                        $current_commission = $ylgw_user['ylgw_total_commission'] ?: 0;
                                        \app\common\model\User::where('id', $ylgw_user['id'])->setInc('ylgw_total_commission', $ylgw_money);

                                        // 3. 记录养老顾问的应得分佣详情
                                        \app\common\model\MoneyLog::create([
                                            'user_id' => $ylgw_user['id'],
                                            'money' => $ylgw_money,
                                            'before' => $current_commission,
                                            'after' => $current_commission + $ylgw_money,
                                            'memo' => '应得分佣-商品《'.$v['title'].'》(已分给上级' . $parent_user_full['id'] . ',待线下分账)',
                                            'type' => 'pending_commission',
                                            'createtime' => time()
                                        ]);
                                    }
                                }
                            }
                        }
                    }
                    $cost_price = $v['cost_price'] * $v['number'] + $v['freight_price'];
                    //给服务者发放佣金
                    if($cost_price > 0){
                        $shop_user_id = Db::name('wanlshop_shop')->where('id',$v['shop_id'])->value('user_id');
                        // \app\common\model\User::money($v['cost_price'],$shop_user_id,'供货商卖出商品获得佣金',2,$v['order_id'],'fenyong');
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->money(+$cost_price, $shop_user_id, '供货商卖出商品获得佣金', 'pay', $order['order_no']);
                    }
                }
			    // 更新退款
			    $order->save(['state' => 4,'taketime' => time()],['id' => $id]);
			    Db::commit();
			} catch (Exception $e) {
			    Db::rollback();
				$this->error($e->getMessage());
			}
		    $this->success('ok', $order ? true : false);
		}
		$this->error(__('非法请求'));
	}
	
	/**
	 * 取消订单
	 *
	 * @ApiSummary  (WanlShop 订单接口取消订单)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	public function cancelOrder()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $id = $this->request->post('id');
			$id ? $id : ($this->error(__('非法请求')));
			// 判断权限
			$this->getOrderState($id) != 1 ? ($this->error(__('订单异常'))):'';
			$row = model('app\api\model\wanlshop\Order')->get(['id' => $id, 'user_id' => $this->auth->id]);
			$result = $row->allowField(true)->save(['state' => 7]);
			// 还原优惠券 1.0.2升级
			if($row['coupon_id'] != 0){
				model('app\api\model\wanlshop\CouponReceive')->where(['id' => $row['coupon_id'], 'user_id' => $this->auth->id])->update(['state' => 1]);
			}
			// 释放库存 1.0.3升级
			foreach(model('app\api\model\wanlshop\OrderGoods')->all(['order_id' => $row['id']]) as $vo){
				// 查询商品是否需要释放库存
				if(model('app\api\model\wanlshop\Goods')->get($vo['goods_id'])['stock'] == 'porder'){
					model('app\api\model\wanlshop\GoodsSku')->where('id', $vo['goods_sku_id'])->setInc('stock', $vo['number']);
				}
			}
		    $this->success('ok', $result ? true : false);
		}
		$this->error(__('非法请求'));
	}
	
    /**
     * 删除订单
     *
     * @ApiSummary  (WanlShop 订单接口删除订单)
     * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
    public function delOrder()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
		    $id = $this->request->post('id');
			$id ? $id : ($this->error(__('非法请求')));
			// 判断权限
			$state = $this->getOrderState($id);
			$state == 6 || $state == 7 ? '' :($this->error(__('非法请求')));
			$order = model('app\api\model\wanlshop\Order')
				->save(['status' => 'hidden'],['id' => $id]);
			$this->success('ok', $order ? true : false);
		}
		$this->error(__('非法请求'));
    }
	
    
	/**
	 * 修改地址
	 *
	 * @ApiSummary  (WanlShop 订单接口修改地址)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	public function editOrderAddress()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $order_id = $this->request->post('id');
			$address_id = $this->request->post('address_id');
			$order_id || $address_id ? $order_id : ($this->error(__('非法请求')));
			// 判断权限
			$this->getOrderState($order_id) > 3 ? ($this->error(__('订单异常'))):'';
			// 订单
			$order = model('app\api\model\wanlshop\Order')
				->where(['id' => $order_id, 'user_id' => $this->auth->id])
				->find();
			
			//判断是否修改过
			if($order['isaddress'] == 1){
				$this->error(__('已经修改过一次了'));
			}else{
				// 获取地址
				$address = model('app\api\model\wanlshop\Address')
					->where(['id' => $address_id, 'user_id' => $this->auth->id])
					->find();
				// 修改地址
				$data = model('app\api\model\wanlshop\OrderAddress')->save([
						'user_id' => $this->auth->id,
						'shop_id' => $order->shop_id,
						'order_id'  => $order_id,
						'isaddress' => 1,
						'name' => $address['name'],
						'mobile' => $address['mobile'],
						'address' => $address['province'].'/'.$address['city'].'/'.$address['district'].'/'.$address['address'],
						'address_name' => $address['address_name'],
						'location' => $address['location']
					]);
				// 修改状态
				model('app\api\model\wanlshop\Order')->where(['id' => $order_id, 'user_id' => $this->auth->id])->update(['isaddress' => 1]);
				$this->success('ok',$data);
			}
		}
		$this->error(__('非法请求'));
	}
	
	/**
	 * 评论订单
	 *
	 * @ApiSummary  (WanlShop 订单接口评论订单)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	public function commentOrder()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $post = $this->request->post();
			$post ? $post : ($this->error(__('数据异常')));
			$user_id = $this->auth->id;
			// 判断权限
			$this->getOrderState($post['order_id']) != 4 ? ($this->error(__('已经评论过或订单异常'))):'';
			// 生成列表
			$commentData = [];
			foreach ($post['goodsList'] as $value) {
				$commentData[] = [
					'user_id' => $user_id,
					'shop_id' => $post['shop']['id'],
					'order_id' => $post['order_id'],
					'goods_id' => $value['goods_id'],
					'order_goods_id' => $value['id'],
					'order_type' => 'goods',
					'state' => $value['state'],
					'content' => $value['comment'],
					'suk' => $value['difference'],
					'images' => $value['imgList'],
					'score' => round((($post['shop']['describe'] + $post['shop']['service'] + $post['shop']['deliver'] + $post['shop']['logistics']) / 4) ,1),
					'score_describe' => $post['shop']['describe'],
					'score_service' => $post['shop']['service'],
					'score_deliver' => $post['shop']['deliver'],
					'score_logistics' => $post['shop']['logistics'],
					'switch' => isset($value['switch']) ? $value['switch'] : 0,
				];
				//评论暂不考虑并发，为列表提供好评付款率，减少并发只能写进商品中
				model('app\api\model\wanlshop\Goods')->where(['id' => $value['goods_id']])->setInc('comment');
				if($value['state'] == 0){
					model('app\api\model\wanlshop\Goods')->where(['id' => $value['goods_id']])->setInc('praise');
				}else if($value['state'] == 1){
					model('app\api\model\wanlshop\Goods')->where(['id' => $value['goods_id']])->setInc('moderate');
				}else if($value['state'] == 2){
					model('app\api\model\wanlshop\Goods')->where(['id' => $value['goods_id']])->setInc('negative');
				}
			}
			if(model('app\api\model\wanlshop\GoodsComment')->saveAll($commentData)){
				$order = model('app\api\model\wanlshop\Order')
					->where(['id' => $post['order_id'], 'user_id' => $user_id])
					->update(['state' => 6]);
			}
			//更新店铺评分
			$score = model('app\api\model\wanlshop\GoodsComment')
				->where(['user_id' => $user_id])
				->select();
			// 从数据集中取出
			$describe = array_column($score,'score_describe');
			$service = array_column($score,'score_service');
			$deliver = array_column($score,'score_deliver');
			$logistics = array_column($score,'score_logistics');
			// 更新店铺评分
			model('app\api\model\wanlshop\Shop')
				->where(['id' => $post['shop']['id']])
				->update([
					'score_describe' => bcdiv(array_sum($describe), count($describe), 1),
					'score_service' => bcdiv(array_sum($service), count($service), 1),
					'score_deliver' => bcdiv(array_sum($deliver), count($deliver), 1),
					'score_logistics' => bcdiv(array_sum($logistics), count($logistics), 1)
				]);
		    $this->success('ok',[]);
		}
		$this->error(__('非法请求'));
	}
	
	/**
	 * 获取订单物流状态
	 *
	 * @ApiSummary  (WanlShop 订单接口获取订单物流状态)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	public function getLogistics()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $id = $this->request->post('id');
			$id ? $id : ($this->error(__('非法请求')));
			//获取订单
			$order = model('app\api\model\wanlshop\Order')
				->where(['id' => $id, 'user_id' => $this->auth->id])
				->field('id,shop_id,express_name,express_no,order_no,createtime,paymenttime,delivertime')
				->find();
            $order_address = model('app\api\model\wanlshop\OrderAddress')
                ->where('order_id',$id)
                ->find();
			// 获取快递
			switch ($order['state']) {
				case 1:
					$express[] = [
						'context' => '付款后，即可将商品发出',
						'status' => '尚未付款',
						'time' => date('Y-m-d H:i:s', $order['createtime'])
					];
					break;
				case 2:
					$express[] = [
						'context' => '商家接受到您的订单，准备出库',
						'status' => '已下单',
						'time' => date('Y-m-d H:i:s', $order['paymenttime'])
					];
					break;
				default: // 获取物流
					$express = model('app\api\model\wanlshop\KuaidiSub')
						->where(['express_no' => $order['express_no']])
						->find();
					if($express){
						$express = json_decode($express['data'], true);
					}else{
						$express[] = [
							'context' => '打包完成，正在等待快递小哥揽收~',
							'status' => '仓库处理中',
							'time' => date('Y-m-d H:i:s', $order['delivertime'])
						];
					}
			}
			$order['kuaidi'] = $order->kuaidi ? $order->kuaidi->visible(['name','logo','tel']) : [];
			$order['express'] = $express;
			$order['order_address'] = $order_address;
		    $this->success('ok',$order);
		}
		$this->error(__('非法请求'));
	}
	
    /**
     * 确认订单
     *
     * @ApiSummary  (WanlShop 订单接口确认订单)
     * @ApiMethod   (POST)
	 * 
	 * @param string $data 商品数据
	 */
    public function getOrderGoodsList()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
		    $request = $this->request->post();
		    // 订单数据
		    $order = array();
		    $map = array();
			// 1.0.5升级 修复客户端地址更新
			$where = !empty($request['address_id']) ? ['id' => $request['address_id'], 'user_id' => $this->auth->id] : ['user_id' => $this->auth->id, 'default' => 1];
			// 地址
			$address = model('app\api\model\wanlshop\Address')
				->where($where)
			    ->field('id,name,mobile,province,city,district,address')
				->find();
			// 1.1.6升级
			if(!$address){
				$this->error(__('请添加收货地址'));
			}
		    // 合计
		    $statis = array(
				"allnum" => 0,
				"allsub" => 0
			);
		    foreach ($request['data'] as $post) {
				$redis = Common::redis();
		    	// 商品详情
		    	$goods = model('app\api\model\wanlshop\Goods')
		    		->where('id', $post['goods_id'])
		    	    ->field('id, shop_id,price,cost_price, shop_category_id, activity_type, title,image,stock,freight_id,sales')
		    	    ->find();
		    	// 获取SKU
				$sku = model('app\api\model\wanlshop\GoodsSku')
		    		->where('id', $post['sku_id'])
		    	    ->field('id,goods_id,difference,price,market_price,cost_price,stock,weigh')
		    	    ->find();
					
				// 1.1.2升级 判断是否超出库存
				// $sku_key = 'goods_'.$sku['goods_id'].'_'.$sku['id'];
				// if($post['number'] > $redis->llen("{$sku_key}")){
				// 	$this->error("系统繁忙，请稍后抢购！");
				// }
				// 获取快递及价格
				$goods['freight'] = $this->freight($goods['freight_id'], $sku['weigh'], $post['number'], $address['city']);
				// 获取SKU
		    	$goods['sku'] = $sku;
		    	// 数量
		    	$goods['number'] = $post['number'];
			    // 格式化
		        if (empty($map[$goods['shop_id']])) {
		            $order[] = array(
					    "shop_id"   => $goods['shop_id'],
					    "shop_name" => $goods->shop ? $goods->shop->visible(['shopname'])['shopname']:[],
					    "products"  => [$goods],
						"coupon" => [],
						"freight"  => [$goods['freight']],
					    "number"    => $goods['number'],
						"sub_price" => bcmul($sku['price'], $goods['number'], 2)
					);
		            $map[$goods['shop_id']] = $goods;
		        } else {
					// 追加1-*
		            foreach ($order as $key => $value) {
		                if ($value['shop_id'] == $goods['shop_id']) {
		            		array_push($order[$key]['products'],$goods);
							array_push($order[$key]['freight'],$goods['freight']);
		            		$order[$key]['number'] += $post['number'];
							$order[$key]['sub_price'] = bcadd($order[$key]['sub_price'], bcmul($sku['price'], $post['number'], 2), 2);
		                    break;
		                }
		            }
		        }
				// 所有店铺统计
				$statis['allnum'] += $goods['number'];
		    }
			// 获取运费策略-店铺循环
			foreach ($order as $key => $value) {
				$config = model('app\api\model\wanlshop\ShopConfig')
					->where('shop_id',$value['shop_id'])
					->find();
				if($config){
					if($config['freight'] == 0){
						// 运费叠加
						$order[$key]['freight'] = [
							'id' => $value['freight'][0]['id'],
							'name' => '运费叠加',
							'price' => array_sum(array_column($value['freight'],'price'))
						];
					}else if($config['freight'] == 1){
						// 以最低结算
						array_multisort(array_column($value['freight'],'price'),SORT_ASC,$value['freight']);
						$order[$key]['freight'] = [
							'id' => $value['freight'][0]['id'],
							'name' => $value['freight'][0]['name'],
							'price' => $value['freight'][0]['price']
						];
					}else if($config['freight'] == 2){
						// 以最高结算
						array_multisort(array_column($value['freight'],'price'),SORT_DESC,$value['freight']);
						$order[$key]['freight'] = [
							'id' => $value['freight'][0]['id'],
							'name' => $value['freight'][0]['name'],
							'price' => $value['freight'][0]['price']
						];
					}
					$order[$key]['order_price'] = $order[$key]['sub_price'];
					// 2020年9月19日12:10:59 添加快递价格备份,用于还原运费
					$order[$key]['freight_price_bak'] = $order[$key]['freight']['price'];
					// 1.0.8升级
					$order[$key]['sub_price'] = bcadd($order[$key]['sub_price'], $order[$key]['freight']['price'], 2);
					$statis['allsub'] = bcadd($statis['allsub'], $order[$key]['sub_price'], 2);
				}else{
					$this->error(__('商家未配置运费组策略，暂不支持下单'));
				}
				
			}
			// 传递Token
			$datalist['token'] = Common::creatToken('orderToken_'. $this->auth->id);
		    // 地址
		    $datalist['addressData'] = $address;
			// 订单
		    $datalist['orderData']['lists'] = $order;
		    $datalist['orderData']['statis'] = $statis;
		    $this->success('ok', $datalist);
		} else {
		    $this->error(__('非法请求'));
		}
    }
    
    /**
     * 提交订单
     *
     * @ApiSummary  (WanlShop 订单接口提交订单)
     * @ApiMethod   (POST)
     * 
     * @param string $data 数组
     */
    public function addOrder()
    {
    	//设置过滤方法
    	$this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
			$result = false;
    		$params = $this->request->post();
			// 验证Token
			// if(array_key_exists('token', $params)){
			// 	if(!Common::checkToken($params['token'], 'orderToken_'. $this->auth->id)){
			// 		$this->error(__('页面安全令牌已过期！请重返此页'));
			// 	}
			// }else{
			// 	$this->error(__('非法提交，未传入Token'));
			// }
    		$user_id = $this->auth->id; // 用户ID
			$addressList = [];
			$goodsList = [];
			$payList = [];

    		// 参数验证
    		$this->validateOrderParams($params);
    		$address_id = $params['order']['address_id'];
    		$lists = $params['order']['lists'];

    		// 查询地址
    		$address = model('app\api\model\wanlshop\Address')
    			->where(['id' => $address_id,'user_id' =>$user_id])
    			->find();
    		if(!$address){
    			$this->error(__('地址异常，没有找到该地址'));
    		}

    		// 批量预加载数据
    		$preloadData = $this->preloadOrderData($lists, $user_id);
            $total_price=0;
            $order_ids=[];
			 //数据库事务操作

    		Db::startTrans();
    		try {
				// 遍历已店铺分类列表
				foreach ($lists as $item) {
					// 获取店铺ID
					$shop_id = $item['shop_id'];

					// 从预加载数据获取店铺配置
					$config = isset($preloadData['shop_configs'][$shop_id])
						? $preloadData['shop_configs'][$shop_id]
						: ['freight' => 0];

					// 生成订单
					$order = new \app\api\model\wanlshop\Order;
					$order->freight_type = $config['freight'];
					$order->user_id = $user_id;
					$order->shop_id = $shop_id;
					$order->order_no = $shop_id.$user_id;
                    $order->is_service = $params['order']['is_service'] ?? 0;
 				    if(isset($item['remarks'])){
					    $order->remarks = $item['remarks'];
					}

					// 从预加载数据获取优惠券
					$coupon = null;
					if(!empty($item['coupon_id']) && isset($preloadData['coupons'][$item['coupon_id']])) {
						$coupon = $preloadData['coupons'][$item['coupon_id']];
					}
					$order->coupon_id = $coupon ? $coupon['id'] : 0;
					// 要补充活动ID
					if($order->save()){
						$priceAll = 0; // 总价格
						$numberAll = 0; // 总数量
						$freightALL = [];
						$shopGoodsAll = [];
						// 计算订单价格
						foreach ($item['products'] as $data){
							// 从预加载数据获取商品和SKU
							$goods = isset($preloadData['goods'][$data['goods_id']])
								? $preloadData['goods'][$data['goods_id']]
								: null;
							$sku = isset($preloadData['skus'][$data['sku_id']])
								? $preloadData['skus'][$data['sku_id']]
								: null;
								
							// 数据验证
							if(!$goods) throw new Exception("对不起当前商品不存在或已下架");
							if(!$sku) throw new Exception("商品规格不存在");

							// 效验shop_id是否正确
							if($goods['shop_id'] != $shop_id) throw new Exception("网络异常SHOPID错误！");

							// 库存验证
							if($sku['stock'] <= 0){
								throw new Exception("商品被抢光了");
							}else if($sku['stock'] < $data['number']){
								throw new Exception("库存不足");
							}

							// 库存计算方式:porder=下单减库存,payment=付款减库存
							if($goods['stock'] == 'porder'){
								// 1.1.2升级
								// if($data['number'] > $redis->llen("{$sku_key}")){
								// 	// throw new Exception("系统繁忙，请稍后抢购！");
								// }else{
								// 	for ($i = 0; $i < $data['number']; $i ++) {
								// 		$redis->rpop("{$sku_key}");
								// 	}
								//	$sku->setDec('stock', $data['number']); // 1.0.3升级
								// }
							}

                            //实际支付
                            $actual_payment = bcmul($sku['price'], $data['number'], 2);
							// 生成运费
							$freight = $this->freight($goods['freight_id'], $sku['weigh'], $data['number'], $address['city']);

                            $orderGoodsInfo = [
                                'order_id' => $order->id, // 获取自增ID
                                'goods_id' => $goods['id'],
                                'shop_id' => $shop_id,
                                'title' => $goods['title'],
                                'image' => $goods['image'],
                                'goods_sku_sn' => $sku['sn'],
                                'goods_sku_id' => $sku['id'],
                                'difference' => is_array($sku['difference']) ? join(',', $sku['difference']) : $sku['difference'],
                                'market_price' => $sku['market_price'], // 市场价
                                'price' => $sku['price'], // 原价
                                'cost_price' => $sku['cost_price'], // 原价
                                'freight_price' => $freight['price'], //快递价格
                                'discount_price' => 0, // 优惠金额
                                'actual_payment' => $actual_payment, // 1.0.6修复 实际支付，因为要和总价进行计算
                                'number' => $data['number'],
                                // 'shequ_bili' => $sq_money,
                                // 'quyu_bili' => $qy_money,
                                // 'service_bili' => $service_money,
                            ];

							// 商品列表 actual_payment
							$shopGoodsAll[] = $orderGoodsInfo;
							$freightALL[] = $freight;
							$priceAll = bcadd($priceAll, bcmul($sku['price'], $data['number'], 2), 2); // 计算价格
							$numberAll += $data['number']; // 计算数量
						}

                        $discount_price=0;
                        //服务者保证金减价
                        if($params['order']['is_service'] == 1){
                            $skill = Skill::where('user_id',$order->user_id)->find();
                            if(!empty($skill) && $skill['ensure_price']>0){
                                $reduce_price = $priceAll*0.9;
                                $priceAll-= $reduce_price;
                            }
                            $discount_price += $reduce_price ?? 0;
                        }


                        //分享减价
                        if(!empty($params['order']['share_id'])){
                            $share_order=ShareGoodsOrder::where('id',$params['order']['share_id'])->where('end_time','>',time())->where('status','<',3)->find();
                            if($share_order && $share_order['reduce']>0){
                                $priceAll-=$share_order['reduce'];
                            }
                            $share_order->status=3;
                            $share_order->order_id=$order->id;
                            $share_order->save();
                            $discount_price += $share_order['reduce'];
                        }



						// 计算运费叠加方案
						if($config['freight'] == 0){
							// 运费叠加
							$freight = [
								'id' => $freightALL[0]['id'],
								'name' => '合并运费',
								'price' => array_sum(array_column($freightALL,'price'))
							];
						}else if($config['freight'] == 1){ // 以最低结算
							array_multisort(array_column($freightALL,'price'),SORT_ASC,$freightALL);
							$freight = [
								'id' => $freightALL[0]['id'],
								'name' => $freightALL[0]['name'],
								'price' => $freightALL[0]['price']
							];
						}else if($config['freight'] == 2){ // 以最高结算
							array_multisort(array_column($freightALL,'price'),SORT_DESC,$freightALL);
							$freight = [
								'id' => $freightALL[0]['id'],
								'name' => $freightALL[0]['name'],
								'price' => $freightALL[0]['price']
							];
						}
						$freight_price = $freight['price'];  //快递金额
						$price = bcadd($priceAll, $freight_price, 2); // 总价格
						$coupon_price = 0; //优惠券金额
                        $discount_price = $discount_price ?? 0; // 优惠金额，因为后续版本涉及到活动减免，所以优惠金额要单独拎出来
						// 如果优惠券存在
						if($coupon) {
							// 判断是否可用
							if($priceAll >= $coupon['limit']){
								// 优惠金额
								if($coupon['type'] == 'reduction' || ($coupon['type'] == 'vip' && $coupon['usertype'] == 'reduction')){
									$coupon_price = $coupon['price']; 
									//总金额 =（订单金额 - 优惠券金额）+ 运费
									$price = bcadd(bcsub($priceAll, $coupon_price, 2), $freight['price'], 2);
								}
								// 折扣金额
								if($coupon['type'] == 'discount' || ($coupon['type'] == 'vip' && $coupon['usertype'] == 'discount')){
									// 排除异常折扣，还原百分之
									$discount = $coupon['discount'] > 10 ? bcdiv($coupon['discount'], 100, 2) : bcdiv($coupon['discount'], 10, 2);
									// 优惠金额 = 订单金额 - 订单金额 * 折扣
									$coupon_price = bcsub($priceAll, bcmul($priceAll, $discount, 2), 2);
									$price = bcadd(bcsub($priceAll, $coupon_price, 2), $freight['price'], 2);
								}
								// 免邮金额
								if($coupon['type'] == 'shipping'){
									$coupon_price = $freight_price;
									$price = $priceAll;
									$freight_price = 0;
								}
								$discount_price = $coupon_price;
								
								// 总优惠金额 1.1.3弃用
								// $paycoupon = 0;
								// 总实际支付金额 1.1.3弃用
								// $payment = 0;   
								foreach ($shopGoodsAll as $row) {
									$goods_price = bcmul($row['price'], $row['number'], 2);
									$goods_discount_price = round($coupon_price * ( $goods_price / $priceAll ), 2); // 优惠金额
									// 1.0.8升级,修复包邮
									$actual_payment = $coupon['type'] === 'shipping' ? $goods_price : bcsub($goods_price, $goods_discount_price, 2);
									//优惠价格
									$row['discount_price'] = $goods_discount_price; 
									// 实际支付 1.0.9升级
									$row['actual_payment'] = $actual_payment <= 0 ? '0.01' : $actual_payment; 
									$row['freight_price'] = $freight_price;
									// 1.0.8升级 1.1.3弃用
									// $paycoupon = bcadd($paycoupon, $goods_discount_price, 2); 
									// $payment = bcadd($payment, $actual_payment, 2);

                                    // 使用calculateCommission方法计算分佣
                                    $commission = $this->calculateCommission($actual_payment, $row['cost_price'], $row['number'], $row['goods_id']);
                                    $row['shequ_d_bili'] = $commission['shequ_d_bili'];
                                    $row['quyu_d_bili'] = $commission['quyu_d_bili'];
                                    $row['ylgw_d_bili'] = $commission['ylgw_d_bili'];

									$goodsList[] = $row;
								}
								
								// 更新已使用数量
								model('app\api\model\wanlshop\Coupon')
									->where(['id' => $coupon['coupon_id']])
									->setInc('usenum');
								// 修改该优惠券状态 已用
								$coupon->allowField(true)->save(['state' => 2]);
							}else{
								model('app\api\model\wanlshop\Order')->destroy($order->id);
								throw new Exception('订单金额'.$priceAll.'元，不满'.$coupon['limit'].'元');
							}
						}else{
							foreach ($shopGoodsAll as $row) {
                                // 使用calculateCommission方法计算分佣
                                $commission = $this->calculateCommission($actual_payment, $row['cost_price'], $row['number'], $row['goods_id']);
                                $row['shequ_d_bili'] = $commission['shequ_d_bili'];
                                $row['quyu_d_bili'] = $commission['quyu_d_bili'];
                                $row['ylgw_d_bili'] = $commission['ylgw_d_bili'];
								$goodsList[] = $row;
							}
						}
						// 生成支付
						$payList[] = [
							'user_id' => $user_id,
							'shop_id' => $shop_id,
							'order_id'  => $order->id,
							'order_no'  => $order->order_no,
							'pay_no' => $order->order_no,
							'type' => 'goods', // 1.0.8升级
							'price'  => $price <= 0 ? 0.01 : $price,  // 支付价格，系统要求至少支付一分钱
							'order_price' => $priceAll, // 订单总金额
							'coupon_price' => $coupon_price,  // 优惠券金额
							'freight_price' => $freight_price, // 快递费
							'discount_price' => $discount_price, // 优惠金额
							'number'  => $numberAll
						];
						// 生成地址
						$addressList[] = [
							'user_id' => $user_id,
							'shop_id' => $shop_id,
							'order_id'  => $order->id,
							'name' => $address['name'],
							'mobile' => $address['mobile'],
							'address' => $address['province'].'/'.$address['city'].'/'.$address['district'].'/'.$address['address'],
							'address_name' => $address['address_name'],
							'city' => $address['city'],
							'district' => $address['district'],
							'location' => $address['location']
						];
					}else{
						throw new Exception('网络繁忙，创建订单失败！');
					}
//                    $total_price+=$price;
//                    $order_ids[]=$order->id;
				}


//                $payList[0]['total_amount']=$total_price;
//                $payList[0]['order_ids']=implode(',',$order_ids);
    		    model('app\api\model\wanlshop\OrderAddress')->saveAll($addressList);
    		    model('app\api\model\wanlshop\OrderGoods')->saveAll($goodsList);
    		    $result = model('app\api\model\wanlshop\Pay')->saveAll($payList);
    		    Db::commit();
    		} catch (Exception $e) {
    		    Db::rollback();
    			$this->error($e->getMessage().'-'.$e->getLine());
    		}
    		if ($result !== false) {
				$this->success('返回成功', ['list' => $result,'token' => Common::creatToken('orderToken_'. $this->auth->id)]);
    		} else {
    		    $this->error(__('订单异常，请返回重新下单'));
    		}
    	} else {
    	    $this->error(__('非法请求'));
    	}
    }
    
	/**
	 * 订单状态码（方法内使用）
	 *
	 * @ApiSummary  (WanlShop 返回订单状态码)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	private function getOrderState($id = 0)
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    $order = model('app\api\model\wanlshop\Order')
	    	->where(['id' => $id, 'user_id' => $this->auth->id])
	    	->find();
		return $order['state'];
	}
	
	/**
	 * 获取运费模板和子类 内部方法 
	 * 1.1.6升级
	 * @param string $id  运费ID
	 * @param string $weigh  商品重量
	 * @param string $number  商品数量
	 * @param string $city  邮递城市
	 */
	private function freight($id = null, $weigh = null, $number = 0, $city = '南山区')
	{
		// 使用缓存获取运费模板
		$cache_key = "freight_template_{$id}";
		$data = cache($cache_key);
		if ($data === false) {
			$data = model('app\api\model\wanlshop\ShopFreight')
				->where('id', $id)
				->field('id,delivery,isdelivery,name,valuation')
				->find();
			if($data) {
				cache($cache_key, $data, 3600); // 缓存1小时
			}
		}

		if(!$data){
			$this->error(__('此商品运费模板不存在，暂不支持下单'));
		}
		$data['price'] = 0;

		// 是否包邮:0=自定义运费,1=卖家包邮
		if($data['isdelivery'] == 0){
			// 获取地址编码 - 使用缓存优化
			$cityid = $this->getCityIdWithCache($city);

			// 获取运费数据 - 使用缓存
			$list = $this->getFreightDataWithCache($id, $cityid);

			if(!$list){
				$list = model('app\api\model\wanlshop\ShopFreightData')
					->where('freight_id', $id)
					->find();
			}

			if($list) {
				// 计价方式:0=按件数,1=按重量,2=按体积
				if($data['valuation'] == 0){
					$price = $this->calculateFreightByNumber($list, $number);
				}else{
					$price = $this->calculateFreightByWeight($list, $weigh, $number);
				}
				$data['price'] = $price;
			}
		}
		return $data;
	}

	/**
	 * 获取城市ID（带缓存）
	 */
	private function getCityIdWithCache($cityName)
	{
		$cache_key = "city_id_{$cityName}";
		$cityId = cache($cache_key);

		if ($cityId === false) {
			$area = model('app\common\model\Area')->where('name', $cityName)->find();
			$cityId = $area ? $area->id : 0;
			cache($cache_key, $cityId, 86400); // 缓存24小时
		}

		return $cityId;
	}

	/**
	 * 获取运费数据（带缓存）
	 */
	private function getFreightDataWithCache($freight_id, $city_id)
	{
		$cache_key = "freight_data_{$freight_id}_{$city_id}";
		$data = cache($cache_key);

		if ($data === false) {
			$data = model('app\api\model\wanlshop\ShopFreightData')
				->where([
					['EXP', Db::raw('FIND_IN_SET('. $city_id .', citys)')],
					'freight_id' => $freight_id
				])
				->find();

			if($data) {
				cache($cache_key, $data, 3600); // 缓存1小时
			}
		}

		return $data;
	}

	/**
	 * 按件数计算运费
	 */
	private function calculateFreightByNumber($freightData, $number)
	{
		if($number <= $freightData['first']){
			return $freightData['first_fee'];
		}else{
			$additional = $freightData['additional'] > 0 ? $freightData['additional'] : 1;
			return bcadd(
				bcmul(ceil(($number - $freightData['first']) / $additional), $freightData['additional_fee'], 2),
				$freightData['first_fee'],
				2
			);
		}
	}

	/**
	 * 按重量计算运费
	 */
	private function calculateFreightByWeight($freightData, $weigh, $number)
	{
		$totalWeight = $weigh * $number;
		if($totalWeight <= $freightData['first']){
			return $freightData['first_fee'];
		}else{
			$additional = $freightData['additional'] > 0 ? $freightData['additional'] : 1;
			return bcadd(
				bcmul(ceil(($totalWeight - $freightData['first']) / $additional), $freightData['additional_fee'], 2),
				$freightData['first_fee'],
				2
			);
		}
	}

	/**
	 * 验证订单参数
	 */
	private function validateOrderParams($params)
	{
		// 判断是否有地址
		if(!array_key_exists('address_id',$params['order'])){
			$this->error(__('请点击上方添加收货地址'));
		}

		// 判断订单是否合法
		if(!array_key_exists('lists',$params['order'])){
			$this->error(__('订单繁忙ERR002：请返回商品详情重新提交订单'));
		}

		$lists = $params['order']['lists'];
		if(!isset($lists) || count($lists) == 0){
			$this->error(__('订单繁忙ERR001：请返回商品详情重新提交订单'));
		}
	}

	/**
	 * 批量预加载订单相关数据
	 */
	private function preloadOrderData($lists, $user_id)
	{
		// 收集所有需要查询的ID
		$shop_ids = [];
		$goods_ids = [];
		$sku_ids = [];
		$coupon_ids = [];
		$freight_ids = [];

		foreach ($lists as $item) {
			$shop_ids[] = $item['shop_id'];
			if(!empty($item['coupon_id'])) {
				$coupon_ids[] = $item['coupon_id'];
			}

			foreach ($item['products'] as $product) {
				$goods_ids[] = $product['goods_id'];
				$sku_ids[] = $product['sku_id'];
			}
		}

		// 批量查询数据
		$data = [];

		// 店铺配置 - 使用缓存
		$data['shop_configs'] = $this->getShopConfigsBatch($shop_ids);

		// 商品数据 - 使用缓存
		$data['goods'] = $this->getGoodsBatch($goods_ids);

		// SKU数据 - 使用select()以确保触发模型属性处理器
		$skus = model('app\api\model\wanlshop\GoodsSku')
			->whereIn('id', $sku_ids)
			->select();
		$data['skus'] = [];
		foreach($skus as $sku) {
			$data['skus'][$sku['id']] = $sku;
		}

		// 优惠券数据
		if(!empty($coupon_ids)) {
			$data['coupons'] = model('app\api\model\wanlshop\CouponReceive')
				->whereIn('id', $coupon_ids)
				->where('user_id', $user_id)
				->column('*', 'id');
		} else {
			$data['coupons'] = [];
		}

		// 运费模板数据 - 使用缓存
		foreach ($data['goods'] as $goods) {
			if($goods['freight_id']) {
				$freight_ids[] = $goods['freight_id'];
			}
		}
		$data['freight_templates'] = $this->getFreightTemplatesBatch($freight_ids);

		return $data;
	}

	/**
	 * 批量获取店铺配置（带缓存）
	 */
	private function getShopConfigsBatch($shop_ids)
	{
		$configs = [];
		$uncached_ids = [];

		// 先从缓存获取
		foreach ($shop_ids as $shop_id) {
			$cache_key = "shop_config_{$shop_id}";
			$config = cache($cache_key);
			if ($config !== false) {
				$configs[$shop_id] = $config;
			} else {
				$uncached_ids[] = $shop_id;
			}
		}

		// 查询未缓存的数据
		if (!empty($uncached_ids)) {
			$db_configs = model('app\api\model\wanlshop\ShopConfig')
				->whereIn('shop_id', $uncached_ids)
				->column('*', 'shop_id');

			foreach ($uncached_ids as $shop_id) {
				$config = isset($db_configs[$shop_id]) ? $db_configs[$shop_id] : ['freight' => 0];
				$configs[$shop_id] = $config;

				// 缓存30分钟
				cache("shop_config_{$shop_id}", $config, 1800);
			}
		}

		return $configs;
	}

	/**
	 * 批量获取商品数据（带缓存）
	 */
	private function getGoodsBatch($goods_ids)
	{
		$goods = [];
		$uncached_ids = [];

		// 先从缓存获取
		foreach ($goods_ids as $goods_id) {
			$cache_key = "goods_{$goods_id}";
			$item = cache($cache_key);
			if ($item !== false) {
				$goods[$goods_id] = $item;
			} else {
				$uncached_ids[] = $goods_id;
			}
		}

		// 查询未缓存的数据
		if (!empty($uncached_ids)) {
			$db_goods = model('app\api\model\wanlshop\Goods')
				->whereIn('id', $uncached_ids)
				->where('status', 'normal')
				->column('*', 'id');

			foreach ($uncached_ids as $goods_id) {
				if (isset($db_goods[$goods_id])) {
					$goods[$goods_id] = $db_goods[$goods_id];
					// 缓存10分钟
					cache("goods_{$goods_id}", $db_goods[$goods_id], 600);
				}
			}
		}

		return $goods;
	}

	/**
	 * 批量获取运费模板（带缓存）
	 */
	private function getFreightTemplatesBatch($freight_ids)
	{
		$templates = [];
		$uncached_ids = [];

		// 先从缓存获取
		foreach ($freight_ids as $freight_id) {
			$cache_key = "freight_template_{$freight_id}";
			$template = cache($cache_key);
			if ($template !== false) {
				$templates[$freight_id] = $template;
			} else {
				$uncached_ids[] = $freight_id;
			}
		}

		// 查询未缓存的数据
		if (!empty($uncached_ids)) {
			$db_templates = model('app\api\model\wanlshop\ShopFreight')
				->whereIn('id', $uncached_ids)
				->column('*', 'id');

			foreach ($uncached_ids as $freight_id) {
				if (isset($db_templates[$freight_id])) {
					$templates[$freight_id] = $db_templates[$freight_id];
					// 缓存1小时
					cache("freight_template_{$freight_id}", $db_templates[$freight_id], 3600);
				}
			}
		}

		return $templates;
	}

	/**
	 * 计算分佣金额
	 */
	private function calculateCommission($actual_payment, $cost_price, $number, $goods_id)
	{
		$sq_money = 0;
		$qy_money = 0;
		$ylgw_money = 0;

		// 如果不是城市运营商自己买则发放奖励
		if($this->auth->is_qydl != 1){
			// 从预加载数据或缓存获取商品分佣比例
			$cache_key = "goods_commission_{$goods_id}";
			$goods_commission = cache($cache_key);

			if ($goods_commission === false) {
				$goods_commission = model('app\api\model\wanlshop\Goods')
					->where('id', $goods_id)
					->field('id,shequ_bili,service_bili,quyu_bili')
					->find();
				if($goods_commission) {
					cache($cache_key, $goods_commission, 600); // 缓存10分钟
				}
			}

			if($goods_commission) {
				$quyu_bili = $goods_commission['quyu_bili'] > 0 ? $goods_commission['quyu_bili'] : config('site.goods_quyu_bili');
				$shequ_bili = $goods_commission['shequ_bili'] > 0 ? $goods_commission['shequ_bili'] : config('site.goods_shequ_bili');

				$lirun = $actual_payment - $cost_price * $number; // 获取利润

				if($lirun > 0){
					// 获取用户关系 - 使用缓存
					$user_relations = $this->getUserRelationsWithCache($this->auth->id);
					$sq_user = $user_relations['sq_user'];
					$qy_user = $user_relations['qy_user'];
					$ylgw_user = $user_relations['ylgw_user'];

					if($sq_user){
						if($sq_user['id'] == $this->auth->id){
							// 如果购买用户自己就是养老院长，不分佣（因为已享受优惠价格）
							$sq_money = 0;
						}else{
							if($qy_user){
								if($qy_user['id'] == $sq_user['id']){
									$qy_money = truncateDecimal($lirun * ($quyu_bili / 100));
								}else{
									$sq_money = truncateDecimal($lirun * ($shequ_bili / 100));
									$qy_money = truncateDecimal($lirun * ($quyu_bili / 100)) - $sq_money;
								}
							}else{
								$qy_money = 0;
								$sq_money = truncateDecimal($lirun * ($shequ_bili / 100));
							}
						}
					}else{
						$sq_money = 0;
						if($qy_user){
							$qy_money = truncateDecimal($lirun * ($quyu_bili / 100));
						}
					}

					if($qy_user){
						if($sq_user && $sq_user['id'] != $qy_user['id']){
							$sq_moneys = truncateDecimal($lirun * ($shequ_bili / 100));
							$qy_money = truncateDecimal($lirun * ($quyu_bili / 100)) - $sq_moneys;
						}else{
							$qy_money = truncateDecimal($lirun * ($quyu_bili / 100));
						}
					}else{
						$qy_money = 0;
					}

					// 计算养老顾问分佣 - 基于利润的50%
					if($ylgw_user){
						// 养老顾问分佣 = (养老顾问支付价格 - 养老院长优惠价格) × 50%
						// 即：利润 × 50%
						$ylgw_money = truncateDecimal($lirun * (config('site.gjia_bili') / 100));
					}
				}
			}
		}

		return [
			'shequ_d_bili' => $sq_money,
			'quyu_d_bili' => $qy_money,
			'ylgw_d_bili' => $ylgw_money
		];
	}

	/**
	 * 获取用户关系（带缓存）
	 */
	private function getUserRelationsWithCache($user_id)
	{
		$cache_key = "user_relations_{$user_id}";
		$relations = cache($cache_key);

		if ($relations === false) {
			$sq_user = \app\common\model\User::getOneSq($user_id);
			$qy_user = \app\common\model\User::getOneQy($user_id);
			$ylgw_user = \app\common\model\User::getOneYlgw($user_id);

			$relations = [
				'sq_user' => $sq_user,
				'qy_user' => $qy_user,
				'ylgw_user' => $ylgw_user
			];

			cache($cache_key, $relations, 1800); // 缓存30分钟
		}

		return $relations;
	}

}