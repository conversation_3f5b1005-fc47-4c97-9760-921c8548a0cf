# 用户身份管理功能说明

## 功能概述
在后台用户管理中增加了"是否是养老顾问"的下拉选择，并实现了用户身份唯一性验证，确保每个用户只能拥有一个身份。

## 修改内容

### 1. 用户模型 (application/admin/model/User.php)
- 新增 `getYlgwList()` 方法，返回养老顾问选项列表
- 提供 "否"(0) 和 "是"(1) 两个选项

### 2. 用户控制器 (application/admin/controller/user/User.php)
- 在 `_initialize()` 方法中添加 `ylgwList` 变量赋值
- 在 `add()` 方法中添加身份唯一性验证逻辑
- 在 `edit()` 方法中添加身份唯一性验证逻辑

### 3. 用户编辑视图 (application/admin/view/user/user/edit.html)
- 在城市运营商选项后添加养老顾问选择下拉框
- 使用 `ylgwList` 数据渲染选项
- 保持与其他身份选择一致的样式

### 4. 用户添加视图 (application/admin/view/user/user/add.html)
- 新建用户添加页面模板
- 包含所有必要的用户信息字段
- 包含养老顾问身份选择

## 身份验证逻辑

### 验证规则
系统会检查以下四个身份字段：
- `is_sqdl` - 养老院长
- `is_qydl` - 城市运营商  
- `is_ylgw` - 养老顾问
- `is_shop` - 供货商

### 验证过程
1. 获取用户提交的表单数据
2. 遍历所有身份字段，统计值为1的字段数量
3. 如果超过1个身份被选择，返回错误信息："用户只能有一个身份，请选择其中一个身份"
4. 验证通过后继续执行保存操作

### 适用场景
- 添加新用户时
- 编辑现有用户时
- 确保数据一致性和业务逻辑正确性

## 使用说明

### 管理员操作
1. 进入后台用户管理页面
2. 点击"添加"或"编辑"用户
3. 在表单中选择用户身份（养老院长、城市运营商、养老顾问、供货商中的一个）
4. 如果选择多个身份，系统会提示错误并阻止保存
5. 确保只选择一个身份后即可正常保存

### 前端显示
- 养老顾问选择框位于城市运营商选择框下方
- 使用标准的Bootstrap下拉选择样式
- 默认选择"否"(0)

## 技术实现

### 后端验证
```php
// 身份唯一性验证
$identityCount = 0;
$identityFields = ['is_sqdl', 'is_qydl', 'is_ylgw', 'is_shop'];

foreach ($identityFields as $field) {
    if (isset($params[$field]) && $params[$field] == 1) {
        $identityCount++;
    }
}

if ($identityCount > 1) {
    $this->error('用户只能有一个身份，请选择其中一个身份');
}
```

### 前端表单
```html
<div class="form-group">
    <label class="control-label col-xs-12 col-sm-2">{:__('养老顾问')}:</label>
    <div class="col-xs-12 col-sm-4">
        <select id="c-is_ylgw" data-rule="required" class="form-control selectpicker" name="row[is_ylgw]">
            {foreach name="ylgwList" item="vo"}
            <option value="{$key}" {in name="key" value="$row.is_ylgw"}selected{/in}>{$vo}</option>
            {/foreach}
        </select>
    </div>
</div>
```

## 注意事项
1. 该功能确保了用户身份的唯一性，符合业务逻辑要求
2. 验证在服务器端进行，确保数据安全性
3. 错误提示清晰明确，便于用户理解和操作
4. 与现有的身份管理功能保持一致的用户体验
