<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Log;

class MatchDeans extends Command
{
    protected function configure()
    {
        $this->setName('match:deans')
            ->setDescription('Match nursing home deans to city operators based on address.');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("Starting the matching process...");
        Log::notice("Starting the matching process...");

        // 1. 获取所有城市运营商 (is_qydl = 1)
        $operators = Db::name('user')
            ->alias('u')
            ->join('user_area ua', 'u.id = ua.user_id')
            ->where('u.is_qydl', 1)
            ->field('u.id AS user_id, ua.addr')
            ->select();

        if (empty($operators)) {
            $output->writeln("No city operators found.");
            Log::notice("No city operators found.");
            return;
        }

        $operatorMap = [];
        foreach ($operators as $operator) {
            if (!empty($operator['addr'])) {
                $operatorMap[$operator['addr']] = $operator['user_id'];
            }
        }
        $output->writeln("Found " . count($operatorMap) . " unique city operators with addresses.");
        Log::notice("Found " . count($operatorMap) . " unique city operators with addresses.");

        // 2. 获取所有养老院长 (is_sqdl = 1)
        $deans = Db::name('user')
            ->alias('u')
            ->where('u.is_sqdl', 1)
            ->where('u.parent_id', 0)
            ->field(['u.id', 'u.addr', 'u.parent_id'])
            ->select();
      
        if (empty($deans)) {
            $output->writeln("No deans found.");
            Log::notice("No deans found.");
            return;
        }
        $output->writeln("Found " . count($deans) . " deans to process.");
        Log::notice("Found " . count($deans) . " deans to process.");

        $matchedCount = 0;
        $unmatchedCount = 0;

        // 3. 匹配和更新
        foreach ($deans as $dean) {
            if (empty($dean['addr'])) {
                $output->writeln("Dean ID: {$dean['id']} has no address, skipping.");
                Log::notice("Dean ID: {$dean['id']} has no address, skipping.");
                $unmatchedCount++;
                continue;
            }

            if (isset($operatorMap[$dean['addr']])) {
                $operatorId = $operatorMap[$dean['addr']];
                // 检查 parent_id 是否需要更新
                if ($dean['parent_id'] != $operatorId) {
                    Db::name('user')->where('id', $dean['id'])->update(['parent_id' => $operatorId]);
                    $output->writeln("Matched Dean ID: {$dean['id']} to Operator ID: {$operatorId} based on address: {$dean['addr']}");
                    Log::notice("Matched Dean ID: {$dean['id']} to Operator ID: {$operatorId} based on address: {$dean['addr']}");
                    $matchedCount++;
                } else {
                    $output->writeln("Dean ID: {$dean['id']} is already correctly matched with Operator ID: {$operatorId}.");
                    Log::notice("Dean ID: {$dean['id']} is already correctly matched with Operator ID: {$operatorId}.");
                }
            } else {
                $output->writeln("No operator found for Dean ID: {$dean['id']} with address: {$dean['addr']}");
                Log::notice("No operator found for Dean ID: {$dean['id']} with address: {$dean['addr']}");
                $unmatchedCount++;
            }
        }

        $output->writeln("Matching process finished.");
        $output->writeln("Total matched and updated: " . $matchedCount);
        $output->writeln("Total unmatched or skipped: " . $unmatchedCount);
        Log::notice("Matching process finished. Matched: {$matchedCount}, Unmatched: {$unmatchedCount}");
    }
}
