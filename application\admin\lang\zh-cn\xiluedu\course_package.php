<?php

return [
    'Id'                    => 'ID',
    'Course_id'             => '课程ID',
    'Name'                  => '套餐名称',
    'Description'           => '套餐描述',
    'Quantity'              => '套餐数量',
    'Original_price'        => '原价',
    'Qydl_price'           => '城市运营商价格',
    'Sqdl_price'           => '养老院长价格',
    'Ylgw_price'           => '养老顾问价格',
    'User_types'           => '适用人员',
    'Sort'                 => '排序',
    'Status'               => '状态',
    'Status 0'             => '禁用',
    'Status 1'             => '启用',
    'Createtime'           => '创建时间',
    'Updatetime'           => '更新时间',
    'Package management'   => '套餐管理',
    'Add package'          => '添加套餐',
    'Edit package'         => '编辑套餐',
    'Delete package'       => '删除套餐',
    'Package name'         => '套餐名称',
    'Package description'  => '套餐描述',
    'Package quantity'     => '套餐数量',
    'Original price'       => '原价',
    'City manager price'   => '城市运营商价格',
    'Nursing home director price' => '养老院长价格',
    'Elderly advisor price' => '养老顾问价格',
    'Applicable users'     => '适用人员',
    'City manager'         => '城市运营商',
    'Nursing home director' => '养老院长',
    'Elderly advisor'      => '养老顾问',
    'Regular user'         => '普通用户',
    'Sort order'           => '排序',
    'Enable'               => '启用',
    'Disable'              => '禁用',
    'Total price'          => '总价',
    'Package contains'     => '套餐包含',
    'items'                => '个',
    'Switch status'        => '切换状态',
    'Back to course list'  => '返回课程列表',
    'Course package management' => '课程套餐管理',
    'Price package'        => '价格套餐',
    'Package settings'     => '套餐设置',
    'User type settings'   => '用户类型设置',
    'Price settings'       => '价格设置',
    'Basic settings'       => '基本设置',
];
