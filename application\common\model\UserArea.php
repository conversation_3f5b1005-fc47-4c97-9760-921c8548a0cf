<?php

namespace app\common\model;

use think\Model;


class UserArea extends Model
{

    

    

    // 表名
    protected $name = 'user_area';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 根据完整地址获取城市运营商
     * @param string $province 省份名称
     * @param string $city 城市名称
     * @param string $district 区域名称
     * @return array|null
     */
    public static function getCityManagerByAddress($province, $city, $district)
    {
        // 构造完整的地址字符串，例如 "河南省/郑州市/二七区"
        $full_address = $province . '/' . $city . '/' . $district;
        // 查找该区域对应的城市运营商
        // 假设user_area表中有一个'addr'字段，且关联的user是is_qydl=1
        $userArea = self::where('addr', $full_address) // 精确匹配完整地址
                        ->alias('ua')
                        ->join('user u', 'ua.user_id = u.id')
                        ->where('u.is_qydl', 1)
                        ->field('ua.*, u.id as user_id, u.nickname, u.is_qydl') // 返回user_id
                        ->find();
        return $userArea ? $userArea->toArray() : null;
    }

    /**
     * 获取默认城市运营商（当用户无地址时）
     * @return array|null
     */
    public static function getDefaultCityManager()
    {
        // 查找一个默认的城市运营商，例如：is_qydl=1 且 parent_id=0 的用户
        // 或者简单地返回第一个找到的城市运营商
        $defaultCityManager = \app\common\model\User::where('is_qydl', 1)
                                                    ->where('parent_id', 0) // 假设parent_id=0表示顶级QYDL
                                                    ->find();
        return $defaultCityManager ? $defaultCityManager->toArray() : null;
    }
}
