<?php
namespace app\api\controller\service;

use app\admin\model\service\skill\UserSkill;
use app\api\model\service\ApplySkill;
use app\common\controller\Api;
use app\common\model\service\Shenhe;
use think\Exception;
use think\Loader;
use think\Db;
/**
 * 首页接口
 */
class Skill extends Api
{
    protected $noNeedLogin = ['nearSkill','searchSkill','skillInfo','getSampleSkill'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();

    }

    /**
     * 城市运营商申诉列表
     * <AUTHOR>
     * @date 2024/9/28  下午6:16
     * @notes
     */
    public function areaShensuList()
    {
        $status = $this->request->param('status');
        $user_id = $this->request->param('user_id');
        $where = [];
        if($status){
            $where['status'] = $status;
        }
        $where['user_id'] = $user_id;
        $list = \app\common\model\service\Shenhe::where($where)->paginate();
        $this->success('ok',$list);
    }

    /**
     * 城市运营商申诉列表详情
     * <AUTHOR>
     * @date 2024/9/28  下午6:27
     * @notes
     */
    public function areaShensuDetails()
    {
        $id = $this->request->param('id');
        $info = \app\common\model\service\Shenhe::get($id);
        if(!$info){
            $this->error('申诉信息错误');
        }
        $info['user']  = \app\api\model\service\Skill::getSimpleSkillForUserId($info->user_id);;
        $this->success('ok',$info);
    }

    /**
     * 服务人员申诉
     * <AUTHOR>
     * @date 2024/9/28  下午6:04
     * @notes
     */
    public function skillShensu()
    {
        $data = $this->request->post();
        $data['user_id'] = $this->auth->id;
        $res = \app\common\model\service\Shenhe::create($data);
        if(!$res){
            $this->error('申诉失败');
        }
        $this->success('申诉成功',$res);
    }

    /**
     * 服务人员申诉
     * <AUTHOR>
     * @date 2024/9/28  下午6:04
     * @notes
     */
    public function skillShensuReply()
    {
        $data = $this->request->post();
        $s=Shenhe::get($data['id']);
        if(empty($s)) $this->error('参数错误');
        $s->status=2;
        $s->hf_user_id=$this->auth->id;
        $s->hf_text=$data['hf_text'];
        $s->hf_time=time();
        $s->save();
        $this->success('',$s);
    }

    /**
     * 申诉列表
     * <AUTHOR>
     * @date 2024/9/28  下午6:09
     * @notes
     */
    public function shensuList()
    {
        $status = $this->request->param('status');
        $where = [];
        if($status){
            $where['status'] = $status;
        }
        $where['user_id'] = $this->auth->id;
        $list = \app\common\model\service\Shenhe::where($where)->paginate();
        $this->success('ok',$list);
    }

    /**
     * 申诉详情
     * <AUTHOR>
     * @date 2024/9/28  下午6:10
     * @notes
     */
    public function shenheDetails()
    {
        $id = $this->request->param('id');
        $info = \app\common\model\service\Shenhe::get($id);
        if(!$info){
            $this->error('申诉信息错误');
        }
        $this->success('ok',$info);
    }

    /**
     * 工装规范列表
     * <AUTHOR>
     * @date 2024/9/28  上午10:40
     * @notes
     */
    public function gzList()
    {
        $status = $this->request->param('type');
        $where = [];
        if($status || $status === 0){
            $where['status'] = $status;
        }
        $where['user_id'] = $this->auth->id;
        $where['cj_time'] = ['<',time()];
        $list = \app\common\model\service\Gz::where($where)->paginate()->each(function ($item){
            if(!$item['user_image']){
                $item['user_image'] = isset(config('site.gz_give_images')[0]) ? config('site.gz_give_images')[0] : '';
            }
            $item['sy_time'] = 15*60+time() - $item['cj_time'] < 0 ? 0 : 15*60+time() - $item['cj_time'];
            return $item;
        });
        $this->success('ok',$list);
    }

    /**
     * 上传工装规范
     * <AUTHOR>
     * @date 2024/9/28  上午11:07
     * @notes
     */
    public function gzSubmit()
    {
        $user_image = $this->request->post('user_image');
        $id = $this->request->post('id');
        if(!$user_image){
            $this->error('请上传图片');
        }
        $gz = \app\common\model\service\Gz::get($id);
        if(!$gz || $gz->status != 0){
            $this->error('工装规范信息错误');
        }
        $gz->user_image = $user_image;
        $gz->status = 1;
        $gz->save();
        $this->success('上传成功');
    }


    /**
     * 订单转接
     * <AUTHOR>
     * @date 2024/9/25  下午3:39
     * @notes
     */
    public function skillZj()
    {
        $type = $this->request->post('type');
        $name = $this->request->post('name');
        $mobile = $this->request->post('mobile');
        $order_id = $this->request->post('order_id');
        $djdd_reason = $this->request->post('djdd_reason');
        $skill_id = \app\api\model\service\Skill::where('user_id',$this->auth->id)->value('id');

        $order = model('app\api\model\service\Order')->where(['id'=>$order_id])->find();
        if(!$order){
            $this->error('订单信息错误');
        }
        if($order->status != 2){
            $this->error('当前订单状态不能转单');
        }
        Db::startTrans();
        try{
            //更新订单状态
            $order->skill_id = 0;
            $order->status = 1;
            $order->is_pool = 1;
            $insert = [
                'order_id'=>$order_id,
                'user_id'=>$this->auth->id,
                'createtime'=>time()
            ];
            //查询当月申请次数
            $month_zj_num = \app\common\model\service\OrderZj::where('user_id',$this->auth->id)->where('type',$type)
                ->where('createtime','between time',[strtotime(date('Y-m-01')),time()])
                ->count();
            if($type == 0){//直接转接
                //获取当前服务人员信息
                $where = [];
                $where['mobile'] = $mobile;
                $where['hx_range'] = $order['district'];
//                $user = \app\common\model\User::where(['mobile'=>$mobile])->find();
                $skill = model('app\admin\model\service\skill\Skill')->where($where)->find();

                if(!$skill || $skill['id']==$skill_id){
                    exception('服务人员信息错误');
                }
                $insert['type'] = 0;
                $order->is_pool = 0;
                $order->skill_id = $skill_id;
                $order->status = 2;
                $order->zj_skill_id = $skill['id'];
                $order->zj_time = time();


                \app\api\model\wanlshop\Notice::sendUserMsg($skill['user_id'],'订单转接','您收到一个订单转接','service_order',$order->id,1);

                //查询是否超过当月次数
                if($month_zj_num >= config('service_zjzj')){
                    //扣除服务费
                   \app\api\model\service\Skill::service_score($this->auth->id,-1,'直接转接次数超限扣除质量分',$order_id,2);
                }
            }else if($type == 1){//抢单大厅
                $insert['type'] = 1;
                $order->status = 1;
                $order->is_pool = 1;
                $order->skill_id = null;
                $order->zj_time = time();
                //查询是否超过当月次数
                if($month_zj_num >= config('service_zjzj')){
                    //扣除服务费
                    \app\api\model\service\Skill::service_score($this->auth->id,-1,'抢单大厅转接次数超限扣除质量分',$order_id,2);
                }
            }elseif($type == 2){//呼叫调度
                $insert['type'] = 2;
                //查询是否超过当月次数
                if($month_zj_num >= config('service_zjdt')){
                    //扣除服务费
                    \app\api\model\service\Skill::service_score($this->auth->id,-1,'呼叫调度次数超限扣除质量分',$order_id,2);
                }
                $insert['djdd_reason'] = $djdd_reason;
            }
            $order->save();
            $res = \app\common\model\service\OrderZj::create($insert);
            if(!$res){
                exception('转接失败');
            }
            Db::commit();
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('转接成功');
    }

    /**
     * 订单取消转接
     * <AUTHOR>
     * @date 2024/12/25  下午3:39
     * @notes
     */
    public function cancelZj()
    {
        $order_id = $this->request->post('order_id');
        $order = model('app\api\model\service\Order')->where(['id'=>$order_id])->find();
        if(!$order){
            $this->error('订单信息错误');
        }
        if($order->zj_skill_id == 0){
            $this->error('当前订单状态不能取消转接');
        }
        Db::startTrans();
        try{
            //更新订单状态
            $order->status=2;
            $order->zj_skill_id = 0;
            $order->zj_time = null;
            $order->save();
            Db::commit();
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('转接成功');
    }


    public function acceptZj()
    {
        $order_id = $this->request->post('order_id');
        $order = model('app\api\model\service\Order')->where(['id'=>$order_id])->find();
        if(!$order){
            $this->error('订单信息错误');
        }
        if($order->zj_skill_id == 0){
            $this->error('当前订单状态不能接受转接');
        }
        $user=$this->auth->getUserinfo();
        if($user['skill_status'] =='hidden' && $user->hidden_time > time() ) $this->error('您的账号已被冻结！','',501);
        Db::startTrans();
        try{
            //更新订单状态
            $order->skill_id=$order->zj_skill_id;
            $order->status=2;
            $order->zj_skill_id = 0;
            $order->zj_time = null;
            $order->save();
            Db::commit();
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('接受成功');
    }

    /**
     * 拒绝接单
     * @return void
     */
    public function rejectZj()
    {
        $order_id=$this->request->param('order_id');
        $order = model('app\api\model\service\Order')->where(['id'=>$order_id])->find();
        if(!$order){
            $this->error('订单信息错误');
        }
        Db::startTrans();
        try{
            //更新订单状态
            $order->status=2;
            $order->zj_skill_id = 0;
            $order->zj_time = null;
            $order->save();
            Db::commit();
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }

        $this->success("操作成功");
    }
    /**
     * 城市运营商查看-已入住服务人员列表
     * <AUTHOR>
     * @date 2024/9/23  下午2:15
     * @notes
     */
    public function skillList()
    {
        $status = $this->request->param('state');
        $name = $this->request->param('name');
        //获取此城市运营商代理区域
        $user =$this->auth->getUserinfo();
        $user_id =$user['qydl_staff']!=0 ? $user['qydl_staff'] :$this->auth->id;
        $district = model('app\common\model\UserArea')->where('user_id',$user_id)->value('district');
        if(!$district){
            $this->error('您还不是城市运营商');
        }
        $where = [];
        $where['s.hx_range'] = $district;
        if($status){
            $where['u.skill_status'] = $status;
        }
        if(!empty($name)){
            $where['s.name']=['like',"%$name%"];
        }
        $list = \app\api\model\service\Skill::alias('s')
            ->join('user u','u.id=s.user_id','left')
            ->where($where)
            ->field('*')
            ->paginate()->each(function (&$item){
                $hidden_time = time() - $item['hidden_time'];
                //冻结时间
                if($item['skill_status'] == 'hidden'){
                    if($hidden_time > 0){
                        $item['hidden_time'] = 0;
                    }else{
                        $item['hidden_time'] = date('Y-m-d H:i:s',$item['hidden_time']);
                    }
                }else{
                    $item['hidden_time'] = 0;
                }
//                $s=Shenhe::get($data['id']);
//                if(empty($s)) $this->error('参数错误');
//                $s->status=2;
//                $s->hf_user_id=$this->auth->id;
//                $s->hf_text=$data['hf_text'];
//                $s->hf_time=time();
//                $s->save();
                $item['shensu_num']=Shenhe::where('user_id',$item['user_id'])->whereNull('hf_text')->count();
            });
        $this->success('ok',$list);
    }

    /**
     * 城市运营商查看-已入住服务人员列表
     * <AUTHOR>
     * @date 2024/9/23  下午2:15
     * @notes
     */
    public function skillStatusInfo()
    {
        $user_id = $this->request->param('user_id');
        $where['u.id']=$user_id;
        $list = \app\api\model\service\Skill::alias('s')
            ->join('user u','u.id=s.user_id','left')
            ->where($where)
            ->field('*')
            ->find();

        if(empty($list)) $this->error('参数错误');
        $hidden_time = time() - $list['hidden_time'];
        //冻结时间
        if($list['skill_status'] == 'hidden'){
            if($hidden_time > 0){
                $list['hidden_time'] = 0;
            }else{
                $list['hidden_time'] = date('Y-m-d H:i:s',$list['hidden_time']);
            }
        }else{
            $list['hidden_time'] = 0;
        }
        if(!empty($list['idcard'])){
            $list['age'] = $this->getAgeFromIdCard($list['idcard']);
        }
        $this->success('ok',$list);
    }

    function getAgeFromIdCard($idCard) {
        // 身份证号码为18位，前6位为地址码，接下来的8位为出生日期，最后的是验证码
        $birthday = substr($idCard, 6, 8);
        // 将出生日期转换为时间戳
        $timeStamp = strtotime($birthday . ' 00:00:00');
        // 获取当前时间戳
        $now = time();
        // 计算年龄
        $age = date('Y', $now) - date('Y', $timeStamp);
        // 如果当前月份小于出生月份则年龄减一
        if (date('m', $now) < date('m', $timeStamp)) {
            $age--;
        }
        // 如果当前日期小于出生日期则年龄减一
        if (date('d', $now) < date('d', $timeStamp)) {
            $age--;
        }
        return $age;
    }
    /**
     * 更新冻结状态
     * <AUTHOR>
     * @date 2024/9/23  下午5:18
     * @notes
     */
    public function skillUpdateStatus()
    {
        $user_id = $this->request->param('user_id');
        $time = $this->request->param('time');
        $type = $this->request->param('type');
        $user = model('app\common\model\User')->get($user_id);
        $skill=\app\admin\model\service\skill\Skill::where(['user_id'=>$user_id])->find();
        if(empty($skill) ) $this->error('服务者信息错误');
        if($type == 1){//冻结
            $hidden_time = strtotime($time);
            if($hidden_time < time()){
                $this->error('冻结时间不能小于当前时间');
            }
            $user->hidden_time = $hidden_time;
            $user->skill_status = 'hidden';
            $user->save();
        }else{//解冻
            $user->hidden_time = 0;
            $user->skill_status = 'normal';
            $user->save();
        }
        $this->success('提交成功');
    }


    /**
     * 服务人员调分
     * <AUTHOR>
     * @date 2024/9/23  下午3:30
     * @notes
     */
    public function skillUpdateScore()
    {
        //获取此城市运营商代理区域
        $user =$this->auth->getUserinfo();
        $user_id2 =$user['qydl_staff']!=0 ? $user['qydl_staff'] :$this->auth->id;
        $district = model('app\common\model\UserArea')->where('user_id',$user_id2)->value('district');
        if(!$district){
            $this->error('您还不是城市运营商');
        }
        $user_id = $this->request->param('user_id');
        $type = $this->request->param('type');
        $score = $this->request->param('score');
        if($type == 1){//调整分数 加分
            \app\api\model\service\Skill::service_score($user_id,$score,'管理员调整质量分',$this->auth->id,5);
        }else{//减分
            \app\api\model\service\Skill::service_score($user_id,-$score,'管理员调整质量分',$this->auth->id,5);
        }
        $this->success('操作成功');
    }

    /**
     * 服务人员申请列表
     * <AUTHOR>
     * @date 2024/9/18  下午3:01
     * @notes
     */
    public function applyList()
    {
        $state = $this->request->param('state');
        $name = $this->request->param('name');
        $state = $state ? $state : 0;
        $where = [];
        $where['state'] = $state;
        if($name){
            $where['name'] = ['like','%'.$name.'%'];
        }
        $applySkill = new \app\api\model\service\ApplySkill();
        //获取此城市运营商代理区域
        $user=$this->auth->getUserinfo();
        $user_id= $user['qydl_staff'] != 0 ? $user['qydl_staff'] : $this->auth->id;
        $district = model('app\common\model\UserArea')->where('user_id',$user_id)->value('district');
        if(!$district){
            $this->error('您还不是城市运营商');
        }
        $where['hx_range'] = $district;
        $list = $applySkill->where($where)->order('id desc')->paginate();
        $this->success('ok',$list);
    }

    /**
     * 服务人员申请详情
     * <AUTHOR>
     * @date 2024/9/18  下午3:34
     * @notes
     */
    public function applyDetails()
    {
        $id = $this->request->param('id');
        $info = model('app\api\model\service\ApplySkill')->where(['id'=>$id])->find();
        if(!$info){
            $this->error('信息不存在');
        }
        $this->success('ok',$info);
    }

    /**
     * 审核申请
     * <AUTHOR>
     * @date 2024/9/18  下午3:44
     * @notes
     */
    public function shenheApply()
    {
        $state = $this->request->param('state');
        $id = $this->request->param('id');
        $note = $this->request->param('note');
        $applySkill = new \app\api\model\service\ApplySkill();
        $row = $applySkill->where(['id'=>$id])->find();
        if(!$row){
            $this->error('信息不存在');
        }
        Db::startTrans();
        try {
            if($state == -1){//审核驳回
                $res = $applySkill->allowField(true)->save(['state'=>$state,'note'=>$note],['id'=>$id]);
                \app\api\model\wanlshop\Notice::sendUserMsg($row->user_id,'审核通知','你的服务者资料申请被驳回，请前往查看修改','service_notice',$id,$this->auth->id);
            }else{//审核通过
                $res=$applySkill->save(['state'=>$state,'applytype'=>1],['id'=>$id]);
                model('app\admin\model\service\User')->where('user_id',$row->user_id)->update(['is_skill'=>1]);
                $data = db('service_apply_skill')->where('id',$row->id)->find();
                $data['percent'] = 0;
                $data['accept_nums'] = \app\api\model\service\ProjectConfig::getProjectConfig('accept_nums');
                unset($data['id']);
                $count = model('app\admin\model\service\skill\Skill')->where('state',1)->count();
                $data['code'] = str_pad(date('d').$count,8,0);
                $data['mobile'] = model('app\admin\model\service\User')->where(['user_id'=>$row->user_id])->value('mobile');
                $data['updatetime'] = time();
                $skill=\app\admin\model\service\skill\Skill::where(['user_id'=>$row->user_id])->find();
                $f=false;
                if(empty($skill)){
                    $f=true;
                    $skill = new \app\admin\model\service\skill\Skill($data);
                }else{
                    $skill->data($data);
                }

                $skill->allowField(true)->save();
                if ($f) \app\admin\model\service\skill\Applyskill::createTime($skill->id);
                $cert = new \app\common\model\service\CertLog();
//                $cert->allowField(true)->save(['status'=>1,'bh_text'=>""],['user_id'=>$row->user_id,'status'=>0]);
                \app\api\model\wanlshop\Notice::sendUserMsg($row->user_id,'审核通知','你的服务者资料申请已通过！','service_notice',$id,$this->auth->id);
            }
            Db::commit();

        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('审核成功');
    }

    /**
     * 申请服务人员,完善资料
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function apply()
    {
        $uid = $this->auth->id;
        $userInfo = model('app\api\model\service\UserInfo')->where(['user_id'=>$uid])->field('id,is_skill')->find();
        $info = model('app\api\model\service\ApplySkill')->order('id desc')->where(['user_id'=>$uid])->find();
        $post = input('post.','','trim,strip_tags,htmlspecialchars,xss_clean');
        $type = input('type','');
        $applySkill = new \app\api\model\service\ApplySkill();
        $post['user_id'] = $uid;
        $post['updatetime'] = time();
        if(in_array($type,['add','edit']))
        {
            $validate = Loader::validate('service.ApplySkill');
            if(!$validate->scene($type)->check($post)){
                $this->error($validate->getError());
            }
        }
        if($type == 'add')
        {
            $userInfo['is_skill'] == 1 && $this->error('请勿重复申请');
//             $post['xy_sign'] = json_encode($post['xy_sign']);
            unset($post['type']);
            $applySkill->data($post)->save();
            $this->success('申请已提交,等待审核');
        }elseif($type == 'edit') {
            // $post['xy_sign'] = json_encode($post);
            unset($post['type']);
            $post['state'] = 0;
            $applySkill->allowField(true)->save($post,['id'=>$info['id']]);
            $this->success('修改成功');
        }elseif ($type == 'perfect') {
//            $post['xy_sign'] = json_encode($post['xy_sign']);
            $post['state'] = 0;
            $post['applytype'] = 1;
            $applySkill->allowField(true)->save($post,['id'=>$info['id']]);
            $this->success('申请已提交,等待审核');
        }
        if($info){
            $info['skillCateName'] = \app\api\model\service\SkillCate::getName($info['skill_cate_id']);
        }
        $this->success('信息返回成功',$info);
    }

    /**
     * 服务人员入驻提交-证书信息
     * <AUTHOR>
     * @date 2024/8/20  下午7:04
     * @notes
     */
    public function applyCert()
    {
        $cert_id = $this->request->post('cert_id');
        $z_image = $this->request->post('z_image');
        $b_image = $this->request->post('b_image');
        $skill_ids = $this->request->post('skill_ids','');
        $type = $this->request->post('type');
        $id = $this->request->post('id');
        $district = $this->request->post('district');
        $status = $this->request->post('status');
        if(!$cert_id){
            $this->error('参数错误');
        }
        $cert = new \app\common\model\service\CertLog();
//        $skill = \app\api\model\service\Skill::where(['user_id'=>$this->auth->id])->field('id,goods_ids,city,hx_range,zd_range,qt_range')->find();
//        if(empty($skill)) $this->error('身份错误');
//        $district = $skill['hx_range'];
        $data = [
            'district'=> $district,
            'user_id' => $this->auth->id,
            'cert_id' => $cert_id,
            'z_image' => $z_image,
            'b_image' => $b_image,
            'skill_ids' => $skill_ids,
            'sign_agreement'=>$this->request->post('sign_agreement',0),
            'sign_img'=>$this->request->post('sign_img',""),
        ];

        if($cert_id ==10){
            $skill = \app\api\model\service\Skill::where(['user_id'=>$this->auth->id])->find();
            if(!empty($skill) && $skill['state']==1){
                $data['status'] = 1;
            }
        }
        if($cert_id ==9){
            $skill = \app\api\model\service\ApplySkill::where(['user_id'=>$this->auth->id,'applytype'=>1])->find();
            if(!empty($skill) && $skill['state']==1){
               // $data['status'] = 1;
            }
        }

        if(!$id){//添加
            $cert->data($data)->save();
        }else{//编辑
            $cert->allowField(true)->save($data,['id'=>$id]);
        }
        $this->success('申请已提交,等待审核',$cert);
    }

    /**
     * 获取当前自己的技能
     * <AUTHOR>
     * @date 2024/11/14  下午1:58
     * @notes
     */
    public function getMySkill()
    {
        $skills = UserSkill::where('user_id',$this->auth->id)->value('skill_ids');
        $skill_id_arr = explode(',',$skills);
        $skill_id_arr= array_unique($skill_id_arr);
        $res = [];
        //列出所有父级
        if($skill_id_arr){
            $p_info_ids = [];
            foreach ($skill_id_arr as $v){
                //查询父级
                $info = model('app\common\model\service\skill\Cate')->field('id,pid,name,image')->where('id',$v)->find();
                if($info){
                    $p_info_ids[$info['pid']][] = $info;
                }
            }
            foreach ($p_info_ids as $k=>$v){
                $info = model('app\common\model\service\skill\Cate')->field('id,pid,name,image')->where('id',$k)->find()->toArray();
                $data = [];
                $data['id'] = $k;
                $data['name'] = $info['name'];
                $data['children'] = $v;
                $res[] = $data;
            }
        }
        $this->success('ok',$res);
    }


    public function saveUserSkill()
    {
       $skill_ids=$this->request->post('skill_ids');
        $user_skill= UserSkill::where(['user_id'=>$this->auth->id])->find();
        if(empty($user_skill)){
            $r = UserSkill::create(['skill_ids'=>$skill_ids,'user_id'=>$this->auth->id]);
        }else {
            $user_skill->skill_ids = $skill_ids;
            $r = $user_skill->save();
        }
        $this->success('保存成功',$r);
    }

    /**
     * 获取拥有的证书技能
     * @return void
     */
    public function getMyCertSkill()
    {
        $cert = new \app\common\model\service\CertLog();
        $logs = $cert->where('user_id',$this->auth->id)->where('status',1)->select();
        $own_skill= UserSkill::where('user_id',$this->auth->id)->value('skill_ids');
        $own_skill_arr=explode(',',$own_skill);
        $ids_arr=[];
        foreach ($logs as  $log) {
            $all_skill = model('app\api\model\service\SkillCate')
                ->where(['state' => 1])
                ->where('find_in_set(:id,service_skil_cate_id)', ['id' => $log['cert_id']])
                ->column('id');
            $ids_arr=array_merge($ids_arr,$all_skill);
        }

        $no_cert_skill=\app\api\model\service\SkillCate::where(['state'=>1,'service_skil_cate_id'=>0])->column('id');
        $ids_arr=array_merge($ids_arr,$no_cert_skill);
        $ids_arr=array_unique($ids_arr);
        if($ids_arr){
            $p_info_ids = [];
            foreach ($ids_arr as $v){
                if(in_array($v,$own_skill_arr)) continue;
                //查询父级
                $info = model('app\common\model\service\skill\Cate')->field('id,pid,name,image')->where('id',$v)->find()->toArray();
                if($info){
                    $p_info_ids[$info['pid']][] = $info;
                }
            }
            foreach ($p_info_ids as $k=>$v){
                $info = model('app\common\model\service\skill\Cate')->field('id,pid,name,image')->where('id',$k)->find();
                if(empty($info)) continue;
                $data = [];
                $data['id'] = $k;
                $data['name'] = $info['name'];
                $data['children'] = $v;
                $res[] = $data;
            }
        }
        $this->success('ok',['skills'=>$res ?? [],'own_skill'=>$own_skill??'']);


    }


    /**
     * 城市运营商-证书审核列表
     * <AUTHOR>
     * @date 2024/8/20  下午7:04
     * @notes
     */
    public function getCertList()
    {
        $status = $this->request->post('status',0);
        //获取此城市运营商代理区域
        $user=$this->auth->getUserinfo();
        $user_id=$user['qydl_staff']!=0?$user['qydl_staff']:$this->auth->id;
        $district = model('app\common\model\UserArea')->where('user_id',$user_id)->value('district');
        if(!$district){
            $this->error('您还不是城市运营商');
        }

        $cert = new \app\common\model\service\CertLog();
        $list = $cert->alias('c')
            ->join('user_area ua','ua.district=c.district','left')
            ->join('service_cert cert','cert.id=c.cert_id','left')
            ->where('ua.user_id',$user_id)
            ->where('c.status',$status)
            ->field('c.*,cert.name as cert_name')
            ->order('c.id desc')
            ->paginate();

        foreach ($list->items() as $k=>&$v){
//            $v['apply_user']=\app\common\model\User::getSimpleUser($v['user_id']);
//            $skill_id=\app\api\model\service\Skill::where(['user_id'=>$v['user_id']])->value('id');
            $v['apply_user']=\app\api\model\service\ApplySkill::where(['user_id'=>$v['user_id']])->find();
        }

        $this->success('ok',$list);
    }

    /**
     * 获取证书详情
     * <AUTHOR>
     * @date 2024/9/18  下午5:35
     * @notes
     */
    public function getCertDetails()
    {
        $id = $this->request->post('id');
        $cert = new \app\common\model\service\CertLog();
        $details = $cert->alias('c')
            ->join('user_area ua','ua.user_id=c.user_id','left')
            ->join('service_cert cert','cert.id=c.cert_id','left')
            ->where('c.id',$id)
            ->field('c.*,cert.name as cert_name')
            ->find();
        $details['apply_user']=\app\api\model\service\ApplySkill::where(['user_id'=>$details['user_id']])->find();
        $this->success('ok',$details);
    }

    /**
     * 审核证书
     * <AUTHOR>
     * @date 2024/9/18  下午5:36
     * @notes
     */
    public function shenheCert()
    {
        $status = $this->request->post('status');
        $id = $this->request->post('id');
        $bh_text = $this->request->post('bh_text');
        $cert = new \app\common\model\service\CertLog();
        $cert->allowField(true)->save(['status'=>$status,'bh_text'=>$bh_text],['id'=>$id]);
        $this->success('ok');
    }

    public function apply_o()
    {
        $uid = $this->auth->id;
        $userInfo = model('app\api\model\service\UserInfo')->where(['user_id'=>$uid])->field('id,is_skill')->find();
        $info = model('app\api\model\service\ApplySkill')->where(['user_id'=>$uid])->find();
        $post = input('post.','','trim,strip_tags,htmlspecialchars,xss_clean');
        $type = input('type','');
        $applySkill = new \app\api\model\service\ApplySkill();
        $post['user_id'] = $uid;
        $post['updatetime'] = time();
        if(in_array($type,['add','edit']))
        {
            $validate = Loader::validate('service.ApplySkill');
            if(!$validate->scene($type)->check($post)){
                $this->error($validate->getError());
            }
        }
        if($type == 'add')
        {
            $userInfo['is_skill'] == 1 && $this->error('请勿重复申请');
            unset($post['type']);
            $applySkill->data($post)->save();
            $this->success('申请已提交,等待审核');
        }elseif($type == 'edit') {
            unset($post['type']);
            $post['state'] = 0;
            $applySkill->allowField(true)->save($post,['id'=>$info['id']]);
            $this->success('申请已提交,等待审核');
        }elseif ($type == 'perfect') {
            $post['state'] = 0;
            $post['applytype'] = 1;
            $applySkill->allowField(true)->save($post,['id'=>$info['id']]);
            $this->success('申请已提交,等待审核');
        }
        if($info){
            $info['skillCateName'] = \app\api\model\service\SkillCate::getName($info['skill_cate_id']);
        }
        $this->success('信息返回成功',$info);
    }

    public function addGoodsTime()
    {
        $id = input('id/d','');
        $ids = input('ids','');
        $order = \app\api\model\service\Order::where(['id'=>$id,'state'=>['in',[2,3,4,5]]])->field('id,skill_id,actendtime')->find();
        $addIds = explode(',',$ids);
        $totalCostSeconds = 0;
        $payprice = 0;
        foreach ($addIds as $val)
        {
            $info = \app\api\model\service\GoodsAdd::where(['id'=>$val,'state'=>1])->find();
            if(!$info){
                $this->error('当前有附加项目无法使用,请刷新后重试');
            }
            $totalCostSeconds+=$info['cost_seconds'];
            $payprice+=$info['price'];
        }
        if($order)
        {
            $endTime = $order['actendtime']+$totalCostSeconds;
            $exist = \app\api\model\service\SkillTime::where(['skill_id'=>$order['skill_id'],'state'=>['>',0],'starttime'=>['between',[$order['actendtime'],$endTime]]])->value('id');
            $exist && $this->error('当前服务者已被预约,暂时无法接单');
        }
        $this->success('信息返回成功',['payprice'=>$payprice,'totalCostSeconds'=>$totalCostSeconds]);
    }



    /**
     * 首页附近服务人员
     * @return void
     */
    public function nearSkill()
    {
        $get = input('get.','','trim,strip_tags');
        $get['limit'] = 10;
        !$get['city'] && $this->error('城市信息缺失');
        (!$get['lng'] || !$get['lat']) && $this->error('定位信息异常');
        $nearSkill = \app\api\model\service\Skill::nearSkill($get);
        if($nearSkill)
        {
            foreach ($nearSkill as &$value)
            {
                $value['skillCate'] = model('app\api\model\service\SkillCate')->where('id',$value['skill_cate_id'])->value('name');
                $value['skillTime'] = \app\api\model\service\SkillTime::getSkillTime($value['id']);
                $value['good_comment_percent'] =  \app\api\model\service\Comment::skillGoodPercent(['skill_id'=>$value['id']]);
            }
        }
        $this->success('信息返回成功',$nearSkill);
    }

    /**
     * 查询服务人员列表
     * @return void
     */
    public function searchSkill()
    {
        $get = $this->request->param();
        $get['limit'] = 10;
        $get['page'] = input('page/d',1);
        !$get['city'] && $this->error('城市信息缺失');
        (!$get['lng'] || !$get['lat']) && $this->error('定位信息异常');
        $list = \app\api\model\service\Skill::searchSkill($get);
        if($list)
        {
            foreach ($list as &$value)
            {
                $value['shopname'] = $value['shop_id']?\app\api\model\service\Shop::getName($value['shop_id']):'';
                $value['skillTime'] = \app\api\model\service\SkillTime::getSkillTime($value['id']);
                $value['skillCate'] = \app\api\model\service\SkillCate::where('id',$value['skill_cate_id'])->value('name');
                $value['commentCount'] = \app\api\model\service\Comment::getCount(['skill_id'=>$value['id'],'state'=>1]);
                $value['good_comment_percent'] =  \app\api\model\service\Comment::skillGoodPercent(['skill_id'=>$value['id']]);
            }
        }
        $this->success('信息返回成功',$list);
    }


    /**
     * 获取服务人员详情
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function skillInfo()
    {
        $uid = $this->auth->isLogin()?$this->auth->id:'';
        $id = input('id/d','');
        $lng = input('lng');
        $lat = input('lat');
        $info = \app\api\model\service\Skill::getInfo($id,$uid);
        !$info && $this->error('服务人员信息异常');
        $info['distance'] = \addons\service\library\Common::distance($lng,$lat,$info['lng'],$info['lat']);
        $info['skillTime'] = \app\api\model\service\SkillTime::getSkillTime($info['id']);
        $info['good_comment_percent'] = \app\api\model\service\Comment::skillGoodPercent(['skill_id'=>$info['id']]);
        $this->success('信息返回成功',$info);
    }


    /**
     * 加入商户
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function joinShop()
    {
        $uid = $this->auth->id;
        $skill = \app\api\model\service\Skill::getSkillShop($uid);
        $data['skill'] = $skill;
        $data['joinShop'] = \app\api\model\service\JoinShop::where(['skill_id'=>$skill['id']])->order('id desc')->find();
        $type = input('type','','trim,strip_tags');
        if(is_numeric($type)){
            $skill['shop_id'] && $this->error('请勿重复申请');
            $info = \app\api\model\service\JoinShop::where(['skill_id'=>$skill['id'],'state'=>['>=',0]])->field('id,code,shop_name,state')->order('id desc')->find();
            ($info && $info['state']>=0) && $this->error('请勿重复申请');
            if($type)
            {
                $code = input('code/s','','trim,strip_tags');
                $shop = \app\api\model\service\Shop::where(['code'=>$code,'state'=>1])->field('id,name,code')->find();
            }else{
                $id = input('id/d','','trim,strip_tags');
                $shop = \app\api\model\service\Shop::where(['id'=>$id,'state'=>1])->field('id,name,code')->find();
            }
            !$shop && $this->error('所选商家信息异常');
            \app\api\model\service\JoinShop::create(['skill_id'=>$skill['id'],'skill_name'=>$skill['name'],'shop_id'=>$shop['id'],'code'=>$shop['code'],'shop_name'=>$shop['name']]);
            $this->success('申请已提交,等待审核');
        }
        $this->success('信息返回成功',$data);
    }



    public function skillIndex()
    {
        $uid = $this->auth->id;
        $skill = \app\api\model\service\Skill::where(['user_id'=>$uid])->field('id,code,ensure_price,name,sex,image,city,district,address,hx_range,zd_range,qt_range,goods_ids,skill_cate_id')->find();
        !$skill && $this->success('',[]);
        $skill['skillCateName'] = \app\api\model\service\SkillCate::getName($skill['skill_cate_id']);
        $data['userInfo'] = \app\api\model\service\UserInfo::where(['user_id'=>$uid])->field('money,shop_money')->find();
        $data['skill'] = $skill;
        $data['goodsCount'] = count(explode(',',$skill['goods_ids']));
        //今日完成订单
        $data['todayOrderCount'] = \app\api\model\service\Order::where(['skill_id'=>$skill['id'],'status'=>['in',[6,7]],'createtime'=>['between',[strtotime(date('Y-m-d')),time()]]])->count();
        //今日收益
        $data['todayIncome'] = \app\common\model\MoneyLog::where(['user_id'=>$uid,'type'=>'fenyong','createtime'=>['between',[strtotime(date('Y-m-d')),time()]]])->sum('money');
        //今日在线时长
        $data['todayOnlineTime'] = round((time() - $this->auth->today_active)/3600,2);
        //保姆信息
        $data['bmInfo'] = \app\common\model\service\Bm::where('user_id',$uid)->find();

        //待接单数量
        $sjd_order_count = \app\api\model\service\Order::where(['zj_skill_id'=>$skill['id'],'status'=>['in',[1]]])->count();
        //待服务
        $dfw_order_count = \app\api\model\service\Order::where(['skill_id'=>$skill['id'],'status'=>['in',[2,3,4,5]]])->count();
        //服务包订单
        $fwb_order_count = \app\api\model\service\Order::where(['skill_id'=>$skill['id'],'goods_type'=>['in',[1,2]],'status'=>['not in',[0,-1]]])->count();
        //已完成订单数量
        $ywc_order_count = \app\api\model\service\Order::where(['skill_id'=>$skill['id'],'status'=>['in',[6,7]]])->count();
        $data['order_count'] = [
            'sjd_order_count'=>$sjd_order_count,
            'dfw_order_count'=>$dfw_order_count,
            'fwb_order_count'=>$fwb_order_count,
            'today_income'=>$data['todayIncome'],
            'ywc_order_count'=>$ywc_order_count
        ];
        $this->success('服务者信息返回成功',$data);
    }

    /**
     * 设置保姆信息
     * <AUTHOR>
     * @date 2024/8/28  下午4:19
     * @notes
     */
    public function setBm()
    {
        $id = $this->request->post('id');
        $params = $this->request->post();
        $params['user_id'] = $this->auth->id;
        $skill_id = \app\api\model\service\Skill::where(['user_id'=>$this->auth->id])->value('id');
        if(!$skill_id){
            $this->error('服务者信息错误');
        }
        $work = $this->request->post('work/a');
        $work2 = $this->request->post('work2','','urldecode');
        if(isset($params['work'])) unset($params['work']);
        if(isset($params['work2'])) unset($params['work2']);
        if(isset($params['work_time'])) $params['work_time']=strtotime($params['work_time']);
        if(isset($params['cs_time'])) $params['cs_time']=strtotime($params['cs_time']);
        $bm = \app\common\model\service\Bm::where('user_id',$this->auth->id)->find();
        Db::startTrans();
        try{
            if($bm){
                $res = \app\common\model\service\Bm::where('id',$bm->id)->update($params);
//                dump($res);exit;
//                if(!$res){
//                    throw new Exception('信息更新失败');
//                }
                if(isset($params['status']) && $params['status'] == 1){} {
                    \app\api\model\wanlshop\Notice::sendUserMsg($params['user_id'],'审核通知','你的护工资料申请被驳回，请前往查看修改','service_notice',$id,$this->auth->id);
                }

                if(isset($params['status']) && $params['status'] == 3 && $params['user_id']!=$this->auth->id){} {
                    if($bm['status'] !=2 && $params['status'] == 3)
                    \app\api\model\wanlshop\Notice::sendUserMsg($params['user_id'],'审核通知','你的护工资料申请已通过！','service_notice',$id,$this->auth->id);
                }

                $bm_id = $bm->id;
            }else{
                $params['createtime'] = time();
                $res = \app\common\model\service\Bm::create($params);
                if(!$res){
                    throw new Exception('信息更新失败');
                }
                $bm_id = $res->id;
            }
            if(!empty($work2))  $work = json_decode($work2,true);
            if(!empty($work)){
                \app\common\model\service\BmWork::where('bm_id',$bm_id)->delete();
                foreach ($work as $item){
//                    $item['start_time'] = $item['start_time'];
//                    $item['end_time'] = $item['end_time'];
                    $item['bm_id'] = $bm_id;
                    $workItem = \app\common\model\service\BmWork::create($item);
                }
            }
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('提交成功');
    }

    public function skillHome()
    {
        $uid = $this->auth->id;
        $skillId = input('skill_id/d','')?:\app\api\model\service\Skill::where(['user_id'=>$uid])->value('id');
        !$skillId && $this->error('请先入驻为服务者');
        $data['orderCount'] = \app\api\model\service\Order::getSkillOrderCount(['skill_id'=>$skillId,'status'=>['in',[2,3,4,5]],'is_service'=>['in',[0,-1]]]);
        $this->success('信息返回成功',$data);
    }

    /**
     * 服务者获取时间更新时间状态
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function skillTime()
    {
        $uid = $this->auth->id;
        $skillId = input('skill_id/d','');
        $skillId = $skillId?$skillId:\app\api\model\service\Skill::where(['user_id'=>$uid])->value('id');
        !$skillId && $this->error('请先入驻为服务者');
        $timetype = input('timetype/d',0);
        $ids = input('ids','');
        $list = \app\api\model\service\SkillTime::where(['timetype'=>$timetype,'skill_id'=>$skillId])->field('id,timetype,number,state,starttime,endtime')->select();
        $type = input('type','');
        if($type)
        {

            $re = \app\api\model\service\SkillTime::updateTime($ids,$skillId,$type);
            !$re && $this->error('更新失败');
            $this->success('时间状态已更改');
        }
        $this->success('信息返回成功',$list);
    }



    /**
     * 更新服务者部分信息
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function updateSkillInfo()
    {
        $uid = $this->auth->id;
        $skillId = input('skill_id/d','');
        $skill =\app\api\model\service\Skill::where(['user_id'=>$uid])->find();
        $skillId = $skillId?:$skill->id ?? 0;
        $type = input('type','');
        $skill = \app\api\model\service\Skill::where(['id'=>$skillId])->find();
        if($type == 'update')
        {
            $post = input('post.','','trim,strip_tags,htmlspecialchars,xss_clean');
            if(!empty($post['user_image'])){
//                $user_time=$skill->user_image_time ?? strtotime('-32 day');
//                $date =date('Y-m-d',$user_time+86400*30);
//                if(time() - $user_time < 86400*30 ){
//                    $this->error('头像每30天可修改一次，请于'.$date.'再修改');
//                }
                $post['user_image_time'] = time();
            }

            $res=model('app\api\model\service\Skill')->allowField('province,name,user_image,city,district,address,lng,lat,is_rest,percent,hx_range,zd_range,qt_range,health_sc_image,health_name,health_card,health_end,user_image_time')->save($post,['id'=>$skillId]);
            $this->success('信息已更新',\app\api\model\service\Skill::where(['id'=>$skillId])->find());
        }

        $this->success('信息返回成功',$skill);
    }

    public function getSampleSkill()
    {
        $id = input('id/d','');
        $skill = \app\api\model\service\Skill::where(['id'=>$id])->field('id,mobile,code,name,idcard,health_image,certificate_image,image')->find();
        $skill['idcard'] = substr_replace($skill['idcard'],'******',6,6);
        $this->success('信息返回成功',$skill);
    }



}